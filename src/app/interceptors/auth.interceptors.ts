import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth-service.service';
@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private router: Router, private authService: AuthService) {}

  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<any>> {
    console.log('Request URL:', req.url); // Debug log

    const apiReq = req.clone({
      withCredentials: true,
    });

    return next.handle(apiReq).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log('Error in interceptor:', error.status, req.url); // Debug log

        // Don't clear auth for permission checks or user listing
        if (
          error.status === 401 &&
          !req.url.includes('checkpermissions') &&
          !req.url.includes('/user/list')
        ) {
          console.log('Auth error, clearing storage:', req.url);
          localStorage.removeItem('currentUser');
          this.router.navigate(['/login']);
        }
        return throwError(() => error);
      })
    );
  }
}
