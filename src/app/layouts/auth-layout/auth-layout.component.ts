import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-auth-layout',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './auth-layout.component.html',
  styleUrl: './auth-layout.component.scss',
})
export class AuthLayoutComponent {
  ngOnInit() {
    const user = localStorage.getItem('currentUser');
    if (user) {
      this.router.navigate(['/site']);
    }
  }

  constructor(private router: Router) {}
}
