// community-signal.service.ts
import { Injectable, signal } from '@angular/core';
import { Community } from '../interfaces/community.interface';

@Injectable({
  providedIn: 'root',
})
export class CommunitySignalService {
  private communitySignal = signal<Community | null>(null);

  setCommunityToEdit(community: Community) {
    this.communitySignal.set(community);
    console.log(community);
  }

  getCommunityToEdit() {
    return this.communitySignal;
  }
}
