import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  AttendanceRecord,
  AttendanceSubmission,
  AttendanceResponse,
} from '../interfaces/attendance.interface';

@Injectable({
  providedIn: 'root',
})
export class AttendanceService {
  // private baseUrl = 'http://localhost:3000/attendance';
  private baseUrl = 'http://localhost:3000/attendance';

  constructor(private http: HttpClient) {}

  // Mark attendance for a storeng v
  markAttendance(data: AttendanceSubmission): Observable<AttendanceResponse> {
    return this.http.post<AttendanceResponse>(`${this.baseUrl}/mark`, data, {
      withCredentials: true,
    });
  }

  // Get attendance for a specific store and date
  getStoreAttendance(
    storeId: number,
    date: string
  ): Observable<AttendanceResponse> {
    return this.http.get<AttendanceResponse>(
      `${this.baseUrl}/store/${storeId}/date/${date}`,
      { withCredentials: true }
    );
  }

  // Get attendance history for a staff member
  getStaffHistory(
    staffId: number,
    startDate: string,
    endDate: string
  ): Observable<AttendanceResponse> {
    return this.http.get<AttendanceResponse>(
      `${this.baseUrl}/staff/${staffId}/history?start_date=${startDate}&end_date=${endDate}`,
      { withCredentials: true }
    );
  }

  // Get attendance summary for a store
  getStoreSummary(
    storeId: number,
    startDate: string,
    endDate: string
  ): Observable<AttendanceResponse> {
    return this.http.get<AttendanceResponse>(
      `${this.baseUrl}/store/${storeId}/summary?start_date=${startDate}&end_date=${endDate}`,
      { withCredentials: true }
    );
  }
}
