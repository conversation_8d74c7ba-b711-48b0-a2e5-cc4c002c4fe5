import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { User } from '../interfaces/user.interface';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private apiUrl = 'http://localhost:3000';
  private userEndpoint = `${this.apiUrl}/user`;
  private authEndpoint = `${this.apiUrl}/auth`;

  constructor(private http: HttpClient) {}

  // User methods
  getAllUsers(): Observable<any> {
    console.log('Getting users from service');
    return this.http
      .get<{ success: boolean; users: User[] }>(`${this.userEndpoint}/list`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Users response:', response)),
        catchError(this.handleError)
      );
  }

  getUserById(id: number): Observable<any> {
    return this.http
      .get<any>(`${this.userEndpoint}/view/${id}`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Get user response:', response)),
        catchError(this.handleError)
      );
  }

  createUser(userData: Partial<User>): Observable<any> {
    return this.http
      .post<any>(`${this.authEndpoint}/addUser`, userData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Create user response:', response)),
        catchError(this.handleError)
      );
  }

  updateUser(id: number, userData: Partial<User>): Observable<any> {
    return this.http
      .put<any>(`${this.userEndpoint}/update/${id}`, userData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Update user response:', response)),
        catchError(this.handleError)
      );
  }

  deleteUser(id: number): Observable<any> {
    return this.http
      .delete<any>(`${this.userEndpoint}/delete/${id}`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Delete user response:', response)),
        catchError(this.handleError)
      );
  }

  deleteUsers(userIds: number[]): Observable<any> {
    return this.http
      .post<any>(
        `${this.userEndpoint}/delete-multiple`,
        { userIds },
        {
          withCredentials: true,
        }
      )
      .pipe(
        tap((response) => console.log('Delete users response:', response)),
        catchError(this.handleError)
      );
  }

  toggleUserStatus(id: number): Observable<any> {
    return this.http
      .patch<any>(
        `${this.userEndpoint}/toggle-status/${id}`,
        {},
        { withCredentials: true }
      )
      .pipe(
        tap((response) => console.log('Toggle status response:', response)),
        catchError(this.handleError)
      );
  }

  // Role methods
  getRoles(): Observable<any> {
    return this.http
      .get<any>(`${this.authEndpoint}/roles`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Get roles response:', response)),
        catchError(this.handleError)
      );
  }

  addRole(roleData: any): Observable<any> {
    return this.http
      .post<any>(`${this.authEndpoint}/addrole`, roleData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Add role response:', response)),
        catchError(this.handleError)
      );
  }

  updateRole(id: number, roleData: any): Observable<any> {
    return this.http
      .put<any>(`${this.authEndpoint}/roles/${id}`, roleData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Update role response:', response)),
        catchError(this.handleError)
      );
  }

  deleteRole(id: number): Observable<any> {
    return this.http
      .delete<any>(`${this.authEndpoint}/roles/${id}`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Delete role response:', response)),
        catchError(this.handleError)
      );
  }

  private handleError(error: any) {
    console.error('API Error:', error);
    return throwError(() => error);
  }
}
