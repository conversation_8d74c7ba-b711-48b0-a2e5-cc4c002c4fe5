// src/app/services/staff-store.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface StoreStaffMapping {
  id?: number;
  mapping_id?: number;
  staff_id: number;
  store_id: number;
  is_active?: boolean;
  mapping_active?: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  firstName?: string;
  lastName?: string;
  employeeId?: string;
  designation_name?: string;
}

@Injectable({
  providedIn: 'root',
})
export class StaffStoreService {
  private apiUrl = 'http://localhost:3000/master';
  private staffStoreApiUrl = `http://localhost:3000/staff/stores`;

  constructor(private http: HttpClient) {}

  assignStaffToStore(staff_id: number, store_id: number): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/staff-store`,
      {
        staff_id,
        store_id,
      },
      { withCredentials: true }
    );
  }

  bulkAssignStaffToStore(
    mappings: { staff_id: number; store_id: number }[]
  ): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/staff-store/bulk`,
      {
        mappings,
      },
      { withCredentials: true }
    );
  }

  getStoreStaff(store_id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/store/${store_id}/staff`, {
      withCredentials: true,
    });
  }

  getStaffStores(staff_id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/staff/${staff_id}/stores`, {
      withCredentials: true,
    });
  }

  getUnassignedStaff(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/unassigned-staff`, {
      withCredentials: true,
    });
  }

  removeStaffFromStore(staff_id: number, store_id: number): Observable<any> {
    return this.http.delete<any>(
      `${this.apiUrl}/staff-store/${staff_id}/${store_id}`,
      { withCredentials: true }
    );
  }

  updateStaffStoreStatus(
    mapping_id: number,
    is_active: boolean
  ): Observable<any> {
    return this.http.patch<any>(
      `${this.apiUrl}/staff-store/${mapping_id}/status`,
      { is_active },
      { withCredentials: true }
    );
  }

  bulkRemoveStaffFromStore(
    mappingIds: number[]
  ): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(
      `${this.apiUrl}/staff-store/bulk-delete`,
      { mappingIds },
      { withCredentials: true }
    );
  }
}
