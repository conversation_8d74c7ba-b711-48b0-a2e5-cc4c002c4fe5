// src/app/services/candidate-comment.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CandidateCommentService {
  // Update to match your API base URL
  // If you don't have an environment file, replace with your actual API URL
  private baseUrl = 'http://localhost:3000';

  constructor(private http: HttpClient) {}

  // Get all comments for a candidate
  getCandidateComments(candidateId: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/interview/comments/candidate/${candidateId}`,
      {
        withCredentials: true,
      }
    );
  }

  // Get comments for a specific field
  getFieldComments(
    candidateId: number,
    fieldIdentifier: string
  ): Observable<any> {
    // URL encode the field identifier since it might contain spaces
    const encodedField = encodeURIComponent(fieldIdentifier);
    return this.http.get<any>(
      `${this.baseUrl}/interview/comments/candidate/${candidateId}/field/${encodedField}`,
      {
        withCredentials: true,
      }
    );
  }

  // Add or update a comment
  addOrUpdateComment(commentData: any): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/interview/comment`,
      commentData,
      {
        withCredentials: true,
      }
    );
  }

  // Mark a comment as read
  markCommentAsRead(commentId: number): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/interview/comment/${commentId}/read`,
      {},
      {
        withCredentials: true,
      }
    );
  }

  // Delete a comment
  deleteComment(commentId: number): Observable<any> {
    return this.http.delete<any>(
      `${this.baseUrl}/interview/comment/${commentId}`,
      {
        withCredentials: true,
      }
    );
  }
}
