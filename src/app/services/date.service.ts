import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DateService {
  private readonly timeZone = 'Asia/Kolkata';

  formatToIST(date: Date): string {
    const indianDate = date.toLocaleString('en-US', {
      timeZone: this.timeZone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });

    const [month, day, year] = indianDate.split('/');
    return `${year}-${month}-${day}`;
  }

  getCurrentISTDate(): Date {
    return new Date(
      new Date().toLocaleString('en-US', { timeZone: this.timeZone })
    );
  }

  formatDateForDisplay(date: string | Date): string {
    const d = new Date(date);
    return d.toLocaleDateString('en-IN', {
      timeZone: this.timeZone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  }

  // For comparing dates (ignoring time)
  isSameDate(date1: Date, date2: Date): boolean {
    const d1 = this.formatToIST(date1);
    const d2 = this.formatToIST(date2);
    return d1 === d2;
  }

  // For getting start and end of day in IST
  getStartOfDay(date: Date): Date {
    const indianDate = new Date(
      date.toLocaleString('en-US', { timeZone: this.timeZone })
    );
    indianDate.setHours(0, 0, 0, 0);
    return indianDate;
  }

  getEndOfDay(date: Date): Date {
    const indianDate = new Date(
      date.toLocaleString('en-US', { timeZone: this.timeZone })
    );
    indianDate.setHours(23, 59, 59, 999);
    return indianDate;
  }
}
