import { Injectable, signal } from '@angular/core';

// Define the Designation interface within the service file
export interface Designation {
  id: number;
  name: string;
  createdAt?: Date;
  updatedAt?: Date;
  created_by?: number;
  updated_by?: number;
}

@Injectable({
  providedIn: 'root',
})
export class DesignationSignalService {
  private designationSignal = signal<Designation | null>(null);

  setDesignationToEdit(designation: Designation) {
    this.designationSignal.set(designation);
  }

  getDesignationToEdit() {
    return this.designationSignal;
  }
}
