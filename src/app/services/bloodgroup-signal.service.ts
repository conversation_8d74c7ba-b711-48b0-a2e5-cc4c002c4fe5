import { Injectable, signal } from '@angular/core';
import { BloodGroup } from '../interfaces/blood-group.interface';

@Injectable({
  providedIn: 'root',
})
export class BloodGroupSignalService {
  private bloodGroupSignal = signal<BloodGroup | null>(null);

  setBloodGroupToEdit(bloodGroup: BloodGroup) {
    this.bloodGroupSignal.set(bloodGroup);
    console.log(bloodGroup);
  }

  getBloodGroupToEdit() {
    return this.bloodGroupSignal;
  }
}
