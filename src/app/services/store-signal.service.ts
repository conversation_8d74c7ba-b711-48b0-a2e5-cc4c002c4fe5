import { Injectable, signal } from '@angular/core';

export interface Store {
  id: number;
  name: string;
  address: string;
  city_id: number;
  city_name?: string;
  state_id: number;
  state_name?: string;
  postal_code: string;
  phone_number: string;
  email: string;
  opening_hours: string;
  closing_hours: string;
  manager_id: number;
  manager_name?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  created_by: number;
  created_by_username?: string;
  updated_by?: number;
  updated_by_username?: string;
  sl_no?: number;
}

@Injectable({
  providedIn: 'root',
})
export class StoreSignalService {
  private storeSignal = signal<Store | null>(null);

  setStoreToEdit(store: Store) {
    this.storeSignal.set(store);
  }

  getStoreToEdit() {
    return this.storeSignal;
  }

  clearStoreToEdit() {
    this.storeSignal.set(null);
  }
}
