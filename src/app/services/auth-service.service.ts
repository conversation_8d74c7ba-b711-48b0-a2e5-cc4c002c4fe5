import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { User } from '../interfaces/user.interface';

@Injectable({
  providedIn: 'root',
})
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private apiUrl = 'http://localhost:3000/auth';
  private currentUserSubject = new BehaviorSubject<any>(
    this.getUserFromStorage()
  );
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {
    // Initial validation only if there's a stored user
    if (this.getUserFromStorage()) {
      this.validateSession().subscribe({
        next: (response) => {
          if (!response?.success) {
            this.clearSession(); // Clear if validation fails
          }
        },
        error: () => {
          this.clearSession(); // Clear on validation error
        },
      });
    }
  }

  public validateSession(): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/validate-session`, { withCredentials: true })
      .pipe(
        map((response) => {
          if (!response?.success) {
            this.clearSession(); // Clear if server says session is invalid
          }
          return response;
        }),
        catchError((error) => {
          this.clearSession(); // Clear on error (including no session cookie)
          return of(null);
        })
      );
  }

  private clearSession(): void {
    localStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
  }

  login(identifier: string, password: string): Observable<any> {
    return this.http
      .post<any>(
        `${this.apiUrl}/login`,
        { identifier, password },
        { withCredentials: true }
      )
      .pipe(
        tap((response) => {
          if (response.success && response.user) {
            localStorage.setItem('currentUser', JSON.stringify(response.user));
            this.currentUserSubject.next(response.user);
          }
        })
      );
  }

  private getUserFromStorage() {
    try {
      const userStr = localStorage.getItem('currentUser');
      console.log('Getting user from storage:', userStr);
      return userStr ? JSON.parse(userStr) : null;
    } catch (e) {
      console.error('Error parsing stored user:', e);
      return null;
    }
  }

  logout(): Observable<any> {
    return this.http
      .post(`${this.apiUrl}/logout`, {}, { withCredentials: true })
      .pipe(
        tap(() => {
          // Clear local storage
          localStorage.removeItem('currentUser');
          // Clear any other stored auth data
          this.clearAuthData();
        }),
        catchError((error) => {
          console.error('Logout error:', error);
          // Even if the server request fails, clear local data
          localStorage.removeItem('currentUser');
          this.clearAuthData();
          return throwError(() => error);
        })
      );
  }

  private clearAuthData(): void {
    // Clear any additional auth-related data
    localStorage.clear();
    sessionStorage.clear();
  }

  isAuthenticated(): boolean {
    return (
      !!this.currentUserSubject.value && !!localStorage.getItem('currentUser')
    );
  }

  checkPermissions(module: string, action: string): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/checkpermissions`, {
        params: { module, action },
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Permission check response:', response)),
        catchError((error) => {
          console.error('Permission check error:', error);
          return throwError(() => error);
        })
      );
  }

  getCurrentUserValue() {
    return this.currentUserSubject.value;
  }

  getAllUsers(): Observable<any> {
    return this.http
      .get<{ success: boolean; users: User[] }>(`${this.apiUrl}/users`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Get users response:', response)),
        catchError((error) => {
          console.error('Get users error:', error);
          return throwError(() => error);
        })
      );
  }

  addUser(userData: Partial<User>): Observable<any> {
    return this.http
      .post<any>(`${this.apiUrl}/addUser`, userData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Add user response:', response)),
        catchError((error) => {
          console.error('Add user error:', error);
          return throwError(() => error);
        })
      );
  }

  getUserById(userId: number): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/user/${userId}`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Get user by ID response:', response)),
        catchError((error) => {
          console.error('Get user by ID error:', error);
          return throwError(() => error);
        })
      );
  }

  updateUser(userId: number, userData: Partial<User>): Observable<any> {
    return this.http
      .put<any>(`${this.apiUrl}/user/${userId}`, userData, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Update user response:', response)),
        catchError((error) => {
          console.error('Update user error:', error);
          return throwError(() => error);
        })
      );
  }

  deleteUser(userId: number): Observable<any> {
    return this.http
      .delete<any>(`${this.apiUrl}/user/${userId}`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Delete user response:', response)),
        catchError((error) => {
          console.error('Delete user error:', error);
          return throwError(() => error);
        })
      );
  }

  toggleUserStatus(userId: number): Observable<any> {
    return this.http
      .patch<any>(
        `${this.apiUrl}/user/${userId}/toggle-status`,
        {},
        { withCredentials: true }
      )
      .pipe(
        tap((response) =>
          console.log('Toggle user status response:', response)
        ),
        catchError((error) => {
          console.error('Toggle user status error:', error);
          return throwError(() => error);
        })
      );
  }

  getAllRoles(): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/roles`, {
        withCredentials: true,
      })
      .pipe(
        tap((response) => console.log('Get roles response:', response)),
        catchError((error) => {
          console.error('Get roles error:', error);
          return throwError(() => error);
        })
      );
  }
}
