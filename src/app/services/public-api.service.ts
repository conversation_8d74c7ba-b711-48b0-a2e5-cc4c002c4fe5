import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PublicApiService {
  // Change to use a single base URL for consistency
  private baseUrl = 'http://localhost:3000';
  private uploadBaseUrl = `${this.baseUrl}/uploads`;
  private publicUrl = `${this.baseUrl}/public`; // For public API endpoints

  constructor(private http: HttpClient) {}

  // Token validation
  validateToken(token: string): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/validate-token/${token}`);
  }

  // Candidate registration
  registerCandidate(token: string, formData: FormData): Observable<any> {
    return this.http.post<any>(
      `${this.publicUrl}/candidate-registration/${token}`,
      formData
    );
  }

  // Get open positions
  getOpenPositions(): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/open-positions`);
  }

  // Get profile picture URL
  getProfilePictureUrl(filename: string): string {
    if (!filename) return '';
    return `${this.uploadBaseUrl}/${filename}`;
  }

  // Get resume download URL
  getResumeDownloadUrl(resumeFileName: string): string {
    if (!resumeFileName) return '';
    return `${this.uploadBaseUrl}/${resumeFileName}`;
  }

  // IMPORTANT: Change all master data endpoints to use the public API path

  // Get all genders
  getAllGenders(): Observable<any> {
    // Change from api/master/public/genders to public/master/genders
    return this.http.get<any>(`${this.publicUrl}/master/genders`);
  }

  // Get all blood groups
  getAllBloodGroups(): Observable<any> {
    // Change from api/master/public/blood-groups to public/master/blood-groups
    return this.http.get<any>(`${this.publicUrl}/master/blood-groups`);
  }

  // Get all religions
  getAllReligions(): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/master/religions`);
  }

  // Get all communities
  getAllCommunities(): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/master/communities`);
  }

  // Get all states
  getAllStates(): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/master/states`);
  }

  // Get cities by state ID
  getCitiesByStateId(stateId: number): Observable<any> {
    return this.http.get<any>(
      `${this.publicUrl}/master/states/${stateId}/cities`
    );
  }

  // Generic master data fetch method
  getMasterData(type: string): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/master/${type}`);
  }

  // This method can be used to handle any missing master data on the public side
  getPublicMasterData(type: string): Observable<any> {
    return this.http.get<any>(`${this.publicUrl}/master-data/${type}`);
  }
}
