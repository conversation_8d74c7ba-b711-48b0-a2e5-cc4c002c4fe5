import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Staff {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  genderId: number;
  email: string;
  phoneNumber: string;
  emergencyContactName: string;
  emergencyContactNumber: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  employeeId: string;
  hireDate: string;
  departmentId: number;
  department_name?: string;
  designationId: number;
  designation_name?: string;
  employmentTypeId: number;
  employment_type_name?: string;
  educationLevel: string;
  degrees?: string;
  salary: number;
  bloodGroupId: number;
  religion?: string;
  community?: string;
  idProof: string;
  isActive: boolean;
  employmentStatus: 'Active' | 'On Leave' | 'Terminated';
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  sl_no?: number;
}

@Injectable({
  providedIn: 'root',
})
export class StaffService {
  private apiUrl = 'http://localhost:3000/staff';

  constructor(private http: HttpClient) {}

  getAllStaff(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/list`, {
      withCredentials: true,
    });
  }

  getStaffById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/view/${id}`, {
      withCredentials: true,
    });
  }

  // staff.service.ts
  addStaff(staffData: any) {
    // If it's already FormData, send as is
    if (staffData instanceof FormData) {
      return this.http.post<any>(`${this.apiUrl}/create`, staffData, {
        withCredentials: true,
      });
    }

    // If it's JSON data, send as JSON
    return this.http.post<any>(`${this.apiUrl}/create`, staffData);
  }

  deleteStaff(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/delete/${id}`, {
      withCredentials: true,
    });
  }

  deleteStaffs(staffIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/delete-multiple`,
      { staffIds },
      { withCredentials: true }
    );
  }

  toggleStaffStatus(id: number): Observable<any> {
    return this.http.patch<any>(
      `${this.apiUrl}/toggle-status/${id}`,
      {},
      { withCredentials: true }
    );
  }

  // Optional: Add a method for uploading profile pictures if needed
  uploadProfilePicture(staffId: number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('profilePicture', file);
    return this.http.post<any>(
      `${this.apiUrl}/upload-profile-picture/${staffId}`,
      formData,
      { withCredentials: true }
    );
  }
  updateStaff(id: number, formData: FormData): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/update/${id}`, formData, {
      withCredentials: true,
    });
  }

  // staff.service.ts
  getLatestEmployeeId(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/latest-employee-id`, {
      withCredentials: true,
    });
  }
  // Add a separate method for profile picture update if needed
  updateProfilePicture(id: number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('profilePicture', file);
    return this.http.put<any>(
      `${this.apiUrl}/update-profile-picture/${id}`,
      formData,
      {
        withCredentials: true,
      }
    );
  }

  // Sibling management methods
  addSibling(staffId: number, siblingData: any): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/${staffId}/siblings`,
      siblingData,
      { withCredentials: true }
    );
  }

  updateSibling(siblingId: number, siblingData: any): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/siblings/${siblingId}`,
      siblingData,
      { withCredentials: true }
    );
  }

  deleteSibling(siblingId: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/siblings/${siblingId}`, {
      withCredentials: true,
    });
  }
}
