import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class InterviewService {
  private baseUrl = 'http://localhost:3000/interview';
  private resumeBaseUrl = `http://localhost:3000/uploads`;

  constructor(private http: HttpClient) {}

  // Dashboard
  getDashboardStats(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/dashboard/stats`, {
      withCredentials: true,
    });
  }

  getRecentCandidates(limit: number = 5): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/candidates/recent?limit=${limit}`,
      {
        withCredentials: true,
      }
    );
  }

  getUpcomingSessions(limit: number = 5): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/interview-sessions/upcoming?limit=${limit}`,
      {
        withCredentials: true,
      }
    );
  }

  // Position API Calls
  getAllPositions(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/positions`, {
      withCredentials: true,
    });
  }

  getPositionById(id: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/position/${id}`, {
      withCredentials: true,
    });
  }

  addPosition(position: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/position`, position, {
      withCredentials: true,
    });
  }

  updatePosition(id: number, position: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/position/${id}`, position, {
      withCredentials: true,
    });
  }

  deletePosition(id: number): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/position/${id}`, {
      withCredentials: true,
    });
  }

  deletePositions(ids: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/positions/delete`,
      { positionIds: ids },
      {
        withCredentials: true,
      }
    );
  }

  getResumeDownloadUrl(resumeFileName: string): string {
    if (!resumeFileName) {
      return '';
    }
    return `${this.resumeBaseUrl}/${resumeFileName}`;
  }

  togglePositionStatus(id: number): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/position/${id}/toggle-status`,
      {},
      {
        withCredentials: true,
      }
    );
  }

  updateVacancies(id: number, vacancies: number): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/position/${id}/vacancies`,
      { vacancies },
      {
        withCredentials: true,
      }
    );
  }

  getPositionsByDepartment(departmentId: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/positions/department/${departmentId}`,
      {
        withCredentials: true,
      }
    );
  }

  getOpenPositions(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/positions/open`, {
      withCredentials: true,
    });
  }

  // Candidate API Calls
  getAllCandidates(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/candidates`, {
      withCredentials: true,
    });
  }

  getProfilePictureUrl(filename: string): string {
    return `${this.resumeBaseUrl}/${filename}`;
  }

  getCandidateById(id: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/candidate/${id}`, {
      withCredentials: true,
    });
  }

  addCandidate(formData: FormData): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/candidate`, formData, {
      withCredentials: true,
    });
  }

  updateCandidate(id: number, formData: FormData): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/candidate/${id}`, formData, {
      withCredentials: true,
    });
  }

  deleteCandidate(id: number): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/candidate/${id}`, {
      withCredentials: true,
    });
  }

  deleteCandidates(ids: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/candidates/delete`,
      { candidateIds: ids },
      {
        withCredentials: true,
      }
    );
  }

  updateCandidateStatus(id: number, status: string): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/candidate/${id}/status`,
      { status },
      {
        withCredentials: true,
      }
    );
  }

  searchCandidates(searchTerms: any): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/candidates/search`,
      searchTerms,
      {
        withCredentials: true,
      }
    );
  }

  // Sibling Management
  addCandidateSibling(candidateId: number, siblingData: any): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/candidate/${candidateId}/siblings`,
      siblingData,
      {
        withCredentials: true,
      }
    );
  }

  getCandidateSiblings(candidateId: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/candidate/${candidateId}/siblings`,
      {
        withCredentials: true,
      }
    );
  }

  updateCandidateSibling(id: number, siblingData: any): Observable<any> {
    return this.http.put<any>(
      `${this.baseUrl}/candidate-sibling/${id}`,
      siblingData,
      {
        withCredentials: true,
      }
    );
  }

  deleteCandidateSibling(id: number): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/candidate-sibling/${id}`, {
      withCredentials: true,
    });
  }

  // Candidate Position Mapping
  assignCandidateToPosition(mapping: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/candidate-position`, mapping, {
      withCredentials: true,
    });
  }

  getCandidatePositions(candidateId: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/candidate/${candidateId}/positions`,
      {
        withCredentials: true,
      }
    );
  }

  getPositionCandidates(positionId: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/position/${positionId}/candidates`,
      {
        withCredentials: true,
      }
    );
  }

  updateCandidatePositionStatus(
    candidateId: number,
    positionId: number,
    status: string
  ): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/candidate/${candidateId}/position/${positionId}/status`,
      { status },
      { withCredentials: true }
    );
  }

  removeCandidateFromPosition(
    candidateId: number,
    positionId: number
  ): Observable<any> {
    return this.http.delete<any>(
      `${this.baseUrl}/candidate/${candidateId}/position/${positionId}`,
      {
        withCredentials: true,
      }
    );
  }

  // Interview Session API Calls
  getAllInterviewSessions(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/interview-sessions`, {
      withCredentials: true,
    });
  }

  getInterviewSessions(
    candidateId: number,
    positionId: number
  ): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/interview-sessions/${candidateId}/${positionId}`,
      {
        withCredentials: true,
      }
    );
  }

  getInterviewSessionById(id: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/interview-session/${id}`, {
      withCredentials: true,
    });
  }

  addInterviewSession(sessionData: any): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/interview-session`,
      sessionData,
      {
        withCredentials: true,
      }
    );
  }

  updateInterviewSession(id: number, session: any): Observable<any> {
    return this.http.put<any>(
      `${this.baseUrl}/interview-session/${id}`,
      session,
      {
        withCredentials: true,
      }
    );
  }

  deleteInterviewSession(id: number): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/interview-session/${id}`, {
      withCredentials: true,
    });
  }

  // Candidate Decision API Calls
  getCandidateDecision(
    candidateId: number,
    positionId: number
  ): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/candidate-decision/${candidateId}/${positionId}`,
      {
        withCredentials: true,
      }
    );
  }

  addCandidateDecision(decision: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/candidate-decision`, decision, {
      withCredentials: true,
    });
  }

  updateCandidateDecision(id: number, decision: any): Observable<any> {
    return this.http.put<any>(
      `${this.baseUrl}/candidate-decision/${id}`,
      decision,
      {
        withCredentials: true,
      }
    );
  }

  setStaffForCandidate(decisionId: number, staffId: number): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/candidate-decision/${decisionId}/staff`,
      { staffId },
      {
        withCredentials: true,
      }
    );
  }

  // Registration Token API Calls
  getAllRegistrationTokens(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/registration-tokens`, {
      withCredentials: true,
    });
  }

  getMyRegistrationTokens(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/my-registration-tokens`, {
      withCredentials: true,
    });
  }

  generateRegistrationToken(tokenData: any): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}/registration-token`,
      tokenData,
      {
        withCredentials: true,
      }
    );
  }

  invalidateToken(token: string): Observable<any> {
    return this.http.patch<any>(
      `${this.baseUrl}/registration-token/${token}/invalidate`,
      {},
      {
        withCredentials: true,
      }
    );
  }

  deleteToken(id: number): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/registration-token/${id}`, {
      withCredentials: true,
    });
  }
}
