import { Injectable, signal } from '@angular/core';
import { City } from '../interfaces/city.interface';

@Injectable({
  providedIn: 'root',
})
export class CitySignalService {
  private citySignal = signal<City | null>(null);
  constructor() {}

  setCityToEdit(city: City) {
    this.citySignal.set(city);
    console.log(city);
  }

  getCityToEdit() {
    console.log(this.citySignal);
    return this.citySignal;
  }
}
