import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { State } from '../interfaces/state.interface';
import {
  Store,
  StoreCreateDTO,
  StoreUpdateDTO,
  StoreValidationError,
} from '../interfaces/store.interface';

export interface StaffResponse {
  success: boolean;
  staff: any[]; // Using any to match existing code
  message?: string;
}

@Injectable({
  providedIn: 'root',
})
export class MasterserviceService {
  private apiUrl = 'http://localhost:3000/master';
  private staffUrl = 'http://localhost:3000/staff';

  constructor(private http: HttpClient) {}

  // Add this method to the MasterserviceService class
  getStoreStaff(storeId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/store/${storeId}/staff`, {
      withCredentials: true,
    });
  }

  // In masterservice.service.ts
  getAllStaff(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/staff`, {
      withCredentials: true,
    });
  }

  getAllCities() {
    return this.http.get<any>(`${this.apiUrl}/cities`, {
      withCredentials: true,
    });
  }

  getCitiesByStateId(stateId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/cities/state/${stateId}`, {
      withCredentials: true,
    });
  }

  getAState(stateId: number) {
    return this.http.get<any>(`${this.apiUrl}/state/` + stateId, {
      withCredentials: true,
    });
  }

  getCityById(cityId: number) {
    return this.http.get<any>(`${this.apiUrl}/city/` + cityId, {
      withCredentials: true,
    });
  }

  getAllStates() {
    return this.http.get<any>(`${this.apiUrl}/states`, {
      withCredentials: true,
    });
  }

  updateCity(id: number, cityData: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/city/${id}`, cityData, {
      withCredentials: true,
    });
  }

  updateState(id: number, stateData: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/state/${id}`, stateData, {
      withCredentials: true,
    });
  }

  addCity(city: any) {
    return this.http.post<any>(`${this.apiUrl}/city`, city, {
      withCredentials: true,
    });
  }

  deleteCities(cityIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteCities`,
      { cityIds },
      { withCredentials: true }
    );
  }

  //State Services

  addState(state: State) {
    return this.http.post<any>(`${this.apiUrl}/state`, state, {
      withCredentials: true,
    });
  }

  deleteStates(stateIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteStates`,
      { stateIds },
      { withCredentials: true }
    );
  }

  //Blood Group Services

  getAllBloodGroups() {
    return this.http.get<any>(`${this.apiUrl}/bloodgroups`, {
      withCredentials: true,
    });
  }

  addBloodGroup(bloodgroup: any) {
    return this.http.post<any>(`${this.apiUrl}/bloodgroup`, bloodgroup, {
      withCredentials: true,
    });
  }

  deleteBloodGroups(bloodGroupIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteBloodGroups`,
      { bloodGroupIds },
      { withCredentials: true }
    );
  }

  updateBloodGroup(id: number, bloodGroupData: any): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/bloodgroup/${id}`,
      bloodGroupData,
      { withCredentials: true }
    );
  }

  // Designation methods
  getAllDesignations(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/designations`, {
      withCredentials: true,
    });
  }

  getDesignationById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/designation/${id}`, {
      withCredentials: true,
    });
  }

  addDesignation(designationData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/designation`, designationData, {
      withCredentials: true,
    });
  }

  updateDesignation(id: number, designationData: any): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/designation/${id}`,
      designationData,
      { withCredentials: true }
    );
  }

  deleteDesignation(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/designation/${id}`, {
      withCredentials: true,
    });
  }

  deleteDesignations(designationIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteDesignations`,
      { designationIds },
      { withCredentials: true }
    );
  }

  //Religion Methods

  addReligion(religionData: { name: string }): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/religion`, religionData, {
      withCredentials: true,
    });
  }

  getAllReligions(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/religions`, {
      withCredentials: true,
    });
  }

  getReligionById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/religion/${id}`, {
      withCredentials: true,
    });
  }

  updateReligion(id: number, religionData: { name: string }): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/religion/${id}`, religionData, {
      withCredentials: true,
    });
  }

  deleteReligion(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/religion/${id}`, {
      withCredentials: true,
    });
  }

  deleteReligions(religionIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteReligions`,
      { religionIds },
      { withCredentials: true }
    );
  }

  // Community methods
  addCommunity(communityData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/community`, communityData, {
      withCredentials: true,
    });
  }

  getAllCommunities(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/communities`, {
      withCredentials: true,
    });
  }

  getCommunityById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/community/${id}`, {
      withCredentials: true,
    });
  }

  updateCommunity(id: number, communityData: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/community/${id}`, communityData, {
      withCredentials: true,
    });
  }

  deleteCommunity(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/community/${id}`, {
      withCredentials: true,
    });
  }

  deleteCommunities(communityIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteCommunities`,
      { communityIds },
      { withCredentials: true }
    );
  }

  // Department methods
  getAllDepartments(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/departments`, {
      withCredentials: true,
    });
  }

  getDepartmentById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/department/${id}`, {
      withCredentials: true,
    });
  }

  addDepartment(departmentData: {
    name: string;
    is_active: boolean;
  }): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/department`, departmentData, {
      withCredentials: true,
    });
  }

  updateDepartment(id: number, departmentData: any): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/department/${id}`,
      departmentData,
      {
        withCredentials: true,
      }
    );
  }

  deleteDepartment(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/department/${id}`, {
      withCredentials: true,
    });
  }

  deleteDepartments(departmentIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteDepartments`,
      { departmentIds },
      { withCredentials: true }
    );
  }

  toggleDepartmentStatus(id: number): Observable<any> {
    return this.http.patch<any>(
      `${this.apiUrl}/department/${id}/toggle-status`,
      {},
      {
        withCredentials: true,
      }
    );
  }

  getAllEmploymentTypes(): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/employmentTypes`, {
        withCredentials: true,
      })
      .pipe(
        map((response: { success: any; employmentTypes: any[] }) => {
          if (response.success && response.employmentTypes) {
            response.employmentTypes = response.employmentTypes.map(
              (et: any) => ({
                ...et,
                is_active: Boolean(et.is_active), // Ensure boolean conversion
              })
            );
          }
          return response;
        })
      );
  }

  getEmploymentTypeById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/employmentType/${id}`, {
      withCredentials: true,
    });
  }

  addEmploymentType(employmentTypeData: any): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/employmentType`,
      employmentTypeData,
      {
        withCredentials: true,
      }
    );
  }

  updateEmploymentType(id: number, employmentTypeData: any): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/employmentType/${id}`,
      employmentTypeData,
      { withCredentials: true }
    );
  }

  deleteEmploymentType(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/employmentType/${id}`, {
      withCredentials: true,
    });
  }

  deleteEmploymentTypes(employmentTypeIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteEmploymentTypes`,
      { employmentTypeIds },
      { withCredentials: true }
    );
  }

  toggleEmploymentTypeStatus(id: number): Observable<any> {
    return this.http.patch<any>(
      `${this.apiUrl}/employmentType/${id}/toggle-status`,
      {},
      {
        withCredentials: true,
      }
    );
  }

  // Gender methods
  getAllGenders(): Observable<any> {
    return this.http
      .get<any>(`${this.apiUrl}/genders`, {
        withCredentials: true,
      })
      .pipe(
        map((response: { success: boolean; genders: any[] }) => {
          if (response.success && response.genders) {
            response.genders = response.genders.map((gender) => ({
              ...gender,
              is_active: Boolean(gender.is_active), // Ensure boolean conversion
            }));
          }
          return response;
        })
      );
  }

  getGenderById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/gender/${id}`, {
      withCredentials: true,
    });
  }

  addGender(genderData: { name: string; is_active: boolean }): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/gender`, genderData, {
      withCredentials: true,
    });
  }

  updateGender(id: number, genderData: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/gender/${id}`, genderData, {
      withCredentials: true,
    });
  }

  deleteGender(id: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/gender/${id}`, {
      withCredentials: true,
    });
  }

  deleteGenders(genderIds: number[]): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/deleteGenders`,
      { genderIds },
      { withCredentials: true }
    );
  }

  toggleGenderStatus(id: number): Observable<any> {
    return this.http.patch<any>(
      `${this.apiUrl}/gender/${id}/toggle-status`,
      {},
      { withCredentials: true }
    );
  }

  // Updated store methods with proper typing
  getAllStores(): Observable<{ success: boolean; stores: Store[] }> {
    return this.http
      .get<{ success: boolean; stores: Store[] }>(`${this.apiUrl}/stores`, {
        withCredentials: true,
      })
      .pipe(
        map((response) => ({
          ...response,
          stores: response.stores.map((store) => ({
            ...store,
            is_active: Boolean(store.is_active),
          })),
        }))
      );
  }

  getStoreById(id: number): Observable<{ success: boolean; store: Store }> {
    return this.http.get<{ success: boolean; store: Store }>(
      `${this.apiUrl}/store/${id}`,
      { withCredentials: true }
    );
  }

  addStore(storeData: StoreCreateDTO): Observable<{
    success: boolean;
    message: string;
    storeId?: number;
  }> {
    return this.http.post<{
      success: boolean;
      message: string;
      storeId?: number;
    }>(`${this.apiUrl}/store`, this.formatStoreDataForSubmission(storeData), {
      withCredentials: true,
    });
  }

  updateStore(
    id: number,
    storeData: StoreUpdateDTO
  ): Observable<{
    success: boolean;
    message: string;
  }> {
    return this.http.put<{ success: boolean; message: string }>(
      `${this.apiUrl}/store/${id}`,
      this.formatStoreDataForSubmission(storeData),
      { withCredentials: true }
    );
  }

  deleteStore(id: number): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(
      `${this.apiUrl}/store/${id}`,
      { withCredentials: true }
    );
  }

  deleteStores(
    storeIds: number[]
  ): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(
      `${this.apiUrl}/deleteStores`,
      { storeIds },
      { withCredentials: true }
    );
  }

  toggleStoreStatus(
    id: number
  ): Observable<{ success: boolean; message: string }> {
    return this.http.patch<{ success: boolean; message: string }>(
      `${this.apiUrl}/store/${id}/toggle-status`,
      {},
      { withCredentials: true }
    );
  }

  getAllManagers(): Observable<any> {
    return this.http.get(`${this.apiUrl}/managers`, { withCredentials: true });
  }

  // Helper methods with proper typing
  validateStoreData(
    storeData: Partial<StoreCreateDTO>
  ): StoreValidationError[] {
    const errors: StoreValidationError[] = [];

    if (!storeData.name?.trim()) {
      errors.push({ field: 'name', message: 'Store name is required' });
    }
    if (!storeData.address?.trim()) {
      errors.push({ field: 'address', message: 'Store address is required' });
    }
    if (!storeData.city_id) {
      errors.push({ field: 'city_id', message: 'City selection is required' });
    }
    if (!storeData.state_id) {
      errors.push({
        field: 'state_id',
        message: 'State selection is required',
      });
    }
    if (
      storeData.phone_number &&
      !/^\d{10}$/.test(storeData.phone_number.replace(/[- ]/g, ''))
    ) {
      errors.push({
        field: 'phone_number',
        message: 'Invalid phone number format',
      });
    }
    if (
      storeData.email &&
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(storeData.email)
    ) {
      errors.push({ field: 'email', message: 'Invalid email format' });
    }
    if (storeData.postal_code && !/^\d{6}$/.test(storeData.postal_code)) {
      errors.push({
        field: 'postal_code',
        message: 'Invalid postal code format',
      });
    }

    return errors;
  }

  private formatStoreDataForSubmission(
    storeData: StoreCreateDTO | StoreUpdateDTO
  ): any {
    return {
      ...storeData,
      opening_hours: this.formatStoreHours(storeData.opening_hours),
      closing_hours: this.formatStoreHours(storeData.closing_hours),
      is_active: storeData.is_active ?? true,
      phone_number: storeData.phone_number?.replace(/[- ]/g, ''),
      postal_code: storeData.postal_code?.toString().trim(),
    };
  }

  private formatStoreHours(time?: string): string {
    if (!time) return '';
    try {
      const [hours, minutes] = time.split(':');
      return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:00`;
    } catch (e) {
      return time;
    }
  }
}
