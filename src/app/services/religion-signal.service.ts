// services/religion-signal.service.ts
import { Injectable, signal } from '@angular/core';
import { Religion } from '../interfaces/religion.interface';

@Injectable({
  providedIn: 'root',
})
export class ReligionSignalService {
  private religionSignal = signal<Religion | null>(null);

  setReligionToEdit(religion: Religion) {
    this.religionSignal.set(religion);
  }

  getReligionToEdit() {
    return this.religionSignal;
  }
}
