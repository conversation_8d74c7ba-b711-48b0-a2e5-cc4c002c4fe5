import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  private apiUrl = 'http://localhost:3000/auth';

  constructor(private http: HttpClient) {}

  checkPermissions(module: string, action: string): Observable<boolean> {
    console.log(`Checking permissions for ${module}:${action}`);

    return this.http
      .get<{ success: boolean; hasPermission: boolean }>(
        `${this.apiUrl}/checkpermissions`,
        {
          params: { module, action },
          withCredentials: true,
        }
      )
      .pipe(
        map((response) => {
          console.log('Permission response:', response);
          return response.hasPermission;
        }),
        catchError((error) => {
          console.error('Permission check error:', error);
          return of(false);
        })
      );
  }
}
