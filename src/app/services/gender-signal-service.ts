import { Injectable, signal } from '@angular/core';

export interface Gender {
  id: number;
  name: string;
  is_active: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  created_by?: number;
  updated_by?: number;
}

@Injectable({
  providedIn: 'root',
})
export class GenderSignalService {
  private genderSignal = signal<Gender | null>(null);

  setGenderToEdit(gender: Gender) {
    this.genderSignal.set(gender);
  }

  getGenderToEdit() {
    return this.genderSignal;
  }

  clearGenderToEdit() {
    this.genderSignal.set(null);
  }
}
