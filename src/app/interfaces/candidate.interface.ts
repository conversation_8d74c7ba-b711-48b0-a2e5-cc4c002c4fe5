export interface Candidate {
  id?: number;
  first_name: string;
  middle_name?: string;
  last_name: string;
  date_of_birth?: string;
  gender_id?: number;
  gender_name?: string;
  email: string;
  mobile_number: string;
  alternate_phone?: string;
  current_address?: string;
  permanent_address?: string;
  id_proof_type?: string;
  id_proof_number?: string;

  total_experience?: number;
  current_organization?: string;
  current_designation?: string;
  current_salary?: number;
  expected_salary?: number;
  notice_period?: number;
  reason_for_change?: string;
  skills?: string;

  highest_qualification?: string;
  university?: string;
  year_of_passing?: number;
  specialization?: string;
  additional_certifications?: string;

  reference_contacts?: string;

  resume_file?: string;
  portfolio_links?: string;

  status: 'New' | 'In Process' | 'Selected' | 'Rejected';
  created_at?: string;
  updated_at?: string;
  created_by: number;
  created_by_username?: string;
  updated_by?: number;
  updated_by_username?: string;
  position_count?: number;
  interview_count?: number;
}
