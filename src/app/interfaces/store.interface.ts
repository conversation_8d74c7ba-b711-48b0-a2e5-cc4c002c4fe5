// src/app/interfaces/store.interface.ts

export interface Store {
  id: number;
  name: string;
  address: string;
  city_id: number;
  city_name?: string;
  state_id: number;
  state_name?: string;
  postal_code: string;
  phone_number: string;
  email: string;
  opening_hours: string;
  closing_hours: string;
  manager_id: number;
  manager_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: number;
  created_by_username?: string;
  updated_by?: number;
  updated_by_username?: string;
  sl_no?: number;
}

// You might also want to add a StoreCreateDTO for creation payloads
export interface StoreCreateDTO {
  name: string;
  address: string;
  city_id: number;
  state_id: number;
  postal_code: string;
  phone_number: string;
  email: string;
  opening_hours: string;
  closing_hours: string;
  manager_id: number;
  is_active?: boolean;
}

// And a StoreUpdateDTO for update payloads
export interface StoreUpdateDTO extends Partial<StoreCreateDTO> {
  updated_by?: number;
}

// Optional: Add validation types if needed
export interface StoreValidationError {
  field: keyof Store;
  message: string;
}

export interface StoreResponse {
  success: boolean;
  stores: Store[];
  message?: string;
}

export interface StoreActionResponse {
  success: boolean;
  message: string;
}
