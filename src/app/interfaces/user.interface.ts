export interface User {
  id: number;
  username: string;
  password?: string; // Optional since we don't always need it
  role_id: number;
  mobile_number: string;
  email: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  // Additional properties for display
  role_name?: string;
  created_by_username?: string;
  updated_by_username?: string;
}

// You might also want a simpler interface for creating new users
export interface CreateUserDto {
  username: string;
  password: string;
  role_id: number;
  mobile_number: string;
  email: string;
  is_active: boolean;
}
