export interface InterviewSession {
  id?: number;
  candidate_id: number;
  position_id: number;
  round_title: string;
  interview_date: string;
  interview_mode: 'In-person' | 'Video' | 'Phone';
  interviewer_id: number;
  interviewer_name?: string;
  comments?: string;
  outcome: 'Move to next round' | 'Reject' | 'Hold' | 'Select';
  created_at?: string;
  updated_at?: string;
  // Additional properties for display
  first_name?: string;
  last_name?: string;
  position_title?: string;
}
