export interface AttendanceRecord {
  id?: number;
  staff_id: number;
  store_id: number;
  date: string;
  status: 'present' | 'absent';
  absence_reason?: string;
  firstName?: string;
  lastName?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface AttendanceSubmission {
  store_id: number;
  date: string;
  attendance_records: {
    staff_id: number;
    status: 'present' | 'absent';
    absence_reason?: string;
  }[];
}

export interface AttendanceSummary {
  date: string;
  present_count: number;
  absent_count: number;
}

export interface AttendanceResponse {
  success: boolean;
  message?: string;
  attendance?: AttendanceRecord[];
  summary?: AttendanceSummary[];
}
