export interface CandidatePosition {
  id?: number;
  candidate_id: number;
  position_id: number;
  application_date?: string;
  status: 'Applied' | 'In Process' | 'Selected' | 'Rejected';
  created_at?: string;
  updated_at?: string;
  created_by: number;
  created_by_username?: string;
  // Additional properties for display
  position_title?: string;
  department_id?: number;
  department_name?: string;
  position_status?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  mobile_number?: string;
  total_experience?: number;
  current_organization?: string;
  expected_salary?: number;
  resume_file?: string;
  candidate_status?: string;
  interview_count?: number;
}
