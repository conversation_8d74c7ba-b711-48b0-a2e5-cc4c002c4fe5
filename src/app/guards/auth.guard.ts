import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AuthService } from '../services/auth-service.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> {
    console.log('AuthGuard: Checking session...');

    return this.authService.validateSession().pipe(
      map((response) => {
        if (response && response.success) {
          return true;
        }

        // Simply redirect to login without query params
        return this.router.createUrlTree(['/login']);
      }),
      catchError((error) => {
        console.error('AuthGuard: Session validation error:', error);
        return of(this.router.createUrlTree(['/login']));
      })
    );
  }
}
