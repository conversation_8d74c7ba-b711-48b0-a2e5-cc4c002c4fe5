import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth-service.service';
import { PermissionService } from '../services/permission.service';

@Injectable({
  providedIn: 'root',
})
export class PermissionGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private permissionService: PermissionService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    console.log('Current user:', this.authService.getCurrentUserValue());
    console.log('LocalStorage:', localStorage.getItem('currentUser'));

    if (!this.authService.isAuthenticated()) {
      console.log('User not authenticated, redirecting to login');
      return of(this.router.createUrlTree(['/login']));
    }

    const requiredModule = route.data['requiredModule'] as string;
    const requiredAction = route.data['requiredAction'] as string;

    console.log(`Checking permissions for ${requiredModule}:${requiredAction}`);

    return this.permissionService
      .checkPermissions(requiredModule, requiredAction)
      .pipe(
        map((hasPermission) => {
          if (hasPermission) {
            console.log('Permission granted');
            return true;
          }
          console.log('Permission denied');
          return this.router.createUrlTree(['/unauthorized']);
        }),
        catchError((error) => {
          console.error('Permission check failed:', error);
          return of(this.router.createUrlTree(['/error']));
        })
      );
  }
}
