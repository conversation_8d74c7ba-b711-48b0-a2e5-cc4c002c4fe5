import { Routes } from '@angular/router';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { StoresComponent } from './pages/master/stores/stores.component';
import { LoginComponent } from './login/login.component';
import { DashLayoutComponent } from './layouts/dash-layout/dash-layout.component';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { CityComponent } from './pages/master/city/city.component';
import { StateComponent } from './pages/master/state/state.component';
import { AddCityComponent } from './pages/master/city/addcity/addcity.component';
import { ListCitiesComponent } from './pages/master/city/listcities/listcities.component';
import { PermissionGuard } from './guards/permissionguard';
import { ListstateComponent } from './pages/master/state/liststate/liststate.component';
import { AddstateComponent } from './pages/master/state/addstate/addstate.component';
import { EditstateComponent } from './pages/master/state/editstate/editstate.component';
import { BloodgroupComponent } from './pages/master/bloodgroup/bloodgroup.component';
import { AddbloodgroupComponent } from './pages/master/bloodgroup/addbloodgroup/addbloodgroup.component';
import { EditbloodgroupComponent } from './pages/master/bloodgroup/editbloodgroup/editbloodgroup.component';
import { ListBloodGroupsComponent } from './pages/master/bloodgroup/listbloodgroup/listbloodgroup.component';
import { DesignationsComponent } from './pages/master/designations/designations.component';
import { ListDesignationsComponent } from './pages/master/designations/listdesignations/listdesignations.component';
import { AdddesignationsComponent } from './pages/master/designations/adddesignations/adddesignations.component';
import { EditdesignationsComponent } from './pages/master/designations/editdesignations/editdesignations.component';
import { CommunityComponent } from './pages/master/community/community.component';
import { AddCommunityComponent } from './pages/master/community/addcommunity/addcommunity.component';
import { EditcommunityComponent } from './pages/master/community/editcommunity/editcommunity.component';
import { ListCommunitiesComponent } from './pages/master/community/list-community/list-community.component';
import { DepartmentComponent } from './pages/master/department/department.component';
import { ListDepartmentsComponent } from './pages/master/department/list-departments/list-departments.component';
import { AddDepartmentComponent } from './pages/master/department/add-department/add-department.component';
import { EditDepartmentComponent } from './pages/master/department/edit-department/edit-department.component';
import { EditcityComponent } from './pages/master/city/editcity/editcity.component';
import { EmploymenttypeComponent } from './pages/master/employmenttype/employmenttype.component';
import { ListEmploymentTypeComponent } from './pages/master/employmenttype/list-employment-type/list-employment-type.component';
import { AddEmploymentTypeComponent } from './pages/master/employmenttype/add-employment-type/add-employment-type.component';
import { EditEmploymentTypeComponent } from './pages/master/employmenttype/edit-employment-type/edit-employment-type.component';
import { GenderComponent } from './pages/master/gender/gender.component';
import { ListGendersComponent } from './pages/master/gender/list-gender/list-gender.component';
import { AddGenderComponent } from './pages/master/gender/add-gender/add-gender.component';
import { EditGenderComponent } from './pages/master/gender/edit-gender/edit-gender.component';
import { AddStaffComponent } from './pages/staff/add-staff/add-staff.component';
import { StaffComponent } from './pages/staff/staff.component';
import { ListStaffComponent } from './pages/staff/list-staff/list-staff.component';
import { AddStoreComponent } from './pages/master/stores/add-store/add-store.component';
import { ListStoresComponent } from './pages/master/stores/list-stores/list-stores.component';
import { ManageStoreStaffComponent } from './pages/staff/manage-store-staff/manage-store-staff.component';
import { AttendanceComponent } from './pages/attendance/attendance.component';
import { AddAttendanceComponent } from './pages/attendance/add-attendance/add-attendance.component';
import { ListAttendanceComponent } from './pages/attendance/list-attendance/list-attendance.component';
import { AuthGuard } from './guards/auth.guard';
import { UserComponent } from './pages/user/user.component';
import { ListUsersComponent } from './pages/user/list-users/list-users.component';
import { AddUserComponent } from './pages/user/add-user/add-user.component';
import { EditUserComponent } from './pages/user/edit-user/edit-user.component';
import { EditStaffComponent } from './pages/staff/edit-staff/edit-staff.component';
import { ReligionComponent } from './pages/master/religion/religion.component';
import { ListReligionsComponent } from './pages/master/religion/list-religions/list-religions.component';
import { AddReligionComponent } from './pages/master/religion/add-religion/add-religion.component';
import { EditReligionComponent } from './pages/master/religion/edit-religion/edit-religion.component';
import { EditStoreComponent } from './pages/master/stores/edit-store/edit-store.component';
import { InterviewComponent } from './pages/interview/interview.component';
import { ListPositionsComponent } from './pages/interview/positions/list-positions/list-positions.component';
import { AddPositionComponent } from './pages/interview/positions/add-position/add-position.component';
import { EditPositionComponent } from './pages/interview/positions/edit-position/edit-position.component';
import { ListCandidatesComponent } from './pages/interview/candidates/list-candidates/list-candidates.component';
import { AddCandidateComponent } from './pages/interview/candidates/add-candidate/add-candidate.component';
import { EditCandidateComponent } from './pages/interview/candidates/edit-candidate/edit-candidate.component';
import { ListInterviewSessionsComponent } from './pages/interview/sessions/list-interview-sessions/list-interview-sessions.component';
import { AddInterviewSessionComponent } from './pages/interview/sessions/add-interview-session/add-interview-session.component';
import { CandidateDecisionComponent } from './pages/interview/decisions/candidate-decision/candidate-decision.component';
import { InterviewDashboardComponent } from './pages/interview/dashboard/dashboard.component';
import { ViewPositionComponent } from './pages/interview/positions/view-position/view-position.component';
import { ViewCandidateComponent } from './pages/interview/candidates/view-candidate/view-candidate.component';
import { EditInterviewSessionComponent } from './pages/interview/sessions/edit-interview-session/edit-interview-session.component';
import { ViewInterviewSessionComponent } from './pages/interview/sessions/view-interview-session/view-interview-session.component';
import { AllInterviewSessionsComponent } from './pages/interview/sessions/all-interview-sessions/all-interview-sessions.component';
// import { AddGenderComponent } from './pages/master/gender/add-gender/add-gender.component';
// import { EditGenderComponent } from './pages/master/gender/edit-gender/edit-gender.component';

export const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: 'login' },
  {
    path: 'site',
    component: DashLayoutComponent,
    canActivate: [AuthGuard],
    data: { requiredModule: 'authModule', requiredAction: 'read' },
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },

      //user routes

      {
        path: 'users',
        component: UserComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'userModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListUsersComponent },
          {
            path: 'add',
            component: AddUserComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'userModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditUserComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'userModule', requiredAction: 'write' },
          },
        ],
      },

      // Stores routes
      {
        path: 'master/stores',
        component: StoresComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListStoresComponent },
          {
            path: 'add',
            component: AddStoreComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditStoreComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // City routes
      {
        path: 'master/city',
        component: CityComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListCitiesComponent },
          {
            path: 'addcity',
            component: AddCityComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'editcity',
            component: EditcityComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // State routes
      {
        path: 'master/state',
        component: StateComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListstateComponent },
          {
            path: 'addstate',
            component: AddstateComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'editstate',
            component: EditstateComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Blood Group routes
      {
        path: 'master/bloodgroup',
        component: BloodgroupComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListBloodGroupsComponent },
          {
            path: 'add',
            component: AddbloodgroupComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit',
            component: EditbloodgroupComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Designations routes
      {
        path: 'master/designations',
        component: DesignationsComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListDesignationsComponent },
          {
            path: 'add',
            component: AdddesignationsComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit',
            component: EditdesignationsComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      {
        path: 'master/religion',
        component: ReligionComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListReligionsComponent },
          {
            path: 'add',
            component: AddReligionComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditReligionComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Community routes
      {
        path: 'master/community',
        component: CommunityComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListCommunitiesComponent },
          {
            path: 'add',
            component: AddCommunityComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit',
            component: EditcommunityComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Department routes
      {
        path: 'master/department',
        component: DepartmentComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListDepartmentsComponent },
          {
            path: 'add',
            component: AddDepartmentComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditDepartmentComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Employment Type routes
      {
        path: 'master/employmenttype',
        component: EmploymenttypeComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListEmploymentTypeComponent },
          {
            path: 'add',
            component: AddEmploymentTypeComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditEmploymentTypeComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Gender routes
      {
        path: 'master/gender',
        component: GenderComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListGendersComponent },
          {
            path: 'add',
            component: AddGenderComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id',
            component: EditGenderComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
        ],
      },

      // Staff routes
      {
        path: 'staffs',
        component: StaffComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'list', pathMatch: 'full' },
          { path: 'list', component: ListStaffComponent },
          {
            path: 'add',
            component: AddStaffComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
          {
            path: 'edit/:id', // Add this route for edit functionality
            component: EditStaffComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'update' },
          },
          {
            path: 'stores/:id',
            component: ManageStoreStaffComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
        ],
      },

      // Attendance routes
      {
        path: 'attendance',
        component: AttendanceComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'masterModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'add', pathMatch: 'full' },
          { path: 'list', component: ListAttendanceComponent },
          {
            path: 'add',
            component: AddAttendanceComponent,
            canActivate: [PermissionGuard],
            data: { requiredModule: 'masterModule', requiredAction: 'write' },
          },
        ],
      },

      {
        path: 'interview',
        component: InterviewComponent,
        canActivate: [PermissionGuard],
        data: { requiredModule: 'interviewModule', requiredAction: 'read' },
        children: [
          { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
          { path: 'dashboard', component: InterviewDashboardComponent },

          // Positions routes
          { path: 'positions', component: ListPositionsComponent },
          { path: 'positions/add', component: AddPositionComponent },
          { path: 'positions/edit/:id', component: EditPositionComponent },
          { path: 'positions/view/:id', component: ViewPositionComponent },

          // Candidates routes
          { path: 'candidates', component: ListCandidatesComponent },
          { path: 'candidates/add', component: AddCandidateComponent },
          { path: 'candidates/edit/:id', component: EditCandidateComponent },
          { path: 'candidates/view/:id', component: ViewCandidateComponent },

          // Interview session routes
          {
            path: 'sessions',
            children: [
              {
                path: '',
                component: AllInterviewSessionsComponent,
                canActivate: [PermissionGuard],
                data: {
                  requiredModule: 'interviewModule',
                  requiredAction: 'read',
                },
              },
              {
                path: ':candidateId/:positionId',
                component: ListInterviewSessionsComponent,
              },
              {
                path: 'add/:candidateId/:positionId',
                component: AddInterviewSessionComponent,
                canActivate: [PermissionGuard],
                data: {
                  requiredModule: 'interviewModule',
                  requiredAction: 'write',
                },
              },
              {
                path: 'view/:sessionId/:candidateId/:positionId',
                component: ViewInterviewSessionComponent,
                canActivate: [PermissionGuard],
                data: {
                  requiredModule: 'interviewModule',
                  requiredAction: 'read',
                },
              },

              {
                path: 'edit/:sessionId/:candidateId/:positionId',
                component: EditInterviewSessionComponent,
                canActivate: [PermissionGuard],
                data: {
                  requiredModule: 'interviewModule',
                  requiredAction: 'update',
                },
              },
            ],
          },

          // Decision routes
          {
            path: 'decision/:candidateId/:positionId',
            component: CandidateDecisionComponent,
            canActivate: [PermissionGuard],
            data: {
              requiredModule: 'interviewModule',
              requiredAction: 'write',
            },
          },

          // Registration Tokens routes
          {
            path: 'registration-tokens',
            loadComponent: () =>
              import(
                './pages/interview/registration-tokens/registration-tokens.component'
              ).then((c) => c.RegistrationTokensComponent),
            canActivate: [PermissionGuard],
            data: {
              requiredModule: 'interviewModule',
              requiredAction: 'write',
            },
          },
        ],
      },
    ],
  },
  {
    path: 'login',
    component: AuthLayoutComponent,
    children: [{ path: '', component: LoginComponent }],
  },
  // Public routes for candidate registration
  {
    path: 'register',
    component: AuthLayoutComponent,
    children: [
      { path: '', redirectTo: 'positions', pathMatch: 'full' },
      {
        path: 'positions',
        loadComponent: () =>
          import('./pages/public/open-positions/open-positions.component').then(
            (c) => c.OpenPositionsComponent
          ),
      },
      {
        path: 'validate/:token',
        loadComponent: () =>
          import(
            './pages/public/token-validation/token-validation.component'
          ).then((c) => c.TokenValidationComponent),
      },
      {
        path: 'candidate/:token',
        loadComponent: () =>
          import(
            './pages/public/candidate-registration/candidate-registration.component'
          ).then((c) => c.CandidateRegistrationComponent),
      },
      {
        path: 'success',
        loadComponent: () =>
          import(
            './pages/public/registration-success/registration-success.component'
          ).then((c) => c.RegistrationSuccessComponent),
      },
    ],
  },
];
