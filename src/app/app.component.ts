import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidenavComponent } from './sidenav/sidenav.component';
import { Router, RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [CommonModule, SidenavComponent, RouterModule]
})


export class AppComponent {
  title = 'Bawa Group';
isAuthenticated  = signal(false);


  ngOnInit(){
    
    const user = localStorage.getItem('currentUser');
    if(user){
      this.isAuthenticated.set(true);

  } else {
    this.isAuthenticated.set(false);
  }



  


}
}