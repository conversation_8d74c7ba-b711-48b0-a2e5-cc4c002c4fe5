<!-- candidate-decision.component.html -->
<div class="decision-container">
    <div class="header-section">
      <div class="title-section">
        <button mat-icon-button color="primary" (click)="goBack()" matTooltip="Back to Candidate">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1 class="page-title">Candidate Decision</h1>
      </div>
    </div>
  
    <div *ngIf="error" class="error-message">
      {{error}}
      <button mat-button color="primary" (click)="loadData()">Retry</button>
    </div>
  
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading details...</p>
    </div>
  
    <ng-container *ngIf="!isLoading && candidate && position">
      <!-- Candidate and Position Info Card -->
      <mat-card class="info-card">
        <mat-card-content>
          <div class="candidate-position-info">
            <div class="info-section">
              <h2 class="section-title">Candidate Information</h2>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">Name</div>
                  <div class="info-value">{{candidate.first_name}} {{candidate.last_name}}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Email</div>
                  <div class="info-value">{{candidate.email}}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Experience</div>
                  <div class="info-value">{{candidate.total_experience || 'N/A'}} years</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Current Company</div>
                  <div class="info-value">{{candidate.current_organization || 'N/A'}}</div>
                </div>
              </div>
            </div>
            
            <mat-divider></mat-divider>
            
            <div class="info-section">
              <h2 class="section-title">Position Information</h2>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">Title</div>
                  <div class="info-value">{{position.title}}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Department</div>
                  <div class="info-value">{{position.department_name}}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Vacancies</div>
                  <div class="info-value">{{position.vacancies}}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">Priority</div>
                  <div class="info-value">{{position.priority}}</div>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
  
      <!-- Decision Form -->
      <mat-card class="decision-card">
        <mat-card-header>
          <mat-card-title>Make Decision</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="decisionForm" (ngSubmit)="onSubmitDecision()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Decision</mat-label>
                <mat-select formControlName="decision" (selectionChange)="onDecisionChange($event)">
                  <mat-option value="Selected">Select</mat-option>
                  <mat-option value="Rejected">Reject</mat-option>
                </mat-select>
                <mat-error *ngIf="decisionForm.get('decision')?.hasError('required')">
                  Decision is required
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Remarks</mat-label>
                <textarea matInput formControlName="remarks" rows="4" placeholder="Enter decision remarks..."></textarea>
                <mat-error *ngIf="decisionForm.get('remarks')?.hasError('required')">
                  Remarks are required
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="decisionForm.invalid || isLoading">
                <mat-icon>save</mat-icon> Save Decision
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
  
      <!-- Staff Assignment (only shown when Selected) -->
      <mat-card class="staff-card" *ngIf="showStaffSection">
        <mat-card-header>
          <mat-card-title>Assign to Staff Position</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <p class="instruction-text">
            Assign the selected candidate to a staff position in the system. This will create a staff record for the candidate.
          </p>
          
          <form [formGroup]="staffForm" (ngSubmit)="onSubmitStaffAssignment()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Select Staff Position</mat-label>
                <mat-select formControlName="staff_id">
                  <mat-option value="">-- Select Staff --</mat-option>
                  <mat-option *ngFor="let staff of staffList" [value]="staff.id">
                    {{staff.firstName}} {{staff.lastName}} ({{staff.employeeId}})
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="staffForm.get('staff_id')?.hasError('required')">
                  Staff position is required
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="form-actions">
              <button mat-raised-button color="accent" type="submit" [disabled]="staffForm.invalid || isLoading || !decision?.id">
                <mat-icon>person_add</mat-icon> Assign to Staff
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
  
      <!-- Decision History (if exists) -->
      <mat-card class="history-card" *ngIf="decision">
        <mat-card-header>
          <mat-card-title>Decision History</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="history-grid">
            <div class="history-item">
              <div class="history-label">Decision</div>
              <div class="history-value">
                <span class="decision-badge" [ngClass]="decision.decision === 'Selected' ? 'decision-selected' : 'decision-rejected'">
                  {{decision.decision}}
                </span>
              </div>
            </div>
            
            <div class="history-item">
              <div class="history-label">Date</div>
              <div class="history-value">{{formatDate(decision.decision_date)}}</div>
            </div>
            
            <div class="history-item">
              <div class="history-label">Approved By</div>
              <div class="history-value">{{decision.approver_name || 'N/A'}}</div>
            </div>
            
            <div class="history-item" *ngIf="decision.staff_id">
              <div class="history-label">Assigned Staff ID</div>
              <div class="history-value">{{decision.staff_id}}</div>
            </div>
          </div>
          
          <div class="remarks-section">
            <div class="remarks-label">Remarks</div>
            <div class="remarks-content">{{decision.remarks}}</div>
          </div>
        </mat-card-content>
      </mat-card>
    </ng-container>
  </div>