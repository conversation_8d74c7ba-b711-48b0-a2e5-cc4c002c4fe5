// candidate-decision.component.scss
.decision-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100%;
  
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .title-section {
        display: flex;
        align-items: center;
        gap: 10px;
  
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }
  
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      
      p {
        margin-top: 16px;
        color: #666;
      }
    }
  
    mat-card {
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
  
    .info-card {
      .candidate-position-info {
        display: flex;
        flex-direction: column;
        gap: 20px;
  
        .info-section {
          .section-title {
            font-size: 18px;
            color: #333;
            margin: 0 0 16px 0;
          }
  
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
  
            .info-item {
              .info-label {
                font-size: 14px;
                color: #666;
                margin-bottom: 4px;
              }
  
              .info-value {
                font-size: 16px;
                color: #333;
              }
            }
          }
        }
      }
    }
  
    .decision-card, .staff-card {
      mat-card-header {
        margin-bottom: 16px;
      }
      
      form {
        display: flex;
        flex-direction: column;
        gap: 16px;
        
        .form-row {
          width: 100%;
          
          mat-form-field {
            width: 100%;
          }
        }
        
        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 16px;
          margin-top: 16px;
        }
      }
      
      .instruction-text {
        margin-bottom: 20px;
        color: #666;
        font-style: italic;
      }
    }
  
    .history-card {
      .history-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
  
        .history-item {
          .history-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }
  
          .history-value {
            font-size: 16px;
            color: #333;
          }
        }
      }
  
      .remarks-section {
        .remarks-label {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }
  
        .remarks-content {
          padding: 16px;
          background-color: #f9f9f9;
          border-radius: 4px;
          white-space: pre-line;
          line-height: 1.5;
        }
      }
    }
  
    .decision-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 14px;
      font-weight: 500;
      
      &.decision-selected {
        background-color: #e8f5e9;
        color: #388e3c;
      }
      
      &.decision-rejected {
        background-color: #ffebee;
        color: #d32f2f;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .decision-container {
      padding: 16px;
      
      .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
      
      .info-card {
        .candidate-position-info {
          .info-section {
            .info-grid {
              grid-template-columns: 1fr;
            }
          }
        }
      }
      
      .history-card {
        .history-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }