// candidate-decision.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { InterviewService } from '../../../../services/interview.service';
import { MasterserviceService } from '../../../../services/masterservice.service';

@Component({
  selector: 'app-candidate-decision',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
  ],
  templateUrl: './candidate-decision.component.html',
  styleUrls: ['./candidate-decision.component.scss'],
})
export class CandidateDecisionComponent implements OnInit {
  candidateId!: number;
  positionId!: number;
  candidate: any = null;
  position: any = null;
  decision: any = null;
  decisionForm!: FormGroup;
  staffForm!: FormGroup;
  isLoading = true;
  error = '';
  staffList: any[] = [];
  showStaffSection = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.candidateId = +params['candidateId'];
      this.positionId = +params['positionId'];

      this.initForms();
      this.loadData();
    });
  }

  initForms(): void {
    this.decisionForm = this.fb.group({
      decision: ['', Validators.required],
      remarks: ['', Validators.required],
    });

    this.staffForm = this.fb.group({
      staff_id: ['', Validators.required],
    });
  }

  loadData(): void {
    this.isLoading = true;
    this.error = '';

    // Load candidate details
    this.interviewService.getCandidateById(this.candidateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.candidate = response.candidate;
          this.loadPosition();
        } else {
          this.error = response.message || 'Failed to load candidate details';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error loading candidate:', error);
        this.error = 'Error loading candidate details';
        this.isLoading = false;
      },
    });
  }

  loadPosition(): void {
    this.interviewService.getPositionById(this.positionId).subscribe({
      next: (response) => {
        if (response.success) {
          this.position = response.position;
          this.loadDecision();
        } else {
          this.error = response.message || 'Failed to load position details';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error loading position:', error);
        this.error = 'Error loading position details';
        this.isLoading = false;
      },
    });
  }

  loadDecision(): void {
    this.interviewService
      .getCandidateDecision(this.candidateId, this.positionId)
      .subscribe({
        next: (response) => {
          if (response.success && response.decision) {
            this.decision = response.decision;
            this.decisionForm.patchValue({
              decision: this.decision.decision,
              remarks: this.decision.remarks,
            });

            if (this.decision.decision === 'Selected') {
              this.showStaffSection = true;
              this.loadStaffList();

              if (this.decision.staff_id) {
                this.staffForm.patchValue({
                  staff_id: this.decision.staff_id,
                });
              }
            }
          } else {
            // No decision found, this is a new decision
            console.log('No existing decision found');
          }
          this.isLoading = false;
        },
        error: (error) => {
          if (error.status === 404) {
            // No decision yet, this is normal
            this.isLoading = false;
          } else {
            console.error('Error loading decision:', error);
            this.error = 'Error loading decision details';
            this.isLoading = false;
          }
        },
      });
  }

  loadStaffList(): void {
    this.masterService.getAllStaff().subscribe({
      next: (response) => {
        if (response.success) {
          this.staffList = response.staff;
        } else {
          console.error('Failed to load staff list:', response.message);
        }
      },
      error: (error) => {
        console.error('Error loading staff list:', error);
      },
    });
  }

  onDecisionChange(event: any): void {
    const decision = event.value;
    this.showStaffSection = decision === 'Selected';

    if (this.showStaffSection) {
      this.loadStaffList();
    }
  }

  onSubmitDecision(): void {
    if (this.decisionForm.invalid) {
      return;
    }

    this.isLoading = true;
    const decisionData = {
      candidate_id: this.candidateId,
      position_id: this.positionId,
      decision: this.decisionForm.value.decision,
      remarks: this.decisionForm.value.remarks,
    };

    const submitAction = this.decision
      ? this.interviewService.updateCandidateDecision(
          this.decision.id,
          decisionData
        )
      : this.interviewService.addCandidateDecision(decisionData);

    submitAction.subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Decision saved successfully', 'Close', {
            duration: 3000,
          });

          if (!this.decision) {
            // This was a new decision, reload to get the decision ID
            this.loadDecision();
          } else {
            this.isLoading = false;
          }
        } else {
          this.error = response.message || 'Failed to save decision';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error saving decision:', error);
        this.error = 'Error saving decision';
        this.isLoading = false;
      },
    });
  }

  onSubmitStaffAssignment(): void {
    if (this.staffForm.invalid || !this.decision?.id) {
      return;
    }

    this.isLoading = true;
    this.interviewService
      .setStaffForCandidate(this.decision.id, this.staffForm.value.staff_id)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Staff assignment saved successfully', 'Close', {
              duration: 3000,
            });
          } else {
            this.error = response.message || 'Failed to assign staff';
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error assigning staff:', error);
          this.error = 'Error assigning staff';
          this.isLoading = false;
        },
      });
  }

  goBack(): void {
    this.router.navigate(['/site/interview/candidates/view', this.candidateId]);
  }

  formatDate(date: string): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleString();
  }
}
