// Main container
.view-candidate-container {
  padding: 20px;
  
  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .component-title {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .content-wrapper {
    width: 100%;
  }
  
  // Error message styling
  .error-message {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  // Loading indicator styling
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #3f51b5;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
}

// Profile card styling
.profile-card {
  margin-bottom: 20px;
  
  .profile-header {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .profile-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      
      .avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .avatar-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: #e0e0e0;
        
        mat-icon {
          font-size: 48px;
          height: 48px;
          width: 48px;
          color: #9e9e9e;
        }
      }
    }
    
    .profile-info {
      flex: 1;
      min-width: 250px;
      
      .candidate-name {
        font-size: 24px;
        font-weight: 500;
        margin: 0 0 5px 0;
      }
      
      .candidate-title {
        color: #616161;
        margin: 0 0 10px 0;
      }
      
      .candidate-status {
        margin-bottom: 10px;
      }
      
      .candidate-contact {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        
        .contact-item {
          display: flex;
          align-items: center;
          gap: 8px;
          
          mat-icon {
            color: #757575;
            font-size: 18px;
            height: 18px;
            width: 18px;
          }
        }
      }
    }
    
    .profile-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: flex-start;
    }
  }
  
  .quick-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: 10px;
      min-width: 180px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f5f5f5;
      
      .stat-icon {
        background: rgba(33, 150, 243, 0.1);
        color: #2196f3;
        width: 40px;
        height: 40px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        mat-icon {
          font-size: 20px;
          height: 20px;
          width: 20px;
        }
      }
      
      .stat-content {
        display: flex;
        flex-direction: column;
        
        .stat-value {
          font-weight: 500;
          font-size: 16px;
        }
        
        .stat-label {
          color: #757575;
          font-size: 12px;
        }
      }
    }
  }
}

// Details card styling
.details-card {
  mat-tab-group {
    margin-top: 10px;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .info-section {
    margin-bottom: 30px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 500;
      color: #424242;
      display: flex;
      align-items: center;
      
      // Comment icon styling
      .comment-icon {
        margin-left: 8px;
        vertical-align: middle;
        
        .mat-icon {
          font-size: 18px;
          color: #9e9e9e;
          
          &.has-comments {
            color: #2196f3;  // Blue color for existing comments
          }
        }
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      
      .info-item {
        .info-label {
          font-size: 12px;
          color: #757575;
          margin-bottom: 5px;
        }
        
        .info-value {
          font-size: 16px;
          word-break: break-word;
        }
      }
    }
    
    .address-section {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      
      .address-box {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        
        h4 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 500;
        }
        
        p {
          margin: 0;
          white-space: pre-wrap;
        }
      }
    }
    
    // Skills styling
    .skills-container {
      margin: 10px 0;
      
      mat-chip-listbox {
        display: block;
        
        mat-chip {
          margin: 4px;
        }
      }
    }
    
    // Reason for change styling
    .reason-text {
      white-space: pre-wrap;
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      margin-top: 0;
    }
    
    // Certifications styling
    .certification-text {
      white-space: pre-wrap;
    }
    
    // Siblings styling
    .siblings-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      
      .sibling-card {
        background-color: #f5f5f5;
        border-radius: 4px;
        padding: 15px;
        
        h4 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 500;
        }
        
        .sibling-detail {
          margin-bottom: 5px;
          font-size: 14px;
          
          .detail-label {
            color: #757575;
            display: inline-block;
            width: 120px;
          }
          
          .detail-badge {
            background-color: #2196f3;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  // Table container styling
  .table-container {
    margin-top: 15px;
    overflow-x: auto;
    
    table {
      width: 100%;
      
      th {
        font-weight: 500;
      }
    }
  }
  
  // No data message styling
  .no-data {
    color: #757575;
    font-style: italic;
    margin: 20px 0;
  }
  
  // Status badge styling
  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    &.status-new {
      background-color: #e3f2fd;
      color: #1976d2;
    }
    
    &.status-in-process {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    &.status-selected {
      background-color: #e8f5e9;
      color: #388e3c;
    }
    
    &.status-rejected {
      background-color: #ffebee;
      color: #d32f2f;
    }
  }
  
  // Outcome badge styling
  .outcome-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    &.outcome-move-to-next-round {
      background-color: #e8f5e9;
      color: #388e3c;
    }
    
    &.outcome-reject {
      background-color: #ffebee;
      color: #d32f2f;
    }
    
    &.outcome-hold {
      background-color: #e0e0e0;
      color: #616161;
    }
    
    &.outcome-select {
      background-color: #e3f2fd;
      color: #1976d2;
    }
  }
  
  mat-divider {
    margin: 30px 0;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .view-candidate-container {
    padding: 15px;
  }
  
  .profile-card {
    .profile-header {
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      .profile-info {
        width: 100%;
        
        .candidate-contact {
          justify-content: center;
        }
      }
      
      .profile-actions {
        width: 100%;
        justify-content: center;
      }
    }
    
    .quick-stats {
      justify-content: center;
    }
  }
  
  .details-card {
    .info-grid {
      grid-template-columns: 1fr;
    }
    
    .address-section {
      grid-template-columns: 1fr;
    }
    
    .siblings-container {
      grid-template-columns: 1fr;
    }
  }
}