<div class="view-candidate-container">
  <div class="component-header">
    <h1 class="component-title">Candidate Details</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back to Candidates
      </button>
    </div>
  </div>

  <div class="content-wrapper">
    <!-- Error message display -->
    <div *ngIf="error" class="error-message">
      {{error}}
      <button mat-button color="primary" (click)="loadCandidateData()">Retry</button>
    </div>
    
    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="loading-indicator">
      <div class="spinner"></div>
      <span>Loading candidate data...</span>
    </div>
    
    <!-- Candidate profile view -->
    <ng-container *ngIf="!isLoading && candidate">
      <!-- Profile Header -->
      <mat-card class="profile-card">
        <mat-card-content>
          <div class="profile-header">
            <div class="profile-avatar">
              <ng-container *ngIf="candidate.profile_picture; else defaultAvatar">
                <img [src]="getProfilePictureUrl(candidate.profile_picture)" alt="Profile Picture" class="avatar-image">
              </ng-container>
              <ng-template #defaultAvatar>
                <div class="avatar-placeholder">
                  <mat-icon>person</mat-icon>
                </div>
              </ng-template>
            </div>
            <div class="profile-info">
              <h2 class="candidate-name">{{candidate.first_name}} {{candidate.middle_name ? candidate.middle_name + ' ' : ''}}{{candidate.last_name}}</h2>
              <p class="candidate-title">{{candidate.current_designation || 'No Current Designation'}} {{candidate.current_organization ? '@ ' + candidate.current_organization : ''}}</p>
              
              <div class="candidate-status">
                <span class="status-badge" [ngClass]="getStatusClass(candidate.status)">
                  {{candidate.status}}
                </span>
              </div>
              
              <div class="candidate-contact">
                <div class="contact-item">
                  <mat-icon>email</mat-icon>
                  <span>{{candidate.email}}</span>
                </div>
                <div class="contact-item">
                  <mat-icon>phone</mat-icon>
                  <span>{{candidate.mobile_number}}</span>
                </div>
              </div>
            </div>
            
            <div class="profile-actions">
              <button mat-raised-button color="primary" (click)="editCandidate()">
                <mat-icon>edit</mat-icon> Edit
              </button>
              <button mat-raised-button color="accent" (click)="downloadResume()" [disabled]="!candidate.resume_file">
                <mat-icon>description</mat-icon> Download Resume
              </button>
              <!-- Status dropdown menu would go here -->
            </div>
          </div>
          
          <!-- Quick stats -->
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>work_history</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{candidate.total_experience || '0'}} yrs</div>
                <div class="stat-label">Experience</div>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>school</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{candidate.highest_qualification || 'N/A'}}</div>
                <div class="stat-label">Qualification</div>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>attach_money</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{candidate.expected_salary || 'N/A'}}</div>
                <div class="stat-label">Expected Salary</div>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>hourglass_empty</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{candidate.notice_period || '0'}} days</div>
                <div class="stat-label">Notice Period</div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
      
      <!-- Detailed Information Tabs -->
      <mat-card class="details-card">
        <mat-card-content>
          <mat-tab-group animationDuration="0ms">
            <!-- Personal Information -->
            <mat-tab label="Personal Info">
              <div class="tab-content">
                <div class="info-section">
                  <h3>
                    Personal Details
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Personal Details', 'Personal Details')" 
                      [matBadge]="hasUnreadComments('Personal Details') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Personal Details')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Personal Details')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Full Name</div>
                      <div class="info-value">{{candidate.first_name}} {{candidate.middle_name || ''}} {{candidate.last_name}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.date_of_birth">
                      <div class="info-label">Date of Birth</div>
                      <div class="info-value">{{formatDate(candidate.date_of_birth)}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.gender_name">
                      <div class="info-label">Gender</div>
                      <div class="info-value">{{candidate.gender_name}}</div>
                    </div>
                    
                    <div class="info-item">
                      <div class="info-label">Email</div>
                      <div class="info-value">{{candidate.email}}</div>
                    </div>
                    
                    <div class="info-item">
                      <div class="info-label">Mobile Number</div>
                      <div class="info-value">{{candidate.mobile_number}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.alternate_phone">
                      <div class="info-label">Alternate Phone</div>
                      <div class="info-value">{{candidate.alternate_phone}}</div>
                    </div>
                  </div>
                </div>
                
                <mat-divider></mat-divider>
                
                <div class="info-section">
                  <h3>
                    Address Information
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Address Information', 'Address Information')" 
                      [matBadge]="hasUnreadComments('Address Information') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Address Information')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Address Information')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="address-section">
                    <div class="address-box">
                      <h4>Current Address</h4>
                      <p>{{candidate.current_address || 'Not provided'}}</p>
                    </div>
                    
                    <div class="address-box">
                      <h4>Permanent Address</h4>
                      <p>{{candidate.permanent_address || 'Not provided'}}</p>
                    </div>
                  </div>
                </div>
                
                <mat-divider *ngIf="candidate.id_proof_type || candidate.id_proof_number"></mat-divider>
                
                <div class="info-section" *ngIf="candidate.id_proof_type || candidate.id_proof_number">
                  <h3>
                    ID Information
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('ID Information', 'ID Information')" 
                      [matBadge]="hasUnreadComments('ID Information') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('ID Information')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('ID Information')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="info-grid">
                    <div class="info-item" *ngIf="candidate.id_proof_type">
                      <div class="info-label">ID Type</div>
                      <div class="info-value">{{candidate.id_proof_type}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.id_proof_number">
                      <div class="info-label">ID Number</div>
                      <div class="info-value">{{candidate.id_proof_number}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Family">
              <div class="tab-content">
                <div class="info-section">
                  <h3>
                    Parents Information
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Parents Information', 'Parents Information')" 
                      [matBadge]="hasUnreadComments('Parents Information') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Parents Information')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Parents Information')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="info-grid">
                    <div class="info-item" *ngIf="candidate.father_name">
                      <div class="info-label">Father's Name</div>
                      <div class="info-value">{{candidate.father_name}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.father_occupation">
                      <div class="info-label">Father's Occupation</div>
                      <div class="info-value">{{candidate.father_occupation}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.father_contact">
                      <div class="info-label">Father's Contact</div>
                      <div class="info-value">{{candidate.father_contact}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.father_status">
                      <div class="info-label">Father's Status</div>
                      <div class="info-value">{{candidate.father_status}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.mother_name">
                      <div class="info-label">Mother's Name</div>
                      <div class="info-value">{{candidate.mother_name}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.mother_occupation">
                      <div class="info-label">Mother's Occupation</div>
                      <div class="info-value">{{candidate.mother_occupation}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.mother_contact">
                      <div class="info-label">Mother's Contact</div>
                      <div class="info-value">{{candidate.mother_contact}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.mother_status">
                      <div class="info-label">Mother's Status</div>
                      <div class="info-value">{{candidate.mother_status}}</div>
                    </div>
                  </div>
                  
                  <div *ngIf="candidate.family_address" class="address-section">
                    <div class="address-box">
                      <h4>Family Address</h4>
                      <p>{{candidate.family_address}}</p>
                    </div>
                  </div>
                </div>
                
                <mat-divider *ngIf="candidate.siblings && candidate.siblings.length > 0"></mat-divider>
                
                <div class="info-section" *ngIf="candidate.siblings && candidate.siblings.length > 0">
                  <h3>
                    Siblings
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Siblings', 'Siblings')" 
                      [matBadge]="hasUnreadComments('Siblings') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Siblings')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Siblings')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="siblings-container">
                    <div class="sibling-card" *ngFor="let sibling of candidate.siblings">
                      <h4>{{sibling.name}}</h4>
                      
                      <div class="sibling-detail">
                        <span class="detail-label">Date of Birth:</span>
                        <span class="detail-value">{{formatDate(sibling.date_of_birth)}}</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.gender">
                        <span class="detail-label">Gender:</span>
                        <span class="detail-value">{{sibling.gender}}</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.occupation">
                        <span class="detail-label">Occupation:</span>
                        <span class="detail-value">{{sibling.occupation}}</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.marital_status">
                        <span class="detail-label">Marital Status:</span>
                        <span class="detail-value">{{sibling.marital_status}}</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.contact">
                        <span class="detail-label">Contact:</span>
                        <span class="detail-value">{{sibling.contact}}</span>
                      </div>
                      
                      <div class="sibling-detail">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">{{sibling.status}}</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.is_emergency_contact">
                        <span class="detail-badge">Emergency Contact</span>
                      </div>
                      
                      <div class="sibling-detail" *ngIf="sibling.additional_info">
                        <span class="detail-label">Additional Info:</span>
                        <span class="detail-value">{{sibling.additional_info}}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </mat-tab>

            
            <!-- Professional Information -->
            <mat-tab label="Professional">
              <div class="tab-content">
                <div class="info-section">
                  <h3>
                    Employment Information
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Employment Information', 'Employment Information')" 
                      [matBadge]="hasUnreadComments('Employment Information') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Employment Information')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Employment Information')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Total Experience</div>
                      <div class="info-value">{{candidate.total_experience || '0'}} years</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.current_organization">
                      <div class="info-label">Current Organization</div>
                      <div class="info-value">{{candidate.current_organization}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.current_designation">
                      <div class="info-label">Current Designation</div>
                      <div class="info-value">{{candidate.current_designation}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.current_salary">
                      <div class="info-label">Current Salary</div>
                      <div class="info-value">{{candidate.current_salary}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.expected_salary">
                      <div class="info-label">Expected Salary</div>
                      <div class="info-value">{{candidate.expected_salary}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.notice_period">
                      <div class="info-label">Notice Period</div>
                      <div class="info-value">{{candidate.notice_period}} days</div>
                    </div>
                  </div>
                </div>
                
                <div class="info-section" *ngIf="candidate.reason_for_change">
                  <h3>
                    Reason for Change
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Reason for Change', 'Reason for Change')" 
                      [matBadge]="hasUnreadComments('Reason for Change') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Reason for Change')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Reason for Change')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  <p class="reason-text">{{candidate.reason_for_change}}</p>
                </div>
                
                <mat-divider></mat-divider>
                
                <div class="info-section">
                  <h3>
                    Skills
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Skills', 'Skills')" 
                      [matBadge]="hasUnreadComments('Skills') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Skills')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Skills')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="skills-container">
                    <mat-chip-listbox *ngIf="getSkillsArray().length > 0">
                      <mat-chip *ngFor="let skill of getSkillsArray()">
                        {{skill}}
                      </mat-chip>
                    </mat-chip-listbox>
                    
                    <p *ngIf="getSkillsArray().length === 0" class="no-data">No skills provided</p>
                  </div>
                </div>
              </div>
            </mat-tab>
            
            <!-- Education Information -->
            <mat-tab label="Education">
              <div class="tab-content">
                <div class="info-section">
                  <h3>
                    Educational Qualifications
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Educational Qualifications', 'Educational Qualifications')" 
                      [matBadge]="hasUnreadComments('Educational Qualifications') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Educational Qualifications')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Educational Qualifications')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  
                  <div class="info-grid">
                    <div class="info-item" *ngIf="candidate.highest_qualification">
                      <div class="info-label">Highest Qualification</div>
                      <div class="info-value">{{candidate.highest_qualification}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.university">
                      <div class="info-label">University/Institution</div>
                      <div class="info-value">{{candidate.university}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.year_of_passing">
                      <div class="info-label">Year of Passing</div>
                      <div class="info-value">{{candidate.year_of_passing}}</div>
                    </div>
                    
                    <div class="info-item" *ngIf="candidate.specialization">
                      <div class="info-label">Specialization</div>
                      <div class="info-value">{{candidate.specialization}}</div>
                    </div>
                  </div>
                </div>
                
                <mat-divider *ngIf="candidate.additional_certifications"></mat-divider>
                
                <div class="info-section" *ngIf="candidate.additional_certifications">
                  <h3>
                    Additional Certifications
                    <button 
                      mat-icon-button 
                      class="comment-icon" 
                      (click)="openCommentDialog('Additional Certifications', 'Additional Certifications')" 
                      [matBadge]="hasUnreadComments('Additional Certifications') ? '!' : ''" 
                      matBadgeSize="small" 
                      matBadgeColor="warn" 
                      [matBadgeHidden]="!hasComments('Additional Certifications')"
                      matTooltip="Add or view comments">
                      <mat-icon [class.has-comments]="hasComments('Additional Certifications')">
                        comment
                      </mat-icon>
                    </button>
                  </h3>
                  <p class="certification-text">{{candidate.additional_certifications}}</p>
                </div>
              </div>
            </mat-tab>
            
            <!-- Positions Applied -->
            <mat-tab label="Applications ({{positionsApplied.length || 0}})">
              <div class="tab-content">
                <div class="info-section">
                  <h3>Positions Applied</h3>
                  
                  <div *ngIf="positionsApplied.length === 0" class="no-data">
                    No applications found for this candidate.
                  </div>
                  
                  <div *ngIf="positionsApplied.length > 0" class="table-container">
                    <table mat-table [dataSource]="positionsApplied">
                      <!-- Position Title Column -->
                      <ng-container matColumnDef="position_title">
                        <th mat-header-cell *matHeaderCellDef>Position</th>
                        <td mat-cell *matCellDef="let element">{{element.position_title}}</td>
                      </ng-container>
                      
                      <!-- Department Column -->
                      <ng-container matColumnDef="department_name">
                        <th mat-header-cell *matHeaderCellDef>Department</th>
                        <td mat-cell *matCellDef="let element">{{element.department_name}}</td>
                      </ng-container>
                      
                      <!-- Application Date Column -->
                      <ng-container matColumnDef="application_date">
                        <th mat-header-cell *matHeaderCellDef>Applied On</th>
                        <td mat-cell *matCellDef="let element">{{formatDate(element.application_date)}}</td>
                      </ng-container>
                      
                      <!-- Status Column -->
                      <ng-container matColumnDef="status">
                        <th mat-header-cell *matHeaderCellDef>Status</th>
                        <td mat-cell *matCellDef="let element">
                          <span class="status-badge" [ngClass]="getStatusClass(element.status)">
                            {{element.status}}
                          </span>
                        </td>
                      </ng-container>
                      
                      <!-- Actions Column -->
                      <ng-container matColumnDef="actions">
                        <th mat-header-cell *matHeaderCellDef>Actions</th>
                        <td mat-cell *matCellDef="let element">
                          <button mat-icon-button color="primary" (click)="viewPosition(element.position_id)" matTooltip="View Position">
                            <mat-icon>visibility</mat-icon>
                          </button>
                          <button mat-icon-button color="accent" (click)="addInterviewSession(element.position_id)" matTooltip="Add Interview">
                            <mat-icon>add_task</mat-icon>
                          </button>
                          <button mat-icon-button color="warn" (click)="loadInterviewSessions(element.position_id)" matTooltip="View Interviews">
                            <mat-icon>event_note</mat-icon>
                          </button>
                        </td>
                      </ng-container>
                      
                      <tr mat-header-row *matHeaderRowDef="positionsColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: positionsColumns;"></tr>
                    </table>
                  </div>
                </div>
              </div>
            </mat-tab>
            
            <!-- Interview Sessions -->
            <mat-tab label="Interviews ({{interviewSessions.length || 0}})">
              <div class="tab-content">
                <div class="info-section">
                  <h3>Interview Sessions</h3>
                  
                  <div *ngIf="interviewSessions.length === 0" class="no-data">
                    No interview sessions found. Please select a position from the Applications tab to view interviews.
                  </div>
                  
                  <div *ngIf="interviewSessions.length > 0" class="table-container">
                    <table mat-table [dataSource]="interviewSessions">
                      <!-- Round Title Column -->
                      <ng-container matColumnDef="round_title">
                        <th mat-header-cell *matHeaderCellDef>Round</th>
                        <td mat-cell *matCellDef="let element">{{element.round_title}}</td>
                      </ng-container>
                      
                      <!-- Interview Date Column -->
                      <ng-container matColumnDef="interview_date">
                        <th mat-header-cell *matHeaderCellDef>Date & Time</th>
                        <td mat-cell *matCellDef="let element">{{formatDate(element.interview_date)}}</td>
                      </ng-container>
                      
                      <!-- Interviewer Column -->
                      <ng-container matColumnDef="interviewer_name">
                        <th mat-header-cell *matHeaderCellDef>Interviewer</th>
                        <td mat-cell *matCellDef="let element">{{element.interviewer_name}}</td>
                      </ng-container>
                      
                      <!-- Mode Column -->
                      <ng-container matColumnDef="interview_mode">
                        <th mat-header-cell *matHeaderCellDef>Mode</th>
                        <td mat-cell *matCellDef="let element">{{element.interview_mode}}</td>
                      </ng-container>
                      
                      <!-- Outcome Column -->
                      <ng-container matColumnDef="outcome">
                        <th mat-header-cell *matHeaderCellDef>Outcome</th>
                        <td mat-cell *matCellDef="let element">
                          <span class="outcome-badge" [ngClass]="getOutcomeClass(element.outcome)">
                              {{element.outcome}}
                            </span>
                        </td>
                      </ng-container>
                      
                      <!-- Actions Column -->
                      <ng-container matColumnDef="actions">
                        <th mat-header-cell *matHeaderCellDef>Actions</th>
                        <td mat-cell *matCellDef="let element">
                          <button mat-icon-button color="primary" matTooltip="View Details">
                            <mat-icon>visibility</mat-icon>
                          </button>
                        </td>
                      </ng-container>
                      
                      <tr mat-header-row *matHeaderRowDef="interviewColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: interviewColumns;"></tr>
                    </table>
                  </div>
                </div>
              </div>
            </mat-tab>
            
            <!-- References Tab -->
            <mat-tab label="References" *ngIf="candidate.reference_contacts">
              <div class="tab-content">
                <div class="info-section">
                  <h3>Reference Contacts</h3>
                  <p class="reference-text">{{candidate.reference_contacts}}</p>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card-content>
      </mat-card>
    </ng-container>
  </div>
</div>