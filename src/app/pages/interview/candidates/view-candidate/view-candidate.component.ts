import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import {
  MatChipsModule,
  MatChipListbox,
  MatChipOption,
} from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { InterviewService } from '../../../../services/interview.service';
import { CommentDialogComponent } from '../../../../components/comment-dialog/comment-dialog.component';
import { CandidateCommentService } from '../../../../services/candidate-comment.service';

interface CandidateComment {
  id: number;
  candidate_id: number;
  field_identifier: string;
  comment_text: string;
  user_id: number;
  username: string;
  created_at: string;
  updated_at: string;
  is_read: boolean;
  status: string;
}

@Component({
  selector: 'app-view-candidate',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatChipsModule,
    MatTableModule,
    MatBadgeModule,
    MatTooltipModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './view-candidate.component.html',
  styleUrls: ['./view-candidate.component.scss'],
})
export class ViewCandidateComponent implements OnInit {
  candidateId: number = 0;
  candidate: any = null;
  positionsApplied: any[] = [];
  interviewSessions: any[] = [];
  isLoading: boolean = true;
  error: string | null = null;
  commentsMap: Map<string, boolean> = new Map(); // fieldIdentifier -> hasUnreadComments

  // Field identifiers based on section titles
  readonly sectionIdentifiers = {
    personalDetails: 'Personal Details',
    addressInformation: 'Address Information',
    idInformation: 'ID Information',
    parentsInformation: 'Parents Information',
    siblings: 'Siblings',
    employmentInformation: 'Employment Information',
    reasonForChange: 'Reason for Change',
    skills: 'Skills',
    educationalQualifications: 'Educational Qualifications',
    additionalCertifications: 'Additional Certifications',
  };

  // Table columns for positions applied
  positionsColumns: string[] = [
    'position_title',
    'department_name',
    'application_date',
    'status',
    'actions',
  ];

  // Table columns for interview sessions
  interviewColumns: string[] = [
    'round_title',
    'interview_date',
    'interviewer_name',
    'interview_mode',
    'outcome',
    'actions',
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private candidateCommentService: CandidateCommentService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.candidateId = +params['id'];
      this.loadCandidateData();
    });
  }

  loadCandidateData(): void {
    this.isLoading = true;
    this.error = null;
    console.log('Loading candidate data for ID:', this.candidateId);

    this.interviewService.getCandidateById(this.candidateId).subscribe({
      next: (response) => {
        console.log('API Response:', response);

        if (response.success) {
          // Log the raw candidate data
          console.log('Raw candidate data from API:', response.candidate);

          // Transform backend field names to frontend expected names
          const candidate = response.candidate;
          console.log(
            'Original candidate object properties:',
            Object.keys(candidate)
          );

          this.candidate = {
            id: candidate.id,
            first_name: candidate.firstName,
            last_name: candidate.lastName,
            date_of_birth: candidate.dateOfBirth,
            gender_id: candidate.genderId,
            gender_name: candidate.gender_name,
            email: candidate.email,
            mobile_number: candidate.phoneNumber,
            alternate_phone: candidate.alternatePhone,

            // Address information
            current_address: candidate.address,
            permanent_address: candidate.permanentAddress,
            city: candidate.city,
            state: candidate.state,
            postal_code: candidate.postalCode,

            // ID information
            id_proof_type: candidate.idProofType,
            id_proof_number: candidate.idProofNumber,

            // Professional details
            total_experience: candidate.totalExperience,
            current_organization: candidate.currentOrganization,
            current_designation: candidate.currentDesignation,
            current_salary: candidate.currentSalary,
            expected_salary: candidate.expectedSalary,
            notice_period: candidate.noticePeriod,
            reason_for_change: candidate.reasonForChange,
            skills: candidate.skills,

            // Employment details
            employee_id: candidate.employeeId,
            hire_date: candidate.hireDate,
            department_id: candidate.departmentId,
            department_name: candidate.department_name,
            designation_id: candidate.designationId,
            designation_name: candidate.designation_name,
            employment_type_id: candidate.employmentTypeId,
            employment_type_name: candidate.employment_type_name,
            salary: candidate.salary,
            employment_status: candidate.employmentStatus,

            // Education
            highest_qualification: candidate.educationLevel,
            degrees: candidate.degrees,
            university: candidate.university,
            year_of_passing: candidate.yearOfPassing,
            specialization: candidate.specialization,
            additional_certifications: candidate.additionalCertifications,

            // Personal information
            blood_group_id: candidate.bloodGroupId,
            blood_group_name: candidate.blood_group_name,
            religion: candidate.religion,
            community: candidate.community,

            // Family information
            father_name: candidate.father_name,
            father_occupation: candidate.father_occupation,
            father_contact: candidate.father_contact,
            father_status: candidate.father_status,
            mother_name: candidate.mother_name,
            mother_occupation: candidate.mother_occupation,
            mother_contact: candidate.mother_contact,
            mother_status: candidate.mother_status,
            family_address: candidate.family_address,

            // Files and links
            resume_file: candidate.resumeFile,
            profile_picture: candidate.profilePicture,
            portfolio_links: candidate.portfolioLinks,
            reference_contacts: candidate.referenceContacts,

            // Status
            status: candidate.status,
            is_active: candidate.isActive,

            // Related data
            positions: candidate.positions || [],
            siblings: candidate.siblings || [],
          };

          // Log the transformed data
          console.log('Transformed candidate object:', this.candidate);

          // Log specific fields that might be problematic
          console.log('Fields after transformation:');
          console.log('First name:', this.candidate.first_name);
          console.log('Last name:', this.candidate.last_name);
          console.log('Experience:', this.candidate.total_experience);
          console.log('Notice period:', this.candidate.notice_period);
          console.log('Expected salary:', this.candidate.expected_salary);
          console.log('Resume file:', this.candidate.resume_file);

          this.loadPositionsApplied();
          this.loadCandidateComments(); // Load comments
        } else {
          console.error('Error in API response:', response.message);
          this.error = response.message || 'Failed to load candidate details';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error from API call:', error);
        this.error = error.message || 'Error loading candidate details';
        this.isLoading = false;
      },
    });
  }

  loadCandidateComments(): void {
    if (!this.candidateId) return;

    this.candidateCommentService
      .getCandidateComments(this.candidateId)
      .subscribe({
        next: (response) => {
          if (response.success) {
            // Reset the map
            this.commentsMap = new Map();

            // Process each comment and update the map
            response.comments.forEach((comment: CandidateComment) => {
              // If a field has multiple comments, we want to mark it as unread if ANY of the comments are unread
              const currentValue = this.commentsMap.get(
                comment.field_identifier
              );
              const hasUnreadComment =
                currentValue === true || !comment.is_read;

              this.commentsMap.set(comment.field_identifier, hasUnreadComment);
            });

            console.log('Comments loaded:', this.commentsMap);
          }
        },
        error: (error) => {
          console.error('Error loading comments:', error);
        },
      });
  }

  openCommentDialog(fieldIdentifier: string, fieldLabel: string): void {
    const dialogRef = this.dialog.open(CommentDialogComponent, {
      width: '500px',
      data: {
        candidateId: this.candidateId,
        fieldIdentifier: fieldIdentifier,
        fieldLabel: fieldLabel,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Reload comments after a successful save
        this.loadCandidateComments();
        this.snackBar.open('Comment saved successfully', 'Close', {
          duration: 3000,
        });
      }
    });
  }

  hasComments(fieldIdentifier: string): boolean {
    return this.commentsMap.has(fieldIdentifier);
  }

  hasUnreadComments(fieldIdentifier: string): boolean {
    return this.commentsMap.get(fieldIdentifier) === true;
  }

  getProfilePictureUrl(filename: string): string {
    if (!filename) return '';
    return this.interviewService.getProfilePictureUrl(filename);
  }

  loadPositionsApplied(): void {
    console.log('Loading positions for candidate ID:', this.candidateId);

    this.interviewService.getCandidatePositions(this.candidateId).subscribe({
      next: (response) => {
        console.log('Positions API Response:', response);

        if (response.success) {
          console.log('Raw positions data from API:', response.positions);

          // Log the first position object if available
          if (response.positions && response.positions.length > 0) {
            console.log(
              'First position object properties:',
              Object.keys(response.positions[0])
            );
            console.log('First position details:', response.positions[0]);
          } else {
            console.log('No positions found for this candidate');
          }

          this.positionsApplied = response.positions || [];

          // Add default values to ensure rendering works correctly
          this.positionsApplied = this.positionsApplied.map((position) => ({
            ...position,
            position_title: position.position_title || 'Unknown Position',
            department_name: position.department_name || 'No Department',
            application_date:
              position.application_date || new Date().toISOString(),
            status: position.status || 'Applied',
          }));

          console.log('Transformed positions data:', this.positionsApplied);
        } else {
          console.error('Failed to load positions:', response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading positions:', error);
        this.isLoading = false;
      },
    });
  }

  loadInterviewSessions(positionId: number): void {
    this.interviewService
      .getInterviewSessions(this.candidateId, positionId)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.interviewSessions = response.sessions;
          } else {
            console.error(
              'Failed to load interview sessions:',
              response.message
            );
          }
        },
        error: (error) => {
          console.error('Error loading interview sessions:', error);
        },
      });
  }

  editCandidate(): void {
    this.router.navigate(['/site/interview/candidates/edit', this.candidateId]);
  }

  viewPosition(positionId: number): void {
    this.router.navigate(['/site/interview/positions/view', positionId]);
  }

  downloadResume(): void {
    if (this.candidate?.resume_file) {
      const resumeUrl = this.interviewService.getResumeDownloadUrl(
        this.candidate.resume_file
      );
      window.open(resumeUrl, '_blank');
    } else {
      this.snackBar.open('No resume file available', 'Close', {
        duration: 3000,
      });
    }
  }

  addInterviewSession(positionId: number): void {
    this.router.navigate(['/site/interview/sessions/add'], {
      queryParams: {
        candidateId: this.candidateId,
        positionId: positionId,
      },
    });
  }

  updateCandidateStatus(status: string): void {
    this.interviewService
      .updateCandidateStatus(this.candidateId, status)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.candidate.status = status;
            this.snackBar.open(
              'Candidate status updated successfully',
              'Close',
              { duration: 3000 }
            );
          } else {
            this.snackBar.open(
              response.message || 'Failed to update status',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (error) => {
          this.snackBar.open('Error updating candidate status', 'Close', {
            duration: 3000,
          });
        },
      });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'New':
        return 'status-new';
      case 'In Process':
        return 'status-in-process';
      case 'Selected':
        return 'status-selected';
      case 'Rejected':
        return 'status-rejected';
      default:
        return '';
    }
  }

  getOutcomeClass(outcome: string): string {
    if (!outcome) return '';
    return 'outcome-' + outcome.toLowerCase().replace(/\s+/g, '-');
  }

  goBack(): void {
    this.router.navigate(['/site/interview/candidates']);
  }

  // Helper method to parse and display skills as an array
  getSkillsArray(): string[] {
    if (!this.candidate?.skills) return [];
    return this.candidate.skills
      .split(',')
      .map((skill: string) => skill.trim());
  }

  // Helper method for formatting dates consistently
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
}
