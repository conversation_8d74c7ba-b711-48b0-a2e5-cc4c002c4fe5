.candidates-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      margin-bottom: 24px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  
    .search-card {
      background-color: white;
      margin-bottom: 16px;
      
      .search-form-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        
        mat-form-field {
          flex: 1;
          min-width: 200px;
        }
      }
      
      .search-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 16px;
        
        button {
          display: flex;
          align-items: center;
          
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background-color: white;
      border-radius: 4px;
      
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  
    .table-container {
      background-color: white;
      border-radius: 4px;
      overflow: hidden;
      
      table {
        width: 100%;
        
        .mat-column-select {
          width: 60px;
        }
        
        .mat-column-actions {
          width: 140px;
          text-align: center;
        }
        
        tr.selected-row {
          background-color: rgba(63, 81, 181, 0.1);
        }
      }
    }
    
    .status-badge {
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-new {
      background-color: #e3f2fd;
      color: #0d47a1;
    }
    
    .status-in-process {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    .status-selected {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    
    .status-rejected {
      background-color: #ffebee;
      color: #b71c1c;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .candidates-container {
      .search-card {
        .search-form-row {
          flex-direction: column;
          gap: 0;
          
          mat-form-field {
            width: 100%;
          }
        }
        
        .search-actions {
          flex-direction: column;
          align-items: stretch;
        }
      }
      
      .table-container {
        overflow-x: auto;
      }
    }
  }