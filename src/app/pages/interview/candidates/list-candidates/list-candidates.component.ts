import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatMenuModule } from '@angular/material/menu';
import { SelectionModel } from '@angular/cdk/collections';
import { InterviewService } from '../../../../services/interview.service';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';

@Component({
  selector: 'app-list-candidates',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatMenuModule,
  ],
  templateUrl: './list-candidates.component.html',
  styleUrls: ['./list-candidates.component.scss'],
})
export class ListCandidatesComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'name',
    'email',
    'mobile',
    'experience',
    'status',
    'positions',
    'actions',
  ];
  dataSource = new MatTableDataSource<any>([]);
  selection = new SelectionModel<any>(true, []);

  searchForm!: FormGroup;
  loading = false;
  error = '';

  statusOptions = ['New', 'In Process', 'Selected', 'Rejected'];

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private fb: FormBuilder,
    private interviewService: InterviewService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initSearchForm();
    this.loadCandidates();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  initSearchForm(): void {
    this.searchForm = this.fb.group({
      name: [''],
      email: [''],
      mobile: [''],
      status: [''],
      min_experience: [''],
      skills: [''],
    });
  }

  loadCandidates(): void {
    this.loading = true;
    this.interviewService.getAllCandidates().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.candidates || [];
        } else {
          this.error = response.message || 'Failed to load candidates';
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading candidates:', err);
        this.error = 'Error loading candidates. Please try again later.';
        this.loading = false;
      },
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  onSearch(): void {
    const searchTerms = this.searchForm.value;

    // Remove empty values to avoid unnecessary filtering
    Object.keys(searchTerms).forEach((key) => {
      if (!searchTerms[key]) {
        delete searchTerms[key];
      }
    });

    if (Object.keys(searchTerms).length === 0) {
      this.loadCandidates();
      return;
    }

    this.loading = true;
    this.interviewService.searchCandidates(searchTerms).subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.candidates || [];
        } else {
          this.error = response.message || 'Failed to search candidates';
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error searching candidates:', err);
        this.error = 'Error searching candidates. Please try again later.';
        this.loading = false;
      },
    });
  }

  resetSearch(): void {
    this.searchForm.reset();
    this.loadCandidates();
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.data.forEach((row) => this.selection.select(row));
    }
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelectedCandidates();
      }
    });
  }

  deleteSelectedCandidates(): void {
    const ids = this.selection.selected.map((candidate) => candidate.id);

    this.interviewService.deleteCandidates(ids).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Candidates deleted successfully', 'Close', {
            duration: 3000,
          });
          this.selection.clear();
          this.loadCandidates();
        } else {
          this.snackBar.open(
            response.message || 'Failed to delete candidates',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (err) => {
        console.error('Error deleting candidates:', err);
        this.snackBar.open('Error deleting candidates', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  updateCandidateStatus(id: number, status: string): void {
    this.interviewService.updateCandidateStatus(id, status).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Candidate status updated successfully', 'Close', {
            duration: 3000,
          });
          this.loadCandidates();
        } else {
          this.snackBar.open(
            response.message || 'Failed to update candidate status',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (err) => {
        console.error('Error updating candidate status:', err);
        this.snackBar.open('Error updating candidate status', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'New':
        return 'status-new';
      case 'In Process':
        return 'status-in-process';
      case 'Selected':
        return 'status-selected';
      case 'Rejected':
        return 'status-rejected';
      default:
        return '';
    }
  }
}
