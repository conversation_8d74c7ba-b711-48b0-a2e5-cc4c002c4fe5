<div class="candidates-container">
    <div class="component-header">
      <h1 class="component-title">Candidates Management</h1>
    </div>
  
    <div class="content-wrapper">
      <!-- Search Form -->
      <mat-card class="search-card">
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
            <div class="search-form-row">
              <mat-form-field appearance="outline">
                <mat-label>Name</mat-label>
                <input matInput formControlName="name" placeholder="Candidate name">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" placeholder="Email address">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Mobile</mat-label>
                <input matInput formControlName="mobile" placeholder="Mobile number">
              </mat-form-field>
            </div>
  
            <div class="search-form-row">
              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status">
                  <mat-option value="">All</mat-option>
                  <mat-option *ngFor="let status of statusOptions" [value]="status">
                    {{status}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Min Experience (years)</mat-label>
                <input matInput type="number" formControlName="min_experience" placeholder="Minimum experience">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Skills</mat-label>
                <input matInput formControlName="skills" placeholder="Skills (e.g. Java, Angular)">
              </mat-form-field>
            </div>
  
            <div class="search-actions">
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon> Search
              </button>
              <button mat-raised-button type="button" (click)="resetSearch()">
                <mat-icon>clear</mat-icon> Reset
              </button>
              <button mat-raised-button color="accent" type="button" [routerLink]="['../candidates/add']">
                <mat-icon>person_add</mat-icon> Add Candidate
              </button>
              <button mat-raised-button color="warn" type="button" (click)="openDeleteConfirmationDialog()" [disabled]="!selection.hasValue()">
                <mat-icon>delete</mat-icon> Delete Selected
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
  
      <!-- Error message display -->
      <div *ngIf="error" class="error-message">
        {{error}}
        <button mat-button color="primary" (click)="loadCandidates()">Retry</button>
      </div>
      
      <!-- Loading indicator -->
      <div *ngIf="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading candidates...</span>
      </div>
      
      <!-- Candidates table -->
      <div class="table-container" *ngIf="!loading && !error">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)">
              </mat-checkbox>
            </td>
          </ng-container>
  
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let element">
              {{element.first_name}} {{element.last_name}}
            </td>
          </ng-container>
  
          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let element">{{element.email}}</td>
          </ng-container>
  
          <!-- Mobile Column -->
          <ng-container matColumnDef="mobile">
            <th mat-header-cell *matHeaderCellDef>Mobile</th>
            <td mat-cell *matCellDef="let element">{{element.mobile_number}}</td>
          </ng-container>
  
          <!-- Experience Column -->
          <ng-container matColumnDef="experience">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Experience</th>
            <td mat-cell *matCellDef="let element">{{element.total_experience || 'N/A'}} {{element.total_experience ? 'yrs' : ''}}</td>
          </ng-container>
  
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let element">
              <span class="status-badge" [ngClass]="getStatusClass(element.status)">
                {{element.status}}
              </span>
            </td>
          </ng-container>
  
          <!-- Positions Column -->
          <ng-container matColumnDef="positions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Applications</th>
            <td mat-cell *matCellDef="let element">{{element.position_count || 0}}</td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" [routerLink]="['../candidates/view', element.id]" matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent" [routerLink]="['../candidates/edit', element.id]" matTooltip="Edit Candidate">
                <mat-icon>edit</mat-icon>
              </button>
              <!-- <button mat-icon-button color="primary" (click)="sendInterviewCall(element)" matTooltip="Send Interview Call">
                <mat-icon>mail</mat-icon>
              </button> -->
              <button mat-icon-button [matMenuTriggerFor]="statusMenu" matTooltip="Update Status">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #statusMenu="matMenu">
                <button mat-menu-item *ngFor="let status of statusOptions" 
                        [disabled]="element.status === status"
                        (click)="updateCandidateStatus(element.id, status)">
                  {{status}}
                </button>
              </mat-menu>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              (click)="selection.toggle(row)"
              [class.selected-row]="selection.isSelected(row)">
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>