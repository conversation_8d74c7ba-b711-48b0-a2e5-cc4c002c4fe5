<!-- edit-candidate.component.html -->
<div class="edit-candidate-container">
  <div class="component-header">
    <div class="title-section">
      <button mat-icon-button color="primary" (click)="goBack()" matTooltip="Back to Candidates">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1 class="component-title">Edit Candidate</h1>
    </div>
    
    <div class="action-buttons">
      <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isSubmitting">
        <mat-icon>save</mat-icon> Save Changes
      </button>
      <button mat-raised-button color="warn" [routerLink]="['/site/interview/candidates/view', candidateId]" [disabled]="isSubmitting">
        <mat-icon>cancel</mat-icon> Cancel
      </button>
    </div>
  </div>

  <div class="content-wrapper">
    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="loading-container">
      <div class="spinner"></div>
      <p>Loading candidate data...</p>
    </div>
    
    <mat-card class="form-card" *ngIf="!isLoading">
      <mat-card-content>
        <form [formGroup]="candidateForm">
          <mat-stepper [linear]="isLinear" #stepper>
            <!-- Personal Information Step -->
            <mat-step [stepControl]="personalInfoForm" label="Personal Information">
              <div class="step-content" [formGroup]="personalInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>First Name*</mat-label>
                    <input matInput formControlName="first_name" required>
                    <mat-error *ngIf="personalInfoForm.get('first_name')?.hasError('required')">
                      First name is required
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Middle Name</mat-label>
                    <input matInput formControlName="middle_name">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Last Name*</mat-label>
                    <input matInput formControlName="last_name" required>
                    <mat-error *ngIf="personalInfoForm.get('last_name')?.hasError('required')">
                      Last name is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Date of Birth</mat-label>
                    <input matInput [matDatepicker]="dobPicker" formControlName="date_of_birth">
                    <mat-datepicker-toggle matSuffix [for]="dobPicker"></mat-datepicker-toggle>
                    <mat-datepicker #dobPicker></mat-datepicker>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Gender</mat-label>
                    <mat-select formControlName="gender_id">
                      <mat-option *ngFor="let gender of genders" [value]="gender.id">
                        {{ gender.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Email Address*</mat-label>
                    <input matInput formControlName="email" type="email" required>
                    <mat-error *ngIf="personalInfoForm.get('email')?.hasError('required')">
                      Email is required
                    </mat-error>
                    <mat-error *ngIf="personalInfoForm.get('email')?.hasError('email')">
                      Please enter a valid email address
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Mobile Number*</mat-label>
                    <input matInput formControlName="mobile_number" required>
                    <mat-error *ngIf="personalInfoForm.get('mobile_number')?.hasError('required')">
                      Mobile number is required
                    </mat-error>
                    <mat-error *ngIf="personalInfoForm.get('mobile_number')?.hasError('pattern')">
                      Please enter a valid 10-digit mobile number
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Alternate Phone</mat-label>
                    <input matInput formControlName="alternate_phone">
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Current Address</mat-label>
                    <textarea matInput formControlName="current_address" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Permanent Address</mat-label>
                    <textarea matInput formControlName="permanent_address" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>ID Proof Type</mat-label>
                    <mat-select formControlName="id_proof_type">
                      <mat-option value="Aadhar">Aadhar Card</mat-option>
                      <mat-option value="PAN">PAN Card</mat-option>
                      <mat-option value="Passport">Passport</mat-option>
                      <mat-option value="Driving License">Driving License</mat-option>
                      <mat-option value="Voter ID">Voter ID</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>ID Proof Number</mat-label>
                    <input matInput formControlName="id_proof_number">
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperNext color="primary">
                    Next <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Professional Information Step -->
            <mat-step [stepControl]="professionalInfoForm" label="Professional Information">
              <div class="step-content" [formGroup]="professionalInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Total Experience (Years)</mat-label>
                    <input matInput type="number" formControlName="total_experience" min="0" step="0.1">
                    <mat-error *ngIf="professionalInfoForm.get('total_experience')?.hasError('min')">
                      Experience cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Current Organization</mat-label>
                    <input matInput formControlName="current_organization">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Current Designation</mat-label>
                    <input matInput formControlName="current_designation">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Current Salary</mat-label>
                    <input matInput type="number" formControlName="current_salary" min="0">
                    <span matPrefix>Rs.&nbsp;</span>
                    <mat-error *ngIf="professionalInfoForm.get('current_salary')?.hasError('min')">
                      Salary cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Expected Salary</mat-label>
                    <input matInput type="number" formControlName="expected_salary" min="0">
                    <span matPrefix>Rs.&nbsp;</span>
                    <mat-error *ngIf="professionalInfoForm.get('expected_salary')?.hasError('min')">
                      Expected salary cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Notice Period (Days)</mat-label>
                    <input matInput type="number" formControlName="notice_period" min="0">
                    <mat-error *ngIf="professionalInfoForm.get('notice_period')?.hasError('min')">
                      Notice period cannot be negative
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Skills</mat-label>
                    <input matInput formControlName="skills" placeholder="E.g. JavaScript, Angular, Node.js, SQL">
                    <mat-hint>Enter skills separated by commas</mat-hint>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Highest Qualification</mat-label>
                    <input matInput formControlName="highest_qualification">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>University/Institution</mat-label>
                    <input matInput formControlName="university">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Year of Passing</mat-label>
                    <input matInput type="number" formControlName="year_of_passing" min="1900" [max]="currentYear">
                    <mat-error *ngIf="professionalInfoForm.get('year_of_passing')?.hasError('min') || professionalInfoForm.get('year_of_passing')?.hasError('max')">
                      Please enter a valid year
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Specialization</mat-label>
                    <input matInput formControlName="specialization">
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Additional Certifications</mat-label>
                    <textarea matInput formControlName="additional_certifications" rows="2"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Portfolio Links</mat-label>
                    <textarea matInput formControlName="portfolio_links" rows="2" placeholder="LinkedIn, GitHub, Portfolio Website, etc."></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Reason for Change</mat-label>
                    <textarea matInput formControlName="reason_for_change" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>References</mat-label>
                    <textarea matInput formControlName="reference_contacts" rows="3" placeholder="Name, Designation, Company, Contact Number"></textarea>
                    <mat-hint>Add multiple references separated by new lines</mat-hint>
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon> Back
                  </button>
                  <button mat-button matStepperNext color="primary">
                    Next <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Resume & Position Step -->
            <mat-step label="Resume & Positions">
              <div class="step-content">
              <!-- Resume Upload Section -->
              <div class="resume-upload-section">
                  <h3>Resume</h3>
                  <div class="file-upload-container">
                    <div class="file-upload-area" 
                         [ngClass]="{'has-file': resumeFileName}"
                         (click)="fileInput.click()">
                      <input hidden type="file" #fileInput (change)="onFileSelected($event)" accept=".pdf,.doc,.docx,.txt,.rtf">
                      <mat-icon>upload_file</mat-icon>
                      <ng-container *ngIf="!resumeFileName">
                        <span>Click to upload resume</span>
                      </ng-container>
                      <ng-container *ngIf="resumeFileName">
                        <span>{{resumeFileName}}</span>
                      </ng-container>
                    </div>
                    <ng-container *ngIf="resumeFileName">
                      <button mat-icon-button 
                              color="warn" 
                              (click)="removeFile()" 
                              matTooltip="Remove file">
                        <mat-icon>close</mat-icon>
                      </button>
                    </ng-container>
                  </div>
                  <p class="upload-hint">Allowed formats: PDF, DOC, DOCX, TXT, RTF. Max size: 10MB</p>
                </div>

                <!-- Current Positions Section -->
                <div class="positions-section">
                  <h3>Current Positions</h3>
                  
                  <div *ngIf="candidatePositions.length === 0" class="no-positions">
                    This candidate is not assigned to any positions.
                  </div>
                  
                  <div *ngIf="candidatePositions.length > 0" class="positions-list">
                    <div *ngFor="let position of candidatePositions" class="position-item">
                      <div class="position-details">
                        <div class="position-title">{{position.position_title}}</div>
                        <div class="position-info">
                          <span class="department">{{position.department_name}}</span>
                          <span class="date">Applied: {{position.application_date | date:'mediumDate'}}</span>
                          <span class="status-badge" [ngClass]="'status-' + position.status.toLowerCase()">{{position.status}}</span>
                        </div>
                      </div>
                      <div class="position-actions">
                        <button mat-icon-button color="warn" (click)="removePositionAssignment(position.position_id)" matTooltip="Remove Position">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Add New Position Section -->
                <div class="new-position-section" [formGroup]="newPositionForm">
                  <h3>Add New Position</h3>
                  
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="position-select">
                      <mat-label>Select Position</mat-label>
                      <mat-select formControlName="position_id">
                        <mat-option [value]="">None</mat-option>
                        <mat-option *ngFor="let position of positions" [value]="position.id">
                          {{position.title}} ({{position.department_name}})
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="date-field">
                      <mat-label>Application Date</mat-label>
                      <input matInput [matDatepicker]="appDatePicker" formControlName="application_date">
                      <mat-datepicker-toggle matSuffix [for]="appDatePicker"></mat-datepicker-toggle>
                      <mat-datepicker #appDatePicker></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon> Back
                  </button>
                  <button mat-raised-button color="primary" (click)="onSubmit()">
                    <mat-icon>save</mat-icon> Update Candidate
                  </button>
                </div>
              </div>
            </mat-step>
          </mat-stepper>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>