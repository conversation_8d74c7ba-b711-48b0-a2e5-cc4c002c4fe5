// edit-candidate.component.ts
import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormArray,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatDividerModule } from '@angular/material/divider';
import { MatStepperModule } from '@angular/material/stepper';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { InterviewService } from '../../../../services/interview.service';
import { MasterserviceService } from '../../../../services/masterservice.service';

@Component({
  selector: 'app-edit-candidate',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatRadioModule,
    MatDividerModule,
    MatStepperModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatSnackBarModule,
    MatTabsModule,
    MatTooltipModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './edit-candidate.component.html',
  styleUrls: ['./edit-candidate.component.scss'],
})
export class EditCandidateComponent implements OnInit {
  candidateId!: number;
  candidateForm!: FormGroup;
  candidatePositions: any[] = [];
  isLinear = true;
  isSubmitting = false;
  isLoading = true;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  currentYear = new Date().getFullYear();

  // Lists for dropdowns
  genders: any[] = [];
  positions: any[] = [];
  departments: any[] = [];

  // File handling properties
  resumeFile: File | null = null;
  resumeFileName: string = '';
  existingResumeFile: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private interviewService: InterviewService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadGenders();
    this.loadPositions();
    this.loadDepartments();

    this.route.params.subscribe((params) => {
      this.candidateId = +params['id'];
      this.loadCandidateData();
    });
  }

  initForm(): void {
    this.candidateForm = this.fb.group({
      // Personal Information
      personalInfo: this.fb.group({
        first_name: ['', [Validators.required]],
        middle_name: [''],
        last_name: ['', [Validators.required]],
        date_of_birth: [''],
        gender_id: [''],
        email: ['', [Validators.required, Validators.email]],
        mobile_number: [
          '',
          [Validators.required, Validators.pattern('^[0-9]{10}$')],
        ],
        alternate_phone: [''],
        current_address: [''],
        permanent_address: [''],
        id_proof_type: [''],
        id_proof_number: [''],
      }),

      // Professional Information
      professionalInfo: this.fb.group({
        total_experience: ['', [Validators.min(0)]],
        current_organization: [''],
        current_designation: [''],
        current_salary: ['', [Validators.min(0)]],
        expected_salary: ['', [Validators.min(0)]],
        notice_period: ['', [Validators.min(0)]],
        reason_for_change: [''],
        skills: [''],
        highest_qualification: [''],
        university: [''],
        year_of_passing: [
          '',
          [Validators.min(1900), Validators.max(this.currentYear)],
        ],
        specialization: [''],
        additional_certifications: [''],
        portfolio_links: [''],
        reference_contacts: [''],
      }),

      // Position Assignment
      newPosition: this.fb.group({
        position_id: [''],
        application_date: [new Date()],
      }),
    });
  }

  loadCandidateData(): void {
    this.isLoading = true;

    this.interviewService.getCandidateById(this.candidateId).subscribe({
      next: (response) => {
        if (response.success && response.candidate) {
          const candidate = response.candidate;

          // Transform backend field names to match frontend expectations
          this.personalInfoForm.patchValue({
            first_name: candidate.firstName,
            middle_name: candidate.middleName,
            last_name: candidate.lastName,
            gender_id: candidate.genderId,
            email: candidate.email,
            mobile_number: candidate.phoneNumber,
            alternate_phone: candidate.alternatePhone,
            current_address: candidate.address,
            permanent_address: candidate.permanentAddress,
            id_proof_type: candidate.idProofType,
            id_proof_number: candidate.idProofNumber,
          });

          // Handle date of birth (convert string to Date object)
          if (candidate.dateOfBirth) {
            this.personalInfoForm.patchValue({
              date_of_birth: new Date(candidate.dateOfBirth),
            });
          }

          // Set values for professional info
          this.professionalInfoForm.patchValue({
            total_experience: candidate.totalExperience,
            current_organization: candidate.currentOrganization,
            current_designation: candidate.currentDesignation,
            current_salary: candidate.currentSalary,
            expected_salary: candidate.expectedSalary,
            notice_period: candidate.noticePeriod,
            reason_for_change: candidate.reasonForChange,
            skills: candidate.skills,
            highest_qualification: candidate.educationLevel,
            university: candidate.university,
            year_of_passing: candidate.yearOfPassing,
            specialization: candidate.specialization,
            additional_certifications: candidate.additionalCertifications,
            portfolio_links: candidate.portfolioLinks,
            reference_contacts: candidate.referenceContacts,
          });

          // Store existing resume file info
          if (candidate.resumeFile) {
            this.existingResumeFile = candidate.resumeFile;
            this.resumeFileName =
              candidate.resumeFile.split('/').pop() || candidate.resumeFile;
          }

          // Load candidate positions
          this.loadCandidatePositions();
        } else {
          this.snackBar.open(
            response.message || 'Failed to load candidate data',
            'Close',
            { duration: 3000 }
          );
          this.router.navigate(['/site/interview/candidates']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading candidate:', error);
        this.snackBar.open(
          'Error loading candidate data. Please try again.',
          'Close',
          { duration: 3000 }
        );
        this.router.navigate(['/site/interview/candidates']);
        this.isLoading = false;
      },
    });
  }

  loadCandidatePositions(): void {
    this.interviewService.getCandidatePositions(this.candidateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.candidatePositions = response.positions || [];
        } else {
          console.error(
            'Failed to load candidate positions:',
            response.message
          );
        }
      },
      error: (error) => {
        console.error('Error loading candidate positions:', error);
      },
    });
  }

  loadGenders(): void {
    this.masterService.getAllGenders().subscribe({
      next: (response) => {
        if (response.success) {
          this.genders = response.genders;
        }
      },
      error: (error) => {
        console.error('Error loading genders:', error);
      },
    });
  }

  loadPositions(): void {
    this.interviewService.getOpenPositions().subscribe({
      next: (response) => {
        if (response.success) {
          this.positions = response.positions;
        }
      },
      error: (error) => {
        console.error('Error loading positions:', error);
      },
    });
  }

  loadDepartments(): void {
    this.masterService.getAllDepartments().subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.departments;
        }
      },
      error: (error) => {
        console.error('Error loading departments:', error);
      },
    });
  }

  get personalInfoForm(): FormGroup {
    return this.candidateForm.get('personalInfo') as FormGroup;
  }

  get professionalInfoForm(): FormGroup {
    return this.candidateForm.get('professionalInfo') as FormGroup;
  }

  get newPositionForm(): FormGroup {
    return this.candidateForm.get('newPosition') as FormGroup;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Accept only pdf, doc, docx, txt, rtf files
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const allowedExts = ['pdf', 'doc', 'docx', 'txt', 'rtf'];

      if (!allowedExts.includes(fileExt || '')) {
        this.snackBar.open(
          'Only document files (PDF, DOC, DOCX, TXT, RTF) are allowed!',
          'Close',
          {
            duration: 3000,
          }
        );
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        this.snackBar.open('File size exceeds 10MB limit!', 'Close', {
          duration: 3000,
        });
        return;
      }

      this.resumeFile = file;
      this.resumeFileName = file.name;
    }
  }

  removeFile(): void {
    this.resumeFile = null;
    this.resumeFileName = '';
    this.existingResumeFile = '';
  }

  onSubmit(): void {
    if (this.candidateForm.invalid) {
      this.markFormGroupTouched(this.candidateForm);
      this.snackBar.open(
        'Please fill in all required fields correctly.',
        'Close',
        {
          duration: 3000,
        }
      );
      return;
    }

    this.isSubmitting = true;

    // Combine form values from different form groups
    const personalInfoValues = { ...this.personalInfoForm.value };
    const professionalInfoValues = { ...this.professionalInfoForm.value };
    const newPositionValues = { ...this.newPositionForm.value };

    // Format the date_of_birth to YYYY-MM-DD format if it exists
    if (personalInfoValues.date_of_birth) {
      const date = new Date(personalInfoValues.date_of_birth);
      personalInfoValues.date_of_birth = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }

    // Create FormData for file upload
    const formData = new FormData();
    if (this.resumeFile) {
      formData.append('resumeFile', this.resumeFile);
    }

    // Map frontend field names to backend expected names
    if (personalInfoValues.first_name)
      formData.append('firstName', personalInfoValues.first_name);
    if (personalInfoValues.last_name)
      formData.append('lastName', personalInfoValues.last_name);
    if (personalInfoValues.date_of_birth)
      formData.append('dateOfBirth', personalInfoValues.date_of_birth);
    if (personalInfoValues.gender_id)
      formData.append('genderId', personalInfoValues.gender_id);
    if (personalInfoValues.email)
      formData.append('email', personalInfoValues.email);
    if (personalInfoValues.mobile_number)
      formData.append('phoneNumber', personalInfoValues.mobile_number);
    if (personalInfoValues.alternate_phone)
      formData.append('alternatePhone', personalInfoValues.alternate_phone);
    if (personalInfoValues.current_address)
      formData.append('address', personalInfoValues.current_address);
    if (personalInfoValues.permanent_address)
      formData.append('permanentAddress', personalInfoValues.permanent_address);
    if (personalInfoValues.id_proof_type)
      formData.append('idProofType', personalInfoValues.id_proof_type);
    if (personalInfoValues.id_proof_number)
      formData.append('idProofNumber', personalInfoValues.id_proof_number);

    // Map professional info to backend field names
    if (professionalInfoValues.total_experience)
      formData.append(
        'totalExperience',
        professionalInfoValues.total_experience
      );
    if (professionalInfoValues.current_organization)
      formData.append(
        'currentOrganization',
        professionalInfoValues.current_organization
      );
    if (professionalInfoValues.current_designation)
      formData.append(
        'currentDesignation',
        professionalInfoValues.current_designation
      );
    if (professionalInfoValues.current_salary)
      formData.append('currentSalary', professionalInfoValues.current_salary);
    if (professionalInfoValues.expected_salary)
      formData.append('expectedSalary', professionalInfoValues.expected_salary);
    if (professionalInfoValues.notice_period)
      formData.append('noticePeriod', professionalInfoValues.notice_period);
    if (professionalInfoValues.reason_for_change)
      formData.append(
        'reasonForChange',
        professionalInfoValues.reason_for_change
      );
    if (professionalInfoValues.skills)
      formData.append('skills', professionalInfoValues.skills);
    if (professionalInfoValues.highest_qualification)
      formData.append(
        'educationLevel',
        professionalInfoValues.highest_qualification
      );
    if (professionalInfoValues.university)
      formData.append('university', professionalInfoValues.university);
    if (professionalInfoValues.year_of_passing)
      formData.append('yearOfPassing', professionalInfoValues.year_of_passing);
    if (professionalInfoValues.specialization)
      formData.append('specialization', professionalInfoValues.specialization);
    if (professionalInfoValues.additional_certifications)
      formData.append(
        'additionalCertifications',
        professionalInfoValues.additional_certifications
      );
    if (professionalInfoValues.portfolio_links)
      formData.append('portfolioLinks', professionalInfoValues.portfolio_links);
    if (professionalInfoValues.reference_contacts)
      formData.append(
        'referenceContacts',
        professionalInfoValues.reference_contacts
      );

    // Update candidate data first
    this.interviewService
      .updateCandidate(this.candidateId, formData)
      .subscribe({
        next: (candidateResponse) => {
          if (candidateResponse.success) {
            // Check if we need to assign a new position
            if (newPositionValues.position_id) {
              // Format application date
              let formattedApplicationDate = new Date()
                .toISOString()
                .slice(0, 10); // Default to today
              if (newPositionValues.application_date) {
                const date = new Date(newPositionValues.application_date);
                formattedApplicationDate = `${date.getFullYear()}-${String(
                  date.getMonth() + 1
                ).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
              }

              // Create position assignment data
              const positionAssignment = {
                candidate_id: this.candidateId,
                position_id: newPositionValues.position_id,
                application_date: formattedApplicationDate,
                created_by: 1, // This would usually come from your auth service
              };

              // Assign candidate to the new position
              this.interviewService
                .assignCandidateToPosition(positionAssignment)
                .subscribe({
                  next: (positionResponse) => {
                    if (positionResponse.success) {
                      this.snackBar.open(
                        'Candidate updated with new position!',
                        'Close',
                        {
                          duration: 3000,
                        }
                      );
                      this.router.navigate([
                        '/site/interview/candidates/view',
                        this.candidateId,
                      ]);
                    } else {
                      this.snackBar.open(
                        'Candidate updated but position assignment failed.',
                        'Close',
                        {
                          duration: 3000,
                        }
                      );
                      this.router.navigate([
                        '/site/interview/candidates/view',
                        this.candidateId,
                      ]);
                    }
                  },
                  error: (error) => {
                    console.error('Position assignment error:', error);
                    this.snackBar.open(
                      'Candidate updated but position assignment failed.',
                      'Close',
                      {
                        duration: 3000,
                      }
                    );
                    this.router.navigate([
                      '/site/interview/candidates/view',
                      this.candidateId,
                    ]);
                  },
                  complete: () => {
                    this.isSubmitting = false;
                  },
                });
            } else {
              // No new position to assign, just show success message
              this.snackBar.open('Candidate updated successfully!', 'Close', {
                duration: 3000,
              });
              this.router.navigate([
                '/site/interview/candidates/view',
                this.candidateId,
              ]);
              this.isSubmitting = false;
            }
          } else {
            this.snackBar.open(
              candidateResponse.message || 'Failed to update candidate',
              'Close',
              {
                duration: 3000,
              }
            );
            this.isSubmitting = false;
          }
        },
        error: (error) => {
          console.error('Update candidate error:', error);
          let errorMessage = 'Error updating candidate. Please try again.';

          // Try to extract more specific error message
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
          });
          this.isSubmitting = false;
        },
      });
  }

  removePositionAssignment(positionId: number): void {
    if (confirm('Are you sure you want to remove this position assignment?')) {
      this.interviewService
        .removeCandidateFromPosition(this.candidateId, positionId)
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open(
                'Position assignment removed successfully',
                'Close',
                {
                  duration: 3000,
                }
              );
              this.loadCandidatePositions(); // Reload positions
            } else {
              this.snackBar.open(
                response.message || 'Failed to remove position assignment',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            console.error('Error removing position assignment:', error);
            this.snackBar.open('Error removing position assignment', 'Close', {
              duration: 3000,
            });
          },
        });
    }
  }

  // Helper function to mark all controls in a form group as touched
  markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/site/interview/candidates']);
  }
}
