// edit-candidate.component.scss
.edit-candidate-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .avatar-image {
      width: 100px !important;
      height: 100px;
      border-radius: 50%;
      object-fit: cover;
    }

    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
  
      .title-section {
        display: flex;
        align-items: center;
        gap: 10px;
  
        .component-title {
          font-size: 24px;
          color: #333;
          margin: 0;
        }
      }
  
      .action-buttons {
        display: flex;
        gap: 10px;
  
        button {
          display: flex;
          align-items: center;
  
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
  
    .content-wrapper {
      max-width: 900px;
      margin: 0 auto;
    }
  
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }
  
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
  
      p {
        font-size: 16px;
        color: #666;
      }
    }
  
    .form-card {
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
      .step-content {
        padding: 16px 8px;
        
        .form-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 16px;
          
          mat-form-field {
            flex: 1;
            min-width: 200px;
          }
          
          &.full-width {
            flex-direction: column;
            
            mat-form-field {
              width: 100%;
            }
          }
        }
        
        .step-actions {
          display: flex;
          justify-content: space-between;
          margin-top: 24px;
          
          button {
            display: flex;
            align-items: center;
            
            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
    
    .resume-upload-section {
      margin: 24px 0;
      
      h3 {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }
      
      .file-upload-container {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .file-upload-area {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          background-color: #f8f9fa;
          border: 2px dashed #ccc;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          flex: 1;
          
          &:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
          }
          
          &.has-file {
            border-color: #4caf50;
            background-color: #e8f5e9;
          }
          
          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
            color: #6c757d;
          }
          
          span {
            color: #495057;
            font-size: 14px;
          }
        }
      }
      
      .upload-hint {
        margin-top: 8px;
        font-size: 12px;
        color: #6c757d;
      }
    }
    
    .positions-section {
      margin: 24px 0;
      
      h3 {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }
      
      .no-positions {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        color: #6c757d;
        font-style: italic;
        text-align: center;
      }
      
      .positions-list {
        .position-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          margin-bottom: 8px;
          background-color: #f8f9fa;
          border-radius: 8px;
          border-left: 4px solid #3f51b5;
          
          .position-details {
            .position-title {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 4px;
              color: #333;
            }
            
            .position-info {
              display: flex;
              flex-wrap: wrap;
              gap: 16px;
              align-items: center;
              color: #6c757d;
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .new-position-section {
      margin: 24px 0;
      
      h3 {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }
      
      .form-row {
        display: flex;
        gap: 16px;
        
        .position-select {
          flex: 2;
        }
        
        .date-field {
          flex: 1;
        }
      }
    }
    
    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-applied {
      background-color: #e3f2fd;
      color: #0d47a1;
    }
    
    .status-in-process {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    .status-selected {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    
    .status-rejected {
      background-color: #ffebee;
      color: #b71c1c;
    }


  }


  
  // Responsive adjustments
  @media (max-width: 768px) {
    .edit-candidate-container {
      .component-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .action-buttons {
          width: 100%;
          justify-content: space-between;
        }
      }
      
      .form-card {
        .step-content {
          .form-row {
            flex-direction: column;
            gap: 0;
          }
          
          .step-actions {
            flex-direction: column;
            gap: 8px;
            
            button {
              width: 100%;
            }
          }
        }
      }
      
      .new-position-section {
        .form-row {
          flex-direction: column;
        }
      }
    }
  }