import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormArray,
} from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatDividerModule } from '@angular/material/divider';
import { MatStepperModule } from '@angular/material/stepper';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { InterviewService } from '../../../../services/interview.service';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { forkJoin } from 'rxjs';

interface Sibling {
  name: string;
  date_of_birth: string | Date;
  gender?: string;
  occupation?: string;
  marital_status?: string;
  contact?: string;
  status?: string;
  is_emergency_contact: boolean;
  additional_info?: string;
}

@Component({
  selector: 'app-add-candidate',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatRadioModule,
    MatDividerModule,
    MatStepperModule,
    MatChipsModule,
    MatAutocompleteModule,
    MatSnackBarModule,
    MatTabsModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './add-candidate.component.html',
  styleUrls: ['./add-candidate.component.scss'],
})
export class AddCandidateComponent implements OnInit {
  candidateForm!: FormGroup;
  personalInfoForm!: FormGroup;
  professionalInfoForm!: FormGroup;
  familyInfoForm!: FormGroup;
  positionInfoForm!: FormGroup;

  isLinear = true;
  isSubmitting = false;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  currentYear = new Date().getFullYear();

  // Lists for dropdowns
  genders: any[] = [];
  positions: any[] = [];
  departments: any[] = [];
  bloodGroups: any[] = [];
  religions: any[] = [];
  communities: any[] = [];
  states: any[] = [];
  cities: any[] = [];
  employmentTypes: any[] = [];
  designations: any[] = [];

  // Education levels
  educationLevels = [
    'High School',
    'Diploma',
    "Bachelor's Degree",
    "Master's Degree",
    'Ph.D.',
    'Other',
  ];

  // File handling properties
  resumeFile: File | null = null;
  resumeFileName: string = '';
  profilePictureFile: File | null = null;
  profilePictureFileName: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private interviewService: InterviewService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadMasterData();
  }

  initForm(): void {
    // Personal Information form group
    this.personalInfoForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      dateOfBirth: [null],
      genderId: [null],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      alternatePhone: ['', [Validators.pattern('^[0-9]{10}$')]],
      emergencyContactName: [''],
      emergencyContactNumber: ['', [Validators.pattern('^[0-9]{10}$')]],
      address: [''],
      permanentAddress: [''],
      city: [''],
      state: [''],
      postalCode: ['', [Validators.pattern('^[0-9]{6}$')]],
      idProofType: [''],
      idProofNumber: [''],
      bloodGroupId: [null],
      religion: [''],
      community: [''],
    });

    // Family Information form group
    this.familyInfoForm = this.fb.group({
      father_name: [''],
      father_occupation: [''],
      father_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      father_status: ['Living'],
      mother_name: [''],
      mother_occupation: [''],
      mother_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      mother_status: ['Living'],
      family_address: [''],
      siblings: this.fb.array([]),
    });

    // Professional Information form group
    this.professionalInfoForm = this.fb.group({
      totalExperience: [null, [Validators.min(0)]],
      currentOrganization: [''],
      currentDesignation: [''],
      currentSalary: [null, [Validators.min(0)]],
      expectedSalary: [null, [Validators.min(0)]],
      noticePeriod: [null, [Validators.min(0)]],
      reasonForChange: [''],
      skills: [''],
      educationLevel: [''],
      degrees: [''],
      university: [''],
      yearOfPassing: [
        null,
        [Validators.min(1900), Validators.max(this.currentYear)],
      ],
      specialization: [''],
      additionalCertifications: [''],
      referenceContacts: [''],
      portfolioLinks: [''],
      departmentId: [null],
      designationId: [null],
      employmentTypeId: [null],
      salary: [null, [Validators.min(0)]],
    });

    // Position Information form group
    this.positionInfoForm = this.fb.group({
      position_id: ['', Validators.required],
      status: ['New'],
      isActive: [true],
      employmentStatus: [''],
      employeeId: [''],
      hireDate: [null],
    });

    // Combine all form groups
    this.candidateForm = this.fb.group({
      personalInfo: this.personalInfoForm,
      familyInfo: this.familyInfoForm,
      professionalInfo: this.professionalInfoForm,
      positionInfo: this.positionInfoForm,
    });
  }

  loadMasterData(): void {
    this.isSubmitting = true;

    // Load genders separately to make sure it doesn't fail with other requests
    this.masterService.getAllGenders().subscribe({
      next: (response) => {
        console.log('Genders response:', response);
        if (response && response.success && response.genders) {
          this.genders = response.genders;
        } else {
          console.error('Unexpected genders response structure:', response);
          this.genders = [];
        }
      },
      error: (error) => {
        console.error('Error loading genders:', error);
        this.genders = [];
      },
    });

    // Load blood groups
    this.masterService.getAllBloodGroups().subscribe({
      next: (response) => {
        if (response && response.success && response.bloodGroups) {
          this.bloodGroups = response.bloodGroups;
        } else {
          this.bloodGroups = [];
        }
      },
      error: (error) => {
        console.error('Error loading blood groups:', error);
        this.bloodGroups = [];
      },
    });

    // Load departments
    this.masterService.getAllDepartments().subscribe({
      next: (response) => {
        if (response && response.success && response.departments) {
          this.departments = response.departments;
        } else {
          this.departments = [];
        }
      },
      error: (error) => {
        console.error('Error loading departments:', error);
        this.departments = [];
      },
    });

    // Load designations
    this.masterService.getAllDesignations().subscribe({
      next: (response) => {
        if (response && response.success && response.designations) {
          this.designations = response.designations;
        } else {
          this.designations = [];
        }
      },
      error: (error) => {
        console.error('Error loading designations:', error);
        this.designations = [];
      },
    });

    // Load employment types
    this.masterService.getAllEmploymentTypes().subscribe({
      next: (response) => {
        if (response && response.success && response.employmentTypes) {
          this.employmentTypes = response.employmentTypes;
        } else {
          this.employmentTypes = [];
        }
      },
      error: (error) => {
        console.error('Error loading employment types:', error);
        this.employmentTypes = [];
      },
    });

    // Load religions
    this.masterService.getAllReligions().subscribe({
      next: (response) => {
        if (response && response.success && response.religions) {
          this.religions = response.religions;
        } else {
          this.religions = [];
        }
      },
      error: (error) => {
        console.error('Error loading religions:', error);
        this.religions = [];
      },
    });

    // Load communities
    this.masterService.getAllCommunities().subscribe({
      next: (response) => {
        if (response && response.success && response.communities) {
          this.communities = response.communities;
        } else {
          this.communities = [];
        }
      },
      error: (error) => {
        console.error('Error loading communities:', error);
        this.communities = [];
      },
    });

    // Load states
    this.masterService.getAllStates().subscribe({
      next: (response) => {
        if (response && response.success && response.states) {
          this.states = response.states;
        } else {
          this.states = [];
        }
      },
      error: (error) => {
        console.error('Error loading states:', error);
        this.states = [];
      },
    });

    // Positions might be failing, so let's handle it separately
    this.interviewService.getOpenPositions().subscribe({
      next: (response) => {
        if (response && response.success && response.positions) {
          this.positions = response.positions;
        } else {
          this.positions = [];
        }
      },
      error: (error) => {
        console.error('Error loading positions:', error);
        this.positions = [];
        // Even if positions fail, we can use a dummy position for testing
        this.positions = [
          {
            id: 0,
            title: 'Dummy Position (API Error)',
            department_name: 'Test',
          },
        ];
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }

  onStateChange(event: any): void {
    const stateId = event.value;
    if (stateId) {
      this.loadCitiesByState(stateId);
      this.personalInfoForm.get('city')?.enable();
    } else {
      this.cities = [];
      this.personalInfoForm.get('city')?.disable();
      this.personalInfoForm.get('city')?.setValue(null);
    }
  }

  loadCitiesByState(stateId: number): void {
    this.masterService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        this.cities = response.cities;
      },
      error: (error) => {
        console.error('Error loading cities:', error);
      },
    });
  }

  // Helper method to get siblings FormArray
  get siblings(): FormArray {
    return this.familyInfoForm.get('siblings') as FormArray;
  }

  // Add a new sibling to the FormArray
  addSibling(): void {
    const siblingForm = this.fb.group({
      name: ['', Validators.required],
      date_of_birth: [null, Validators.required],
      gender: [''],
      occupation: [''],
      marital_status: ['Single'],
      contact: ['', Validators.pattern('^[0-9]{10}$')],
      status: ['Living'],
      is_emergency_contact: [false],
      additional_info: [''],
    });

    this.siblings.push(siblingForm);
  }

  // Remove a sibling from the FormArray
  removeSibling(index: number): void {
    this.siblings.removeAt(index);
  }

  // Helper to get a specific control from a sibling form
  getSiblingControl(index: number, controlName: string) {
    return this.siblings.at(index).get(controlName);
  }

  // File handling methods
  onResumeSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Accept only pdf, doc, docx, txt, rtf files
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const allowedExts = ['pdf', 'doc', 'docx', 'txt', 'rtf'];

      if (!allowedExts.includes(fileExt || '')) {
        this.snackBar.open(
          'Only document files (PDF, DOC, DOCX, TXT, RTF) are allowed!',
          'Close',
          {
            duration: 3000,
          }
        );
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        this.snackBar.open('File size exceeds 10MB limit!', 'Close', {
          duration: 3000,
        });
        return;
      }

      this.resumeFile = file;
      this.resumeFileName = file.name;
    }
  }

  removeResume(): void {
    this.resumeFile = null;
    this.resumeFileName = '';
  }

  onProfilePictureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Accept only image files
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const allowedExts = ['jpg', 'jpeg', 'png'];

      if (!allowedExts.includes(fileExt || '')) {
        this.snackBar.open(
          'Only image files (JPG, JPEG, PNG) are allowed!',
          'Close',
          {
            duration: 3000,
          }
        );
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        this.snackBar.open('File size exceeds 5MB limit!', 'Close', {
          duration: 3000,
        });
        return;
      }

      this.profilePictureFile = file;
      this.profilePictureFileName = file.name;
    }
  }

  removeProfilePicture(): void {
    this.profilePictureFile = null;
    this.profilePictureFileName = '';
  }

  formatDate(date: Date | null): string | null {
    if (!date) return null;
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
      2,
      '0'
    )}-${String(d.getDate()).padStart(2, '0')}`;
  }

  // Form submission
  onSubmit(): void {
    console.log('Form submission started');
    console.log('Form valid status:', this.candidateForm.valid);

    // Check form validity
    if (this.candidateForm.invalid) {
      console.log('Form is invalid. Marking all fields as touched.');
      this.markFormGroupTouched(this.candidateForm);

      // Log specific validation errors
      this.logFormValidationErrors(this.candidateForm);

      this.snackBar.open(
        'Please fill in all required fields correctly.',
        'Close',
        {
          duration: 3000,
        }
      );
      return;
    }

    this.isSubmitting = true;
    console.log('Submission in progress...');

    // Get form values from each form group
    const personalInfo = this.personalInfoForm.value;
    const familyInfo = this.familyInfoForm.value;
    const professionalInfo = this.professionalInfoForm.value;
    const positionInfo = this.positionInfoForm.value;

    console.log('Personal Info:', personalInfo);
    console.log('Family Info:', familyInfo);
    console.log('Professional Info:', professionalInfo);
    console.log('Position Info:', positionInfo);

    // Create FormData for file upload
    const formData = new FormData();

    // Add files if selected
    if (this.resumeFile) {
      console.log(
        'Adding resume file:',
        this.resumeFile.name,
        this.resumeFile.size
      );
      formData.append('resumeFile', this.resumeFile);
    }

    if (this.profilePictureFile) {
      console.log(
        'Adding profile picture:',
        this.profilePictureFile.name,
        this.profilePictureFile.size
      );
      formData.append('profilePicture', this.profilePictureFile);
    }

    // Format dates
    let formattedDob = null;
    if (personalInfo.dateOfBirth) {
      const date = new Date(personalInfo.dateOfBirth);
      formattedDob = date.toISOString().split('T')[0];
      console.log('Formatted DOB:', formattedDob);
    }

    let formattedHireDate = null;
    if (positionInfo.hireDate) {
      const date = new Date(positionInfo.hireDate);
      formattedHireDate = date.toISOString().split('T')[0];
      console.log('Formatted hire date:', formattedHireDate);
    }

    // Combine all form data
    const candidateData = {
      ...personalInfo,
      dateOfBirth: formattedDob,
      ...professionalInfo,
      ...positionInfo,
      hireDate: formattedHireDate,
      position_id: positionInfo.position_id,
      isActive: positionInfo.isActive ? 1 : 0,
      // Family info except siblings
      father_name: familyInfo.father_name,
      father_occupation: familyInfo.father_occupation,
      father_contact: familyInfo.father_contact,
      father_status: familyInfo.father_status,
      mother_name: familyInfo.mother_name,
      mother_occupation: familyInfo.mother_occupation,
      mother_contact: familyInfo.mother_contact,
      mother_status: familyInfo.mother_status,
      family_address: familyInfo.family_address,
    };

    console.log('Combined candidate data:', candidateData);

    // Add all candidate data to FormData
    Object.keys(candidateData).forEach((key) => {
      if (candidateData[key] !== null && candidateData[key] !== undefined) {
        formData.append(key, candidateData[key]);
        console.log(`Adding field: ${key} = ${candidateData[key]}`);
      }
    });

    // Handle siblings array
    console.log('Checking siblings...');
    const siblings = this.siblings.value;
    console.log('Raw siblings form value:', siblings);
    console.log('Siblings length:', siblings.length);

    if (siblings && siblings.length > 0) {
      // Format siblings
      const formattedSiblings = siblings.map(
        (sibling: Sibling, index: number) => {
          console.log(`Processing sibling ${index + 1}:`, sibling);

          // Format date correctly
          let siblingDob = null;
          if (sibling.date_of_birth) {
            if (sibling.date_of_birth instanceof Date) {
              siblingDob = sibling.date_of_birth.toISOString().split('T')[0];
            } else {
              // Handle if it's already a string
              const date = new Date(sibling.date_of_birth);
              siblingDob = date.toISOString().split('T')[0];
            }
            console.log(`Sibling ${index + 1} DOB:`, siblingDob);
          } else {
            console.warn(`Sibling ${index + 1} missing DOB!`);
          }

          // Convert boolean to number for is_emergency_contact
          const isEmergencyContact = sibling.is_emergency_contact ? 1 : 0;

          return {
            name: sibling.name,
            date_of_birth: siblingDob,
            gender: sibling.gender || '',
            occupation: sibling.occupation || '',
            marital_status: sibling.marital_status || 'Single',
            contact: sibling.contact || '',
            status: sibling.status || 'Living',
            is_emergency_contact: isEmergencyContact,
            additional_info: sibling.additional_info || '',
          };
        }
      );

      // Log the formatted siblings data for debugging
      console.log('Formatted siblings data:', formattedSiblings);

      // Convert to JSON string and add to FormData
      const siblingsJson = JSON.stringify(formattedSiblings);
      console.log('Siblings JSON to send:', siblingsJson);
      formData.append('siblings', siblingsJson);
    } else {
      console.log('No siblings to add');
    }

    // Submit the form
    console.log('Submitting candidate data to API');
    this.interviewService.addCandidate(formData).subscribe({
      next: (response) => {
        console.log('API response:', response);
        if (response.success) {
          this.snackBar.open('Candidate added successfully!', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['/site/interview/candidates']);
        } else {
          console.error('API returned success: false', response);
          this.snackBar.open(
            response.message || 'Failed to add candidate',
            'Close',
            {
              duration: 3000,
            }
          );
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error adding candidate:', error);
        console.error('Error details:', error.error);

        let errorMessage = 'Error adding candidate';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.snackBar.open(errorMessage, 'Close', {
          duration: 5000,
        });
        this.isSubmitting = false;
      },
    });
  }

  // Helper method to log validation errors
  private logFormValidationErrors(formGroup: FormGroup | FormArray): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.logFormValidationErrors(control);
      } else {
        if (control?.errors) {
          console.error(`Validation error in field '${key}':`, control.errors);
        }
      }
    });
  }

  // Add this to your component
  fillTestData(): void {
    // Current date for calculations
    const today = new Date();
    const pastDate = new Date();
    pastDate.setFullYear(today.getFullYear() - 25); // 25 years ago

    // Populate personal info
    this.personalInfoForm.patchValue({
      firstName: 'Test',
      lastName: 'Candidate',
      dateOfBirth: pastDate,
      genderId: 5, // Assuming 5 is Male in your system
      email: `test.candidate${Date.now()}@example.com`, // Add timestamp for uniqueness
      phoneNumber: '9876543210',
      alternatePhone: '8765432109',
      emergencyContactName: 'Emergency Contact',
      emergencyContactNumber: '7654321098',
      address: '123 Test Street, Test City',
      permanentAddress: '123 Test Street, Test City',
      city: 51, // Use an ID from your database
      state: 35, // Use an ID from your database
      postalCode: '641605',
      idProofType: 'Aadhar',
      idProofNumber: '123456789012',
      bloodGroupId: 23, // Use an ID from your database
    });

    // Populate family info
    this.familyInfoForm.patchValue({
      father_name: 'Father Name',
      father_occupation: 'Father Occupation',
      father_contact: '9876543211',
      father_status: 'Living',
      mother_name: 'Mother Name',
      mother_occupation: 'Mother Occupation',
      mother_contact: '9876543212',
      mother_status: 'Living',
      family_address: '123 Family Address, Test City',
    });

    // Add a test sibling
    this.addTestSibling();

    // Populate professional info
    this.professionalInfoForm.patchValue({
      totalExperience: 3.5,
      currentOrganization: 'Current Organization',
      currentDesignation: 'Current Designation',
      currentSalary: 25000,
      expectedSalary: 30000,
      noticePeriod: 30,
      reasonForChange: 'Career growth',
      skills: 'Skill 1, Skill 2, Skill 3',
      educationLevel: "Bachelor's Degree",
      degrees: 'Test Degree',
      university: 'Test University',
      yearOfPassing: today.getFullYear() - 5,
      specialization: 'Test Specialization',
      additionalCertifications: 'Test Certification',
    });

    // Populate position info
    this.positionInfoForm.patchValue({
      position_id: 4, // Use an ID from your positions list
      status: 'New',
      isActive: true,
    });

    // Show a message
    this.snackBar.open('Form filled with test data!', 'Close', {
      duration: 2000,
    });
  }

  // Helper method to add a test sibling
  addTestSibling(): void {
    const siblingDate = new Date();
    siblingDate.setFullYear(siblingDate.getFullYear() - 20); // 20 years ago

    const siblingForm = this.fb.group({
      name: ['Test Sibling', Validators.required],
      date_of_birth: [siblingDate, Validators.required],
      gender: ['Male'],
      occupation: ['Student'],
      marital_status: ['Single'],
      contact: ['9876543213'],
      status: ['Living'],
      is_emergency_contact: [false],
      additional_info: ['Test sibling information'],
    });

    this.siblings.push(siblingForm);
  }

  // Mark all form controls as touched to trigger validation
  markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      }
    });
  }

  resetForm(): void {
    this.candidateForm.reset();
    this.personalInfoForm.reset();
    this.familyInfoForm.reset();
    this.professionalInfoForm.reset();
    this.positionInfoForm.reset();

    // Reset siblings array
    while (this.siblings.length > 0) {
      this.siblings.removeAt(0);
    }

    // Reset default values
    this.familyInfoForm.patchValue({
      father_status: 'Living',
      mother_status: 'Living',
    });

    this.positionInfoForm.patchValue({
      status: 'New',
      isActive: true,
    });

    // Reset file uploads
    this.resumeFile = null;
    this.resumeFileName = '';
    this.profilePictureFile = null;
    this.profilePictureFileName = '';
  }

  onFileSelected(event: any): void {
    // This is just an alias to maintain compatibility with the existing HTML
    this.onResumeSelected(event);
  }

  removeFile(): void {
    // This is just an alias to maintain compatibility with the existing HTML
    this.removeResume();
  }

  goBack(): void {
    this.router.navigate(['/site/interview/candidates']);
  }
}
