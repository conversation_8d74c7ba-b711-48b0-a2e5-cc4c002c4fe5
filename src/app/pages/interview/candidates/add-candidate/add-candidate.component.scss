.add-candidate-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;

  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    width: 50%;  // Match the form width

    .title-section {
      display: flex;
      align-items: center;
      gap: 10px;

      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      button {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 50%;  // Set width to 50%
    margin: 0 auto;  // Center horizontally

    .form-card {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      background-color: white;
      width: 100%;  // Take full width of parent

      mat-card-content {
        padding: 0;
      }
    }
  }

  .step-content {
    padding: 24px;
    
    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 16px;
      
      &.full-width {
        flex-direction: column;
      }
      
      mat-form-field {
        flex: 1;
        min-width: 200px;
        
        &.short-field {
          flex: 0 0 150px;
        }
      }

      .checkbox-field {
        display: flex;
        align-items: center;
        min-height: 56px; // Match form field height
        padding: 0 16px;
      }
    }

    .siblings-section {
      margin: 24px 0;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        color: #333;
        margin-bottom: 16px;

        mat-icon {
          color: #3f51b5;
        }
      }
    }
    
    .sibling-form {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    
      .sibling-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    
        h4 {
          margin: 0;
          color: #3f51b5;
        }
      }
    }
    
    .file-upload-section {
      margin: 20px 0;
      
      h3 {
        margin-bottom: 16px;
        font-size: 18px;
        color: #333;
      }
      
      .file-upload-container {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .file-upload-area {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px dashed #ccc;
          border-radius: 4px;
          padding: 30px;
          cursor: pointer;
          transition: all 0.3s ease;
          flex: 1;
          background-color: #f9f9f9;
          min-height: 100px;
          
          &:hover {
            border-color: #3f51b5;
            background-color: #f0f0f0;
          }
          
          &.has-file {
            border-color: #4caf50;
            background-color: #f0f8f0;
          }
          
          mat-icon {
            font-size: 36px;
            width: 36px;
            height: 36px;
            margin-bottom: 10px;
            color: #757575;
          }
          
          span {
            text-align: center;
            color: #666;
          }
        }
      }
      
      .upload-hint {
        margin-top: 10px;
        font-size: 12px;
        color: #757575;
        font-style: italic;
      }
    }
    
    .step-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      
      button {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  // Loading state
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    
    .loading-content {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      text-align: center;
      
      mat-spinner {
        margin: 0 auto 15px;
      }
      
      p {
        margin: 0;
        color: #333;
      }
    }
  }

  // Responsive design
  @media (max-width: 1200px) {
    .component-header,
    .content-wrapper {
      width: 70%;  // Increase width on medium screens
    }
  }

  @media (max-width: 768px) {
    padding: 10px;
    
    .component-header,
    .content-wrapper {
      width: 95%;  // Almost full width on mobile
    }
    
    .component-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
      
      .action-buttons {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .step-content {
      padding: 16px;
      
      .form-row {
        flex-direction: column;
        
        mat-form-field {
          width: 100%;
        }
      }
      
      .step-actions {
        flex-wrap: wrap;
        gap: 10px;
      }
    }
  }
}

// Stepper customization
::ng-deep .mat-horizontal-stepper-header-container {
  margin-bottom: 10px;
}

::ng-deep .mat-horizontal-stepper-header {
  height: 72px;
}

::ng-deep .mat-stepper-horizontal-line {
  margin: 0 8px;
}

::ng-deep .mat-step-header .mat-step-icon-selected {
  background-color: #3f51b5;
}

::ng-deep .mat-step-header .mat-step-label.mat-step-label-active {
  color: #3f51b5;
}