<div class="add-candidate-container">
  <div class="component-header">
    <div class="title-section">
      <button mat-icon-button color="primary" (click)="goBack()" matTooltip="Back to Candidates">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1 class="component-title">Add New Candidate</h1>
    </div>
    
    <div class="action-buttons">
      <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isSubmitting">
        <mat-icon>save</mat-icon> Save Candidate
      </button>
      <button mat-raised-button color="warn" (click)="resetForm()" [disabled]="isSubmitting">
        <mat-icon>clear</mat-icon> Reset Form
      </button>
      <button type="button" mat-raised-button color="accent" (click)="fillTestData()">
        <mat-icon>auto_fix_high</mat-icon> Fill Test Data
      </button>
    </div>
  </div>

  <div class="content-wrapper">
    <mat-card class="form-card">
      <mat-card-content>
        <form [formGroup]="candidateForm">
          <mat-stepper [linear]="isLinear" #stepper>
            <!-- Personal Information Step -->
            <mat-step [stepControl]="personalInfoForm" label="Personal Information">
              <div class="step-content" [formGroup]="personalInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>First Name*</mat-label>
                    <input matInput formControlName="firstName" required>
                    <mat-error *ngIf="personalInfoForm.get('firstName')?.hasError('required')">
                      First name is required
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Last Name*</mat-label>
                    <input matInput formControlName="lastName" required>
                    <mat-error *ngIf="personalInfoForm.get('lastName')?.hasError('required')">
                      Last name is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Date of Birth</mat-label>
                    <input matInput [matDatepicker]="dobPicker" formControlName="dateOfBirth">
                    <mat-datepicker-toggle matSuffix [for]="dobPicker"></mat-datepicker-toggle>
                    <mat-datepicker #dobPicker></mat-datepicker>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Gender</mat-label>
                    <mat-select formControlName="genderId">
                      <mat-option *ngFor="let gender of genders" [value]="gender.id">
                        {{ gender.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Email Address*</mat-label>
                    <input matInput formControlName="email" type="email" required>
                    <mat-error *ngIf="personalInfoForm.get('email')?.hasError('required')">
                      Email is required
                    </mat-error>
                    <mat-error *ngIf="personalInfoForm.get('email')?.hasError('email')">
                      Please enter a valid email address
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Phone Number*</mat-label>
                    <input matInput formControlName="phoneNumber" required>
                    <mat-error *ngIf="personalInfoForm.get('phoneNumber')?.hasError('required')">
                      Phone number is required
                    </mat-error>
                    <mat-error *ngIf="personalInfoForm.get('phoneNumber')?.hasError('pattern')">
                      Please enter a valid 10-digit phone number
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Alternate Phone</mat-label>
                    <input matInput formControlName="alternatePhone">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Emergency Contact Name</mat-label>
                    <input matInput formControlName="emergencyContactName">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Emergency Contact Number</mat-label>
                    <input matInput formControlName="emergencyContactNumber">
                    <mat-error *ngIf="personalInfoForm.get('emergencyContactNumber')?.hasError('pattern')">
                      Please enter a valid 10-digit number
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Address</mat-label>
                    <textarea matInput formControlName="address" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Permanent Address</mat-label>
                    <textarea matInput formControlName="permanentAddress" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row">

                  <mat-form-field appearance="outline">
                    <mat-label>State</mat-label>
                    <mat-select formControlName="state" (selectionChange)="onStateChange($event)">
                      <mat-option *ngFor="let state of states" [value]="state.id">
                        {{state.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>


                  <mat-form-field appearance="outline">
                    <mat-label>City</mat-label>
                    <mat-select formControlName="city" [disabled]="!personalInfoForm.get('state')?.value">
                      <mat-option *ngFor="let city of cities" [value]="city.id">
                        {{city.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

            
                

                  <mat-form-field appearance="outline">
                    <mat-label>Postal Code</mat-label>
                    <input matInput formControlName="postalCode">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>ID Proof Type</mat-label>
                    <mat-select formControlName="idProofType">
                      <mat-option value="Aadhar">Aadhar Card</mat-option>
                      <mat-option value="PAN">PAN Card</mat-option>
                      <mat-option value="Passport">Passport</mat-option>
                      <mat-option value="Driving License">Driving License</mat-option>
                      <mat-option value="Voter ID">Voter ID</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>ID Proof Number</mat-label>
                    <input matInput formControlName="idProofNumber">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Blood Group</mat-label>
                    <mat-select formControlName="bloodGroupId">
                      <mat-option *ngFor="let bg of bloodGroups" [value]="bg.id">
                        {{bg.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Religion</mat-label>
                    <mat-select formControlName="religion">
                      <mat-option *ngFor="let religion of religions" [value]="religion.id">
                        {{religion.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Community</mat-label>
                    <mat-select formControlName="community">
                      <mat-option *ngFor="let community of communities" [value]="community.id">
                        {{community.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperNext color="primary">
                    Next <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Family Information Step -->
            <mat-step [stepControl]="familyInfoForm" label="Family Information">
              <div class="step-content" [formGroup]="familyInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Father's Name</mat-label>
                    <input matInput formControlName="father_name">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Father's Occupation</mat-label>
                    <input matInput formControlName="father_occupation">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Father's Contact</mat-label>
                    <input matInput formControlName="father_contact">
                    <mat-error *ngIf="familyInfoForm.get('father_contact')?.hasError('pattern')">
                      Please enter a valid 10-digit number
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Father's Status</mat-label>
                    <mat-select formControlName="father_status">
                      <mat-option value="Living">Living</mat-option>
                      <mat-option value="Deceased">Deceased</mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Mother's Name</mat-label>
                    <input matInput formControlName="mother_name">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Mother's Occupation</mat-label>
                    <input matInput formControlName="mother_occupation">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Mother's Contact</mat-label>
                    <input matInput formControlName="mother_contact">
                    <mat-error *ngIf="familyInfoForm.get('mother_contact')?.hasError('pattern')">
                      Please enter a valid 10-digit number
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Mother's Status</mat-label>
                    <mat-select formControlName="mother_status">
                      <mat-option value="Living">Living</mat-option>
                      <mat-option value="Deceased">Deceased</mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Family Address</mat-label>
                    <textarea matInput formControlName="family_address" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <!-- Siblings Section -->
                <div class="siblings-section">
                  <h3 class="section-title">
                    <mat-icon>people</mat-icon>
                    Siblings
                  </h3>

                  <div formArrayName="siblings">
                    <div *ngFor="let sibling of siblings.controls; let i=index" [formGroupName]="i" class="sibling-form">
                      <div class="sibling-header">
                        <h4>Sibling {{i + 1}}</h4>
                        <button mat-icon-button color="warn" type="button" (click)="removeSibling(i)">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Name</mat-label>
                          <input matInput formControlName="name" required>
                          <mat-error *ngIf="getSiblingControl(i, 'name')?.hasError('required')">Name is required</mat-error>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Date of Birth</mat-label>
                          <input matInput [matDatepicker]="siblingDob" formControlName="date_of_birth" required>
                          <mat-datepicker-toggle matSuffix [for]="siblingDob"></mat-datepicker-toggle>
                          <mat-datepicker #siblingDob></mat-datepicker>
                          <mat-error *ngIf="getSiblingControl(i, 'date_of_birth')?.hasError('required')">Date of birth is required</mat-error>
                        </mat-form-field>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Gender</mat-label>
                          <mat-select formControlName="gender">
                            <mat-option value="Male">Male</mat-option>
                            <mat-option value="Female">Female</mat-option>
                            <mat-option value="Other">Other</mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Occupation</mat-label>
                          <input matInput formControlName="occupation">
                        </mat-form-field>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Marital Status</mat-label>
                          <mat-select formControlName="marital_status">
                            <mat-option value="Single">Single</mat-option>
                            <mat-option value="Married">Married</mat-option>
                            <mat-option value="Divorced">Divorced</mat-option>
                            <mat-option value="Widowed">Widowed</mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>Contact</mat-label>
                          <input matInput formControlName="contact">
                        </mat-form-field>
                      </div>

                      <div class="form-row">
                        <mat-form-field appearance="outline">
                          <mat-label>Status</mat-label>
                          <mat-select formControlName="status">
                            <mat-option value="Living">Living</mat-option>
                            <mat-option value="Deceased">Deceased</mat-option>
                          </mat-select>
                        </mat-form-field>

                        <div class="checkbox-field">
                          <mat-checkbox formControlName="is_emergency_contact">Emergency Contact</mat-checkbox>
                        </div>
                      </div>

                      <div class="form-row full-width">
                        <mat-form-field appearance="outline">
                          <mat-label>Additional Information</mat-label>
                          <textarea matInput formControlName="additional_info" rows="2"></textarea>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <button mat-raised-button color="primary" type="button" (click)="addSibling()">
                    <mat-icon>add</mat-icon> Add Sibling
                  </button>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon> Back
                  </button>
                  <button mat-button matStepperNext color="primary">
                    Next <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Professional Information Step -->
            <mat-step [stepControl]="professionalInfoForm" label="Professional Information">
              <div class="step-content" [formGroup]="professionalInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Total Experience (Years)</mat-label>
                    <input matInput type="number" formControlName="totalExperience" min="0" step="0.1">
                    <mat-error *ngIf="professionalInfoForm.get('totalExperience')?.hasError('min')">
                      Experience cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Current Organization</mat-label>
                    <input matInput formControlName="currentOrganization">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Current Designation</mat-label>
                    <input matInput formControlName="currentDesignation">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Current Salary</mat-label>
                    <input matInput type="number" formControlName="currentSalary" min="0">
                    <mat-error *ngIf="professionalInfoForm.get('currentSalary')?.hasError('min')">
                      Salary cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Expected Salary</mat-label>
                    <input matInput type="number" formControlName="expectedSalary" min="0">
                    <mat-error *ngIf="professionalInfoForm.get('expectedSalary')?.hasError('min')">
                      Expected salary cannot be negative
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Notice Period (Days)</mat-label>
                    <input matInput type="number" formControlName="noticePeriod" min="0">
                    <mat-error *ngIf="professionalInfoForm.get('noticePeriod')?.hasError('min')">
                      Notice period cannot be negative
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Skills</mat-label>
                    <textarea matInput formControlName="skills" placeholder="E.g. Excel, Customer Service, Management"></textarea>
                    <mat-hint>Enter skills separated by commas</mat-hint>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Reason For Change</mat-label>
                    <textarea matInput formControlName="reasonForChange" rows="3"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Education Level</mat-label>
                    <mat-select formControlName="educationLevel">
                      <mat-option *ngFor="let level of educationLevels" [value]="level">
                        {{level}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Degrees</mat-label>
                    <input matInput formControlName="degrees">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>University/Institution</mat-label>
                    <input matInput formControlName="university">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Year of Passing</mat-label>
                    <input matInput type="number" formControlName="yearOfPassing" min="1900" [max]="currentYear">
                    <mat-error *ngIf="professionalInfoForm.get('yearOfPassing')?.hasError('min') || professionalInfoForm.get('yearOfPassing')?.hasError('max')">
                      Please enter a valid year
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Specialization</mat-label>
                    <input matInput formControlName="specialization">
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Additional Certifications</mat-label>
                    <textarea matInput formControlName="additionalCertifications" rows="2"></textarea>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Reference Contacts</mat-label>
                    <textarea matInput formControlName="referenceContacts" rows="3" placeholder="Name, Designation, Company, Contact Number"></textarea>
                    <mat-hint>Add multiple references separated by new lines</mat-hint>
                  </mat-form-field>
                </div>

                <div class="form-row full-width">
                  <mat-form-field appearance="outline">
                    <mat-label>Portfolio Links</mat-label>
                    <textarea matInput formControlName="portfolioLinks" rows="2" placeholder="LinkedIn, Personal Website, etc."></textarea>
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon> Back
                  </button>
                  <button mat-button matStepperNext color="primary">
                    Next <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Position and Document Upload Step -->
            <mat-step [stepControl]="positionInfoForm" label="Position & Documents">
              <div class="step-content" [formGroup]="positionInfoForm">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Position Applying For*</mat-label>
                    <mat-select formControlName="position_id" required>
                      <mat-option *ngFor="let position of positions" [value]="position.id">
                        {{ position.title }} ({{ position.department_name }})
                      </mat-option>
                    </mat-select>
                    <mat-error *ngIf="positionInfoForm.get('position_id')?.hasError('required')">
                      Position is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Status</mat-label>
                    <mat-select formControlName="status">
                      <mat-option value="New">New</mat-option>
                      <mat-option value="In Process">In Process</mat-option>
                      <mat-option value="Selected">Selected</mat-option>
                      <mat-option value="Rejected">Rejected</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <div class="checkbox-field">
                    <mat-checkbox formControlName="isActive">Is Active</mat-checkbox>
                  </div>
                </div>

                <div class="file-upload-section">
                  <h3>Resume Upload</h3>
                  <div class="file-upload-container">
                    <div class="file-upload-area" 
                         [ngClass]="{'has-file': resumeFileName}"
                         (click)="resumeFileInput.click()">
                      <input hidden type="file" #resumeFileInput (change)="onResumeSelected($event)" accept=".pdf,.doc,.docx,.txt,.rtf">
                      <mat-icon>upload_file</mat-icon>
                      <ng-container *ngIf="!resumeFileName">
                        <span>Click to upload resume</span>
                      </ng-container>
                      <ng-container *ngIf="resumeFileName">
                        <span>{{resumeFileName}}</span>
                      </ng-container>
                    </div>
                    <ng-container *ngIf="resumeFileName">
                      <button mat-icon-button 
                              color="warn" 
                              type="button"
                              (click)="removeResume()" 
                              matTooltip="Remove file">
                        <mat-icon>close</mat-icon>
                      </button>
                    </ng-container>
                  </div>
                  <p class="upload-hint">Allowed formats: PDF, DOC, DOCX, TXT, RTF. Max size: 10MB</p>
                </div>

                <div class="file-upload-section">
                  <h3>Profile Picture Upload</h3>
                  <div class="file-upload-container">
                    <div class="file-upload-area" 
                         [ngClass]="{'has-file': profilePictureFileName}"
                         (click)="profilePictureInput.click()">
                      <input hidden type="file" #profilePictureInput (change)="onProfilePictureSelected($event)" accept="image/jpeg,image/png,image/jpg">
                      <mat-icon>add_photo_alternate</mat-icon>
                      <ng-container *ngIf="!profilePictureFileName">
                        <span>Click to upload profile picture</span>
                      </ng-container>
                      <ng-container *ngIf="profilePictureFileName">
                        <span>{{profilePictureFileName}}</span>
                      </ng-container>
                    </div>
                    <ng-container *ngIf="profilePictureFileName">
                      <button mat-icon-button 
                              color="warn" 
                              type="button"
                              (click)="removeProfilePicture()" 
                              matTooltip="Remove file">
                        <mat-icon>close</mat-icon>
                      </button>
                    </ng-container>
                  </div>
                  <p class="upload-hint">Allowed formats: JPG, PNG. Max size: 5MB</p>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon> Back
                  </button>
                  <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="isSubmitting">
                    <mat-icon>save</mat-icon> Submit
                  </button>
                </div>
              </div>
            </mat-step>
          </mat-stepper>
        </form>
      </mat-card-content>
    </mat-card>
  </div>

  <div class="loading-overlay" *ngIf="isSubmitting">
    <div class="loading-content">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Saving candidate information...</p>
    </div>
  </div>
</div>