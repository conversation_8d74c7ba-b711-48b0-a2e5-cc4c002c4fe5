.tokens-container {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.page-subtitle {
  margin: 4px 0 0 0;
  color: rgba(0, 0, 0, 0.6);
}

.tokens-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 0;
}

.table-container {
  overflow-x: auto;
}

.tokens-table {
  width: 100%;
}

.token-cell {
  display: flex;
  align-items: center;
}

.token-text {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.status-chip {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-valid {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-used {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-invalid {
  background-color: #ffebee;
  color: #c62828;
}

.expired {
  color: #c62828;
}

.expired-tag {
  background-color: #ffebee;
  color: #c62828;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
