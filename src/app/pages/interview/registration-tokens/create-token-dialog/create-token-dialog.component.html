<h2 mat-dialog-title>Create Registration Token</h2>

<mat-dialog-content>
  <form [formGroup]="tokenForm">
    <p class="dialog-description">
      Create a registration token that candidates can use to register for a position.
    </p>

    <div class="form-field">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Position (Optional)</mat-label>
        <mat-select formControlName="position_id">
          <mat-option [value]="">Any Position</mat-option>
          <mat-option *ngFor="let position of positions" [value]="position.id">
            {{ position.title }} - {{ position.department_name }}
          </mat-option>
        </mat-select>
        <mat-spinner *ngIf="isLoadingPositions" diameter="20" matSuffix></mat-spinner>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Email (Optional)</mat-label>
        <input matInput formControlName="email" type="email" placeholder="<EMAIL>">
        <mat-hint>If specified, only this email can use the token</mat-hint>
        <mat-error *ngIf="tokenForm.get('email')?.hasError('email')">
          Please enter a valid email address
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Expiry Days</mat-label>
        <input matInput formControlName="expiry_days" type="number" min="1" max="30">
        <mat-hint>Number of days until the token expires</mat-hint>
        <mat-error *ngIf="tokenForm.get('expiry_days')?.hasError('required')">
          Expiry days is required
        </mat-error>
        <mat-error *ngIf="tokenForm.get('expiry_days')?.hasError('min')">
          Minimum 1 day
        </mat-error>
        <mat-error *ngIf="tokenForm.get('expiry_days')?.hasError('max')">
          Maximum 30 days
        </mat-error>
      </mat-form-field>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()" [disabled]="isLoading">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="tokenForm.invalid || isLoading">
    <span *ngIf="!isLoading">Create Token</span>
    <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
  </button>
</mat-dialog-actions>
