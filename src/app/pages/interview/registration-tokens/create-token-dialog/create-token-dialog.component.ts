import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { InterviewService } from '../../../../services/interview.service';

@Component({
  selector: 'app-create-token-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './create-token-dialog.component.html',
  styleUrls: ['./create-token-dialog.component.scss']
})
export class CreateTokenDialogComponent implements OnInit {
  tokenForm!: FormGroup;
  positions: any[] = [];
  isLoading: boolean = false;
  isLoadingPositions: boolean = true;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CreateTokenDialogComponent>,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadPositions();
  }

  initForm(): void {
    this.tokenForm = this.fb.group({
      position_id: [''],
      email: ['', [Validators.email]],
      expiry_days: [7, [Validators.required, Validators.min(1), Validators.max(30)]]
    });
  }

  loadPositions(): void {
    this.isLoadingPositions = true;
    this.interviewService.getOpenPositions().subscribe({
      next: (response) => {
        this.isLoadingPositions = false;
        if (response.success) {
          this.positions = response.positions || [];
        } else {
          this.snackBar.open(response.message || 'Failed to load positions', 'Close', { duration: 3000 });
        }
      },
      error: (error) => {
        this.isLoadingPositions = false;
        this.snackBar.open(error.error?.message || 'Error loading positions. Please try again.', 'Close', { duration: 3000 });
        console.error('Error loading positions:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.tokenForm.invalid) {
      return;
    }

    this.isLoading = true;
    const tokenData = this.tokenForm.value;

    this.interviewService.generateRegistrationToken(tokenData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.snackBar.open('Registration token created successfully!', 'Close', { duration: 3000 });
          this.dialogRef.close(true);
        } else {
          this.snackBar.open(response.message || 'Failed to create token', 'Close', { duration: 3000 });
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open(error.error?.message || 'Error creating token. Please try again.', 'Close', { duration: 3000 });
        console.error('Error creating token:', error);
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
