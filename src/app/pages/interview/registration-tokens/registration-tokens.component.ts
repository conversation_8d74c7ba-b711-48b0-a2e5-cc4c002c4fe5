import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { InterviewService } from '../../../services/interview.service';
import { CreateTokenDialogComponent } from './create-token-dialog/create-token-dialog.component';
import { ConfirmDialogComponent } from '../../../components/confirm-dialog/confirm-dialog.component';
import { RegistrationToken } from '../../../interfaces/registration-token.interface';

@Component({
  selector: 'app-registration-tokens',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatDialogModule,
    MatChipsModule,
    MatMenuModule,
    CreateTokenDialogComponent,
    ConfirmDialogComponent
  ],
  templateUrl: './registration-tokens.component.html',
  styleUrls: ['./registration-tokens.component.scss']
})
export class RegistrationTokensComponent implements OnInit {
  tokens: RegistrationToken[] = [];
  isLoading: boolean = true;
  errorMessage: string = '';
  displayedColumns: string[] = ['token', 'position', 'email', 'expiry_date', 'is_used', 'is_valid', 'created_by', 'actions'];

  constructor(
    private interviewService: InterviewService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadTokens();
  }

  loadTokens(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.interviewService.getAllRegistrationTokens().subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.tokens = response.tokens || [];
        } else {
          this.errorMessage = response.message || 'Failed to load registration tokens';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.error?.message || 'Error loading tokens. Please try again.';
        console.error('Error loading registration tokens:', error);
      }
    });
  }

  openCreateTokenDialog(): void {
    const dialogRef = this.dialog.open(CreateTokenDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadTokens();
      }
    });
  }

  copyTokenLink(token: string): void {
    const registrationLink = `${window.location.origin}/register/validate/${token}`;
    navigator.clipboard.writeText(registrationLink).then(() => {
      this.snackBar.open('Registration link copied to clipboard!', 'Close', { duration: 3000 });
    }, (err) => {
      console.error('Could not copy text: ', err);
      this.snackBar.open('Failed to copy link. Please try again.', 'Close', { duration: 3000 });
    });
  }

  invalidateToken(token: string): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Invalidate Token',
        message: 'Are you sure you want to invalidate this registration token? This action cannot be undone.',
        confirmText: 'Invalidate',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.interviewService.invalidateToken(token).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Token invalidated successfully!', 'Close', { duration: 3000 });
              this.loadTokens();
            } else {
              this.snackBar.open(response.message || 'Failed to invalidate token', 'Close', { duration: 3000 });
            }
          },
          error: (error) => {
            this.snackBar.open(error.error?.message || 'Error invalidating token. Please try again.', 'Close', { duration: 3000 });
            console.error('Error invalidating token:', error);
          }
        });
      }
    });
  }

  deleteToken(id: number): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Token',
        message: 'Are you sure you want to delete this registration token? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.interviewService.deleteToken(id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Token deleted successfully!', 'Close', { duration: 3000 });
              this.loadTokens();
            } else {
              this.snackBar.open(response.message || 'Failed to delete token', 'Close', { duration: 3000 });
            }
          },
          error: (error) => {
            this.snackBar.open(error.error?.message || 'Error deleting token. Please try again.', 'Close', { duration: 3000 });
            console.error('Error deleting token:', error);
          }
        });
      }
    });
  }

  getStatusClass(isValid: boolean, isUsed: boolean): string {
    if (!isValid) return 'status-invalid';
    if (isUsed) return 'status-used';
    return 'status-valid';
  }

  getStatusText(isValid: boolean, isUsed: boolean): string {
    if (!isValid) return 'Invalid';
    if (isUsed) return 'Used';
    return 'Valid';
  }

  isExpired(expiryDate: string): boolean {
    return new Date(expiryDate) < new Date();
  }
}
