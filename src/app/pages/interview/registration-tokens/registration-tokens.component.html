<div class="tokens-container">
  <div class="header-section">
    <div class="title-section">
      <h1 class="page-title">Registration Tokens</h1>
      <p class="page-subtitle">Manage candidate registration tokens</p>
    </div>
    
    <button mat-raised-button color="primary" (click)="openCreateTokenDialog()">
      <mat-icon>add</mat-icon> Create Token
    </button>
  </div>

  <mat-card class="tokens-card">
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading registration tokens...</p>
    </div>

    <div *ngIf="!isLoading && errorMessage" class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ errorMessage }}</p>
      <button mat-button color="primary" (click)="loadTokens()">Retry</button>
    </div>

    <div *ngIf="!isLoading && !errorMessage && tokens.length === 0" class="empty-container">
      <mat-icon color="primary">info</mat-icon>
      <p>No registration tokens found. Create a token to allow candidates to register.</p>
      <button mat-raised-button color="primary" (click)="openCreateTokenDialog()">
        <mat-icon>add</mat-icon> Create Token
      </button>
    </div>

    <div *ngIf="!isLoading && !errorMessage && tokens.length > 0" class="table-container">
      <table mat-table [dataSource]="tokens" class="tokens-table">
        <!-- Token Column -->
        <ng-container matColumnDef="token">
          <th mat-header-cell *matHeaderCellDef>Token</th>
          <td mat-cell *matCellDef="let token">
            <div class="token-cell">
              <span class="token-text">{{ token.token.substring(0, 8) }}...</span>
              <button mat-icon-button color="primary" (click)="copyTokenLink(token.token)" matTooltip="Copy registration link">
                <mat-icon>content_copy</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <!-- Position Column -->
        <ng-container matColumnDef="position">
          <th mat-header-cell *matHeaderCellDef>Position</th>
          <td mat-cell *matCellDef="let token">
            {{ token.position_title || 'Any Position' }}
          </td>
        </ng-container>

        <!-- Email Column -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef>Email</th>
          <td mat-cell *matCellDef="let token">
            {{ token.email || 'Not specified' }}
          </td>
        </ng-container>

        <!-- Expiry Date Column -->
        <ng-container matColumnDef="expiry_date">
          <th mat-header-cell *matHeaderCellDef>Expiry Date</th>
          <td mat-cell *matCellDef="let token" [class.expired]="isExpired(token.expiry_date)">
            {{ token.expiry_date | date:'medium' }}
            <span *ngIf="isExpired(token.expiry_date)" class="expired-tag">Expired</span>
          </td>
        </ng-container>

        <!-- Is Used Column -->
        <ng-container matColumnDef="is_used">
          <th mat-header-cell *matHeaderCellDef>Used</th>
          <td mat-cell *matCellDef="let token">
            <mat-icon *ngIf="token.is_used" color="primary">check_circle</mat-icon>
            <mat-icon *ngIf="!token.is_used" color="warn">cancel</mat-icon>
          </td>
        </ng-container>

        <!-- Is Valid Column -->
        <ng-container matColumnDef="is_valid">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let token">
            <span class="status-chip" [ngClass]="getStatusClass(token.is_valid, token.is_used)">
              {{ getStatusText(token.is_valid, token.is_used) }}
            </span>
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by">
          <th mat-header-cell *matHeaderCellDef>Created By</th>
          <td mat-cell *matCellDef="let token">
            {{ token.created_by_username || 'Unknown' }}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let token">
            <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Actions">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <button mat-menu-item (click)="copyTokenLink(token.token)">
                <mat-icon>content_copy</mat-icon>
                <span>Copy Link</span>
              </button>
              <button mat-menu-item (click)="invalidateToken(token.token)" [disabled]="!token.is_valid">
                <mat-icon>block</mat-icon>
                <span>Invalidate</span>
              </button>
              <button mat-menu-item (click)="deleteToken(token.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </mat-card>
</div>
