<!-- all-interview-sessions.component.html -->
<div class="sessions-container">
    <div class="component-header">
      <h1 class="component-title">Interview Sessions</h1>
      <div class="header-actions">
        <button mat-raised-button color="primary" [routerLink]="['/site/interview/candidates']">
          <mat-icon>people</mat-icon> View Candidates
        </button>
      </div>
    </div>
  
    <div class="content-wrapper">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Search sessions...">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
  
      <!-- Error message display -->
      <div *ngIf="error" class="error-message">
        {{error}}
        <button mat-button color="primary" (click)="loadAllSessions()">Retry</button>
      </div>
      
      <!-- Loading indicator -->
      <div *ngIf="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading interview sessions...</span>
      </div>
      
      <!-- Sessions table -->
      <div class="table-container" *ngIf="!loading && !error">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Round Title Column -->
          <ng-container matColumnDef="round_title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Round</th>
            <td mat-cell *matCellDef="let element">{{element.round_title}}</td>
          </ng-container>
  
          <!-- Date Column -->
          <ng-container matColumnDef="interview_date">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Date & Time</th>
            <td mat-cell *matCellDef="let element">{{formatDate(element.interview_date)}}</td>
          </ng-container>
  
          <!-- Mode Column -->
          <ng-container matColumnDef="interview_mode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Mode</th>
            <td mat-cell *matCellDef="let element">{{element.interview_mode}}</td>
          </ng-container>
  
          <!-- Candidate Column -->
          <ng-container matColumnDef="candidate_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Candidate</th>
            <td mat-cell *matCellDef="let element">
              {{element.first_name}} {{element.last_name}}
            </td>
          </ng-container>
  
          <!-- Position Column -->
          <ng-container matColumnDef="position_title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Position</th>
            <td mat-cell *matCellDef="let element">{{element.position_title}}</td>
          </ng-container>
  
          <!-- Interviewer Column -->
          <ng-container matColumnDef="interviewer_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Interviewer</th>
            <td mat-cell *matCellDef="let element">{{element.interviewer_name}}</td>
          </ng-container>
  
          <!-- Outcome Column -->
          <ng-container matColumnDef="outcome">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Outcome</th>
            <td mat-cell *matCellDef="let element">
              <span class="outcome-badge" [ngClass]="getOutcomeClass(element.outcome)">
                {{element.outcome}}
              </span>
            </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" (click)="viewSession(element)" matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent" (click)="editSession(element)" matTooltip="Edit Session">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>