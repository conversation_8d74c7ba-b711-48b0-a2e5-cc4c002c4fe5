// all-interview-sessions.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router, RouterModule } from '@angular/router';
import { InterviewService } from '../../../../services/interview.service';

@Component({
  selector: 'app-all-interview-sessions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatChipsModule,
    MatSnackBarModule,
  ],
  templateUrl: './all-interview-sessions.component.html',
  styleUrls: ['./all-interview-sessions.component.scss'],
})
export class AllInterviewSessionsComponent implements OnInit {
  displayedColumns: string[] = [
    'round_title',
    'interview_date',
    'interview_mode',
    'candidate_name',
    'position_title',
    'interviewer_name',
    'outcome',
    'actions',
  ];
  dataSource = new MatTableDataSource<any>([]);
  loading = false;
  error = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private interviewService: InterviewService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAllSessions();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadAllSessions(): void {
    this.loading = true;
    this.error = '';

    this.interviewService.getAllInterviewSessions().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.sessions || [];
        } else {
          this.error = response.message || 'Failed to load sessions';
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading sessions:', err);
        this.error = 'Error loading sessions. Please try again later.';
        this.loading = false;
      },
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  viewSession(session: any): void {
    this.router.navigate([
      '/site/interview/sessions/view',
      session.id,
      session.candidate_id,
      session.position_id,
    ]);
  }

  editSession(session: any): void {
    this.router.navigate([
      '/site/interview/sessions/edit',
      session.id,
      session.candidate_id,
      session.position_id,
    ]);
  }

  getOutcomeClass(outcome: string): string {
    if (!outcome) return '';

    switch (outcome) {
      case 'Move to next round':
        return 'outcome-move-to-next-round';
      case 'Reject':
        return 'outcome-reject';
      case 'Hold':
        return 'outcome-hold';
      case 'Select':
        return 'outcome-select';
      default:
        return '';
    }
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleString();
  }
}
