// all-interview-sessions.component.scss
.sessions-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
  
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  
    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  
    .search-field {
      width: 100%;
      max-width: 500px;
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background-color: white;
      border-radius: 4px;
      
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  
    .table-container {
      background-color: white;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      table {
        width: 100%;
      }
    }
  
    .outcome-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      
      &.outcome-move-to-next-round {
        background-color: #e3f2fd;
        color: #1976d2;
      }
      
      &.outcome-reject {
        background-color: #ffebee;
        color: #d32f2f;
      }
      
      &.outcome-hold {
        background-color: #fff8e1;
        color: #ffa000;
      }
      
      &.outcome-select {
        background-color: #e8f5e9;
        color: #388e3c;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .sessions-container {
      padding: 16px;
      
      .component-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
      
      .table-container {
        overflow-x: auto;
      }
    }
  }