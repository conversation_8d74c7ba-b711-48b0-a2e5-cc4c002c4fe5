.add-interview-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100%;
  
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .title-section {
        display: flex;
        align-items: center;
        gap: 10px;
  
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  
    .info-card {
      margin-bottom: 20px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
      .candidate-info {
        display: grid;
        grid-template-columns: auto 1fr auto 1fr;
        gap: 10px;
        align-items: center;
  
        .info-label {
          font-weight: 500;
          color: #666;
        }
  
        .info-value {
          color: #333;
        }
      }
    }
  
    .form-card {
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background-color: #fff;
      margin-bottom: 20px;
  
      mat-card-header {
        padding: 16px;
        border-bottom: 1px solid #e0e0e0;
      }
  
      mat-card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }
    }
  
    .interview-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;
  
      .full-width {
        width: 100%;
      }
  
      .form-row {
        display: flex;
        gap: 16px;
  
        .form-field {
          flex: 1;
        }
      }
  
      textarea {
        min-height: 80px;
      }
  
      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 16px;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .add-interview-container {
      padding: 16px;
  
      .info-card {
        .candidate-info {
          grid-template-columns: auto 1fr;
        }
      }
  
      .interview-form {
        .form-row {
          flex-direction: column;
          gap: 0;
        }
  
        .form-actions {
          flex-direction: column-reverse;
          gap: 10px;
  
          button {
            width: 100%;
          }
        }
      }
    }
  }