<div class="add-interview-container">
    <div class="header-section">
      <div class="title-section">
        <button mat-icon-button color="primary" (click)="cancel()" matTooltip="Go Back">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1 class="page-title">Schedule Interview Session</h1>
      </div>
    </div>
  
    <mat-card class="info-card">
      <mat-card-content>
        <div class="candidate-info">
          <div class="info-label">Candidate:</div>
          <div class="info-value">{{ candidateName }}</div>
          <div class="info-label">Position:</div>
          <div class="info-value">{{ positionTitle }}</div>
        </div>
      </mat-card-content>
    </mat-card>
  
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>Interview Details</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="interviewForm" class="interview-form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Round Title</mat-label>
            <input matInput formControlName="round_title" placeholder="e.g. Technical Round, HR Round">
            <mat-error *ngIf="interviewForm.get('round_title')?.hasError('required')">
              Round title is required
            </mat-error>
            <mat-error *ngIf="interviewForm.get('round_title')?.hasError('minlength')">
              Round title must be at least 3 characters
            </mat-error>
          </mat-form-field>
  
          <div class="form-row">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Interview Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="interview_date">
              <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="interviewForm.get('interview_date')?.hasError('required')">
                Interview date is required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Interview Time</mat-label>
              <input matInput type="time" formControlName="interview_time">
              <mat-error *ngIf="interviewForm.get('interview_time')?.hasError('required')">
                Interview time is required
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="form-row">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Interview Mode</mat-label>
              <mat-select formControlName="interview_mode">
                <mat-option *ngFor="let mode of interviewModes" [value]="mode">
                  {{ mode }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="interviewForm.get('interview_mode')?.hasError('required')">
                Interview mode is required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Interviewer</mat-label>
              <mat-select formControlName="interviewer_id">
                <mat-option *ngFor="let interviewer of interviewers" [value]="interviewer.id">
                  {{ interviewer.username }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="interviewForm.get('interviewer_id')?.hasError('required')">
                Interviewer is required
              </mat-error>
            </mat-form-field>
          </div>
  
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Comments</mat-label>
            <textarea matInput formControlName="comments" rows="3" placeholder="Any additional information about this interview..."></textarea>
          </mat-form-field>
  
         
  
          <div class="form-actions">
            <button mat-button (click)="cancel()" [disabled]="isLoading">Cancel</button>
            <button 
              mat-raised-button 
              color="primary" 
              (click)="onSubmit()" 
              [disabled]="interviewForm.invalid || isLoading">
              <span *ngIf="!isLoading">Schedule Interview</span>
              <span *ngIf="isLoading">Scheduling...</span>
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>