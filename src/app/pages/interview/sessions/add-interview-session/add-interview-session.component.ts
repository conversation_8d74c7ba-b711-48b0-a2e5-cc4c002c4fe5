import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { InterviewService } from '../../../../services/interview.service';
import { UserService } from '../../../../services/user.service';

interface User {
  id: number;
  username: string;
}

@Component({
  selector: 'app-add-interview-session',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
  ],
  templateUrl: './add-interview-session.component.html',
  styleUrls: ['./add-interview-session.component.scss'],
})
export class AddInterviewSessionComponent implements OnInit {
  interviewForm!: FormGroup;
  interviewers: User[] = [];
  interviewModes = ['In-person', 'Video', 'Phone'];
  possibleOutcomes = ['Move to next round', 'Reject', 'Hold', 'Select'];
  isLoading = false;
  candidateId!: number;
  positionId!: number;
  candidateName: string = '';
  positionTitle: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private interviewService: InterviewService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.candidateId = +this.route.snapshot.paramMap.get('candidateId')!;
    this.positionId = +this.route.snapshot.paramMap.get('positionId')!;

    this.createForm();
    this.loadInterviewers();
    this.loadCandidateDetails();
    this.loadPositionDetails();
  }

  createForm(): void {
    this.interviewForm = this.fb.group({
      round_title: ['', [Validators.required, Validators.minLength(3)]],
      interview_date: [new Date(), Validators.required],
      interview_time: ['10:00', Validators.required],
      interview_mode: ['In-person', Validators.required],
      interviewer_id: ['', Validators.required],
      comments: [''], // No validators - this makes it optional
    });
  }

  loadInterviewers(): void {
    this.userService.getAllUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.interviewers = response.users;
        } else {
          this.snackBar.open('Failed to load interviewers', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error loading interviewers:', error);
        this.snackBar.open('Error loading interviewers', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  // In add-interview-session.component.ts
  loadCandidateDetails(): void {
    this.interviewService.getCandidateById(this.candidateId).subscribe({
      next: (response) => {
        if (response.success && response.candidate) {
          // The issue may be here - the field names might be different
          // Check if we're using the correct property names
          const candidate = response.candidate;

          // Add debugging to check the structure
          console.log('Candidate data:', candidate);

          // Try different possible field name combinations
          if (candidate.firstName && candidate.lastName) {
            this.candidateName = `${candidate.firstName} ${candidate.lastName}`;
          } else if (candidate.first_name && candidate.last_name) {
            this.candidateName = `${candidate.first_name} ${candidate.last_name}`;
          } else {
            // Fallback
            this.candidateName =
              candidate.name || 'Candidate Name Not Available';
          }
        }
      },
      error: (error) => {
        console.error('Error loading candidate details:', error);
        this.snackBar.open('Error loading candidate details', 'Close', {
          duration: 3000,
        });
        // Set a fallback name
        this.candidateName = 'Candidate Name Not Available';
      },
    });
  }

  loadPositionDetails(): void {
    this.interviewService.getPositionById(this.positionId).subscribe({
      next: (response) => {
        if (response.success && response.position) {
          this.positionTitle = response.position.title;
        }
      },
      error: (error) => {
        console.error('Error loading position details:', error);
        this.snackBar.open('Error loading position details', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  onSubmit(): void {
    if (this.interviewForm.valid) {
      this.isLoading = true;

      // Get form values
      const formValues = this.interviewForm.value;

      // Format date if needed
      let formattedDate;
      if (formValues.interview_date) {
        const interviewDate = new Date(formValues.interview_date);
        if (formValues.interview_time) {
          const [hours, minutes] = formValues.interview_time
            .split(':')
            .map(Number);
          interviewDate.setHours(hours, minutes, 0, 0);
        }
        formattedDate = interviewDate
          .toISOString()
          .slice(0, 19)
          .replace('T', ' ');
      }

      const sessionData = {
        round_title: formValues.round_title,
        interview_date: formattedDate,
        interview_mode: formValues.interview_mode,
        interviewer_id: formValues.interviewer_id,
        comments: formValues.comments,
        outcome: formValues.outcome,
        candidate_id: this.candidateId,
        position_id: this.positionId,
      };

      this.interviewService.addInterviewSession(sessionData).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open(
              'Interview session scheduled successfully',
              'Close',
              { duration: 3000 }
            );
            this.router.navigate([
              '/site/interview/sessions',
              this.candidateId,
              this.positionId,
            ]);
          } else {
            this.snackBar.open(
              response.message || 'Failed to schedule interview',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error scheduling interview:', error);
          this.snackBar.open('Error scheduling interview', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  navigateToSessions(): void {
    this.router.navigate([
      '/site/interview/sessions/candidate',
      this.candidateId,
      'position',
      this.positionId,
    ]);
  }

  cancel(): void {
    this.navigateToSessions();
  }
}
