import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { InterviewService } from '../../../../services/interview.service';
import { UserService } from '../../../../services/user.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

interface InterviewSession {
  id: number;
  candidate_id: number;
  position_id: number;
  round_title: string;
  interview_date: string;
  interview_mode: string;
  interviewer_id: number;
  interviewer_name: string;
  comments: string;
  outcome: string;
  first_name?: string;
  last_name?: string;
  position_title?: string;
}

interface User {
  id: number;
  username: string;
}

@Component({
  selector: 'app-edit-interview-session',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-interview-session.component.html',
  styleUrls: ['./edit-interview-session.component.scss'],
})
export class EditInterviewSessionComponent implements OnInit {
  interviewForm!: FormGroup;
  interviewers: User[] = [];
  interviewModes = ['In-person', 'Video', 'Phone'];
  possibleOutcomes = ['Move to next round', 'Reject', 'Hold', 'Select'];
  isLoading = true;
  sessionId!: number;
  candidateId!: number;
  positionId!: number;
  session: InterviewSession | null = null;
  candidateName: string = '';
  positionTitle: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private interviewService: InterviewService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.sessionId = +this.route.snapshot.paramMap.get('sessionId')!;
    this.candidateId = +this.route.snapshot.paramMap.get('candidateId')!;
    this.positionId = +this.route.snapshot.paramMap.get('positionId')!;

    console.log('Edit session component initialized with:');
    console.log('Session ID:', this.sessionId);
    console.log('Candidate ID:', this.candidateId);
    console.log('Position ID:', this.positionId);

    this.createForm();
    this.loadSessionData();
    this.loadInterviewers();
  }

  createForm(): void {
    this.interviewForm = this.fb.group({
      round_title: ['', [Validators.required, Validators.minLength(3)]],
      interview_date: ['', Validators.required],
      interview_time: ['', Validators.required],
      interview_mode: ['', Validators.required],
      interviewer_id: ['', Validators.required],
      comments: [''],
    });
  }

  loadSessionData(): void {
    this.isLoading = true;
    this.interviewService.getInterviewSessionById(this.sessionId).subscribe({
      next: (response) => {
        if (response.success && response.session) {
          this.session = response.session;
          this.candidateName =
            this.session?.first_name && this.session?.last_name
              ? `${this.session.first_name} ${this.session.last_name}`
              : '';
          this.positionTitle = this.session?.position_title || '';
          this.populateForm();
          this.isLoading = false;
        } else {
          this.snackBar.open('Failed to load interview session', 'Close', {
            duration: 3000,
          });
          this.navigateBack();
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error loading interview session:', error);
        this.snackBar.open('Error loading interview session', 'Close', {
          duration: 3000,
        });
        this.navigateBack();
      },
    });
  }

  loadInterviewers(): void {
    this.userService.getAllUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.interviewers = response.users;
        } else {
          this.snackBar.open('Failed to load interviewers', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error loading interviewers:', error);
        this.snackBar.open('Error loading interviewers', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  populateForm(): void {
    if (this.session) {
      // Parse the interview date/time from MySQL format
      const interviewDate = new Date(this.session.interview_date);

      // Extract time in HH:MM format
      const hours = interviewDate.getHours().toString().padStart(2, '0');
      const minutes = interviewDate.getMinutes().toString().padStart(2, '0');
      const timeString = `${hours}:${minutes}`;

      this.interviewForm.patchValue({
        round_title: this.session.round_title,
        interview_date: interviewDate,
        interview_time: timeString,
        interview_mode: this.session.interview_mode,
        interviewer_id: this.session.interviewer_id,
        comments: this.session.comments,
        outcome: this.session.outcome,
      });
    }
  }

  onSubmit(): void {
    if (this.interviewForm.valid && this.session) {
      this.isLoading = true;

      // Get form values
      const formValues = this.interviewForm.value;

      // Format date for MySQL
      let formattedDate;
      if (formValues.interview_date) {
        const interviewDate = new Date(formValues.interview_date);
        if (formValues.interview_time) {
          const [hours, minutes] = formValues.interview_time
            .split(':')
            .map(Number);
          interviewDate.setHours(hours, minutes, 0, 0);
        }
        formattedDate = interviewDate
          .toISOString()
          .slice(0, 19)
          .replace('T', ' ');
      }

      const sessionData = {
        round_title: formValues.round_title,
        interview_date: formattedDate,
        interview_mode: formValues.interview_mode,
        interviewer_id: formValues.interviewer_id,
        comments: formValues.comments,
        outcome: formValues.outcome,
      };

      this.interviewService
        .updateInterviewSession(this.sessionId, sessionData)
        .subscribe({
          next: (response) => {
            this.isLoading = false;
            if (response.success) {
              this.snackBar.open(
                'Interview session updated successfully',
                'Close',
                { duration: 3000 }
              );
              this.navigateToSessions();
            } else {
              this.snackBar.open(
                response.message || 'Failed to update interview',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Error updating interview:', error);
            this.snackBar.open('Error updating interview', 'Close', {
              duration: 3000,
            });
          },
        });
    }
  }

  navigateToSessions(): void {
    if (this.session) {
      this.router.navigate([
        '/site/interview/sessions',
        this.session.candidate_id,
        this.session.position_id,
      ]);
    } else {
      this.router.navigate(['/site/interview/candidates']);
    }
  }

  navigateBack(): void {
    this.router.navigate([
      '/site/interview/sessions',
      this.candidateId,
      this.positionId,
    ]);
  }

  cancel(): void {
    this.navigateBack();
  }
}
