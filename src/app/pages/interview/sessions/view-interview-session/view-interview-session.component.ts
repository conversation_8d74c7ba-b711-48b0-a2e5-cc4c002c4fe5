import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InterviewService } from '../../../../services/interview.service';

interface InterviewSession {
  id: number;
  candidate_id: number;
  position_id: number;
  round_title: string;
  interview_date: string;
  interview_mode: string;
  interviewer_id: number;
  interviewer_name: string;
  comments: string;
  outcome: string;
  first_name?: string;
  last_name?: string;
  position_title?: string;
  updated_by_name?: string;
}

@Component({
  selector: 'app-view-interview-session',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
  ],
  templateUrl: './view-interview-session.component.html',
  styleUrls: ['./view-interview-session.component.scss'],
})
export class ViewInterviewSessionComponent implements OnInit {
  sessionId!: number;
  candidateId!: number;
  positionId!: number;
  session: InterviewSession | null = null;
  isLoading = true;
  error = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.sessionId = +params.get('sessionId')!;
      this.candidateId = +params.get('candidateId')!;
      this.positionId = +params.get('positionId')!;

      console.log('View session component initialized with:');
      console.log('Session ID:', this.sessionId);
      console.log('Candidate ID:', this.candidateId);
      console.log('Position ID:', this.positionId);

      this.loadSessionData();
    });
  }

  loadSessionData(): void {
    this.isLoading = true;
    this.error = '';

    this.interviewService.getInterviewSessionById(this.sessionId).subscribe({
      next: (response) => {
        if (response.success && response.session) {
          this.session = response.session;
          this.isLoading = false;
        } else {
          this.error = response.message || 'Failed to load interview session';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error loading interview session:', error);
        this.error = 'Error loading interview session. Please try again.';
        this.isLoading = false;
      },
    });
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }

  navigateToEdit(): void {
    this.router.navigate([
      '/site/interview/sessions/edit',
      this.sessionId,
      this.candidateId,
      this.positionId,
    ]);
  }

  goBack(): void {
    this.router.navigate(['/site/interview/sessions']);
  }
}
