.view-interview-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100%;
  
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .title-section {
        display: flex;
        align-items: center;
        gap: 10px;
  
        .page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }
  
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      p {
        margin-top: 16px;
        color: #666;
      }
    }
  
    .info-card {
      margin-bottom: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
      .candidate-info {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
  
        .info-section {
          display: flex;
          align-items: center;
          gap: 10px;
  
          .section-title {
            font-weight: 500;
            color: #666;
          }
  
          .section-value {
            font-size: 16px;
            color: #333;
          }
        }
      }
    }
  
    .details-card {
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
  
      mat-card-header {
        padding-bottom: 0;
      }
  
      .details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
  
        .detail-item {
          .detail-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
          }
  
          .detail-value {
            font-size: 16px;
            color: #333;
          }
        }
      }
  
      .section-divider {
        margin: 20px 0;
      }
  
      .comments-section {
        .section-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 10px;
        }
  
        .comments-content {
          padding: 15px;
          background-color: #f9f9f9;
          border-radius: 4px;
          white-space: pre-line;
          line-height: 1.5;
        }
      }
    }
  
    .outcome-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 14px;
      font-weight: 500;
      
      &.outcome-move-to-next-round {
        background-color: #e3f2fd;
        color: #1976d2;
      }
      
      &.outcome-reject {
        background-color: #ffebee;
        color: #d32f2f;
      }
      
      &.outcome-hold {
        background-color: #fff8e1;
        color: #ffa000;
      }
      
      &.outcome-select {
        background-color: #e8f5e9;
        color: #388e3c;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .view-interview-container {
      padding: 16px;
  
      .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        button {
          align-self: flex-start;
        }
      }
  
      .info-card {
        .candidate-info {
          flex-direction: column;
          gap: 16px;
        }
      }
  
      .details-card {
        .details-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }