<div class="view-interview-container">
    <div class="header-section">
      <div class="title-section">
        <button mat-icon-button color="primary" (click)="goBack()" matTooltip="Back to Sessions">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1 class="page-title">Interview Session Details</h1>
      </div>
      
      <button mat-raised-button color="primary" (click)="navigateToEdit()">
        <mat-icon>edit</mat-icon> Edit Session
      </button>
    </div>
  
    <div *ngIf="error" class="error-message">
      {{error}}
      <button mat-button color="primary" (click)="loadSessionData()">Retry</button>
    </div>
  
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading interview session details...</p>
    </div>
  
    <ng-container *ngIf="!isLoading && session">
      <!-- Candidate and Position Info Card -->
      <mat-card class="info-card">
        <mat-card-content>
          <div class="candidate-info">
            <div class="info-section">
              <div class="section-title">Candidate:</div>
              <div class="section-value">
                {{session.first_name}} {{session.last_name}}
              </div>
            </div>
            
            <div class="info-section">
              <div class="section-title">Position:</div>
              <div class="section-value">{{session.position_title || 'N/A'}}</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
  
      <!-- Session Details Card -->
      <mat-card class="details-card">
        <mat-card-header>
          <mat-card-title>Interview Details</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="details-grid">
            <div class="detail-item">
              <div class="detail-label">Round</div>
              <div class="detail-value">{{session.round_title}}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Date & Time</div>
              <div class="detail-value">{{formatDate(session.interview_date)}}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Mode</div>
              <div class="detail-value">{{session.interview_mode}}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Interviewer</div>
              <div class="detail-value">{{session.interviewer_name || 'N/A'}}</div>
            </div>
            
          
            
              <div class="detail-item" *ngIf="session.updated_by_name">
                <div class="detail-label">Last Updated By</div>
                <div class="detail-value">{{session.updated_by_name}}</div>
              </div>
          </div>
          
          <mat-divider class="section-divider"></mat-divider>
          
          <div class="comments-section">
            <div class="section-title">Comments</div>
            <div class="comments-content">
              {{session.comments || 'No comments provided.'}}
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </ng-container>
  </div>