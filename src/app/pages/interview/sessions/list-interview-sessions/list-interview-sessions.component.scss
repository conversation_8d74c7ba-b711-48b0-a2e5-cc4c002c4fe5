.interview-sessions-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100%;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title-section {
      display: flex;
      align-items: center;
      gap: 10px;

      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
  }

  .info-card {
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .candidate-info {
      h2 {
        margin: 0 0 10px;
        color: #333;
        font-size: 20px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 16px;
      }
    }
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      
      p {
        margin-top: 16px;
        color: #666;
      }
    }

    .sessions-table {
      width: 100%;

      th.mat-header-cell {
        background-color: #f5f5f5;
        color: #333;
        font-weight: 500;
        padding: 12px 16px;
      }

      td.mat-cell {
        padding: 12px 16px;
      }

      .outcome-chip {
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        
        &.chip-progress {
          background-color: #e3f2fd;
          color: #1976d2;
        }
        
        &.chip-reject {
          background-color: #ffebee;
          color: #d32f2f;
        }
        
        &.chip-hold {
          background-color: #fff8e1;
          color: #ffa000;
        }
        
        &.chip-select {
          background-color: #e8f5e9;
          color: #388e3c;
        }
      }
    }
  }

  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .no-data-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      color: #9e9e9e;
      margin-bottom: 16px;
    }

    .no-data-text {
      font-size: 18px;
      color: #757575;
      margin-bottom: 24px;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .interview-sessions-container {
    padding: 16px;

    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      button {
        align-self: flex-start;
      }
    }
    
    .table-container {
      overflow-x: auto;
      
      td.mat-cell,
      th.mat-header-cell {
        padding: 8px;
        
        &:first-of-type {
          padding-left: 16px;
        }
        
        &:last-of-type {
          padding-right: 16px;
        }
      }
      
      .outcome-chip {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}