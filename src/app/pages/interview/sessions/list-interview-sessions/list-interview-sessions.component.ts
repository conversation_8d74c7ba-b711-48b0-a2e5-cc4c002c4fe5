import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { InterviewService } from '../../../../services/interview.service';

interface InterviewSession {
  id: number;
  candidate_id: number;
  position_id: number;
  round_title: string;
  interview_date: string;
  interview_mode: 'In-person' | 'Video' | 'Phone';
  interviewer_id: number;
  interviewer_name: string;
  comments: string;
  outcome: 'Move to next round' | 'Reject' | 'Hold' | 'Select';
  created_at: string;
  updated_at: string;
}

@Component({
  selector: 'app-interview-sessions-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatTableModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatChipsModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './list-interview-sessions.component.html',
  styleUrls: ['./list-interview-sessions.component.scss'],
})
export class ListInterviewSessionsComponent implements OnInit {
  candidateId!: number;
  positionId!: number;
  candidateName: string = '';
  positionTitle: string = '';
  interviewSessions: InterviewSession[] = [];
  isLoading = true;

  displayedColumns: string[] = [
    'interview_date',
    'round_title',
    'interview_mode',
    'interviewer_name',
    'outcome',
    'updated_by',
    'actions',
  ];

  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.candidateId = +this.route.snapshot.paramMap.get('candidateId')!;
    this.positionId = +this.route.snapshot.paramMap.get('positionId')!;

    if (isNaN(this.candidateId)) {
      console.error(
        'Invalid candidate ID:',
        this.route.snapshot.paramMap.get('candidateId')
      );
      // Handle invalid ID, maybe redirect or show an error
      return;
    }

    this.loadCandidateDetails();
    this.loadPositionDetails();
    this.loadInterviewSessions();
  }

  loadCandidateDetails(): void {
    if (!this.candidateId || isNaN(this.candidateId)) {
      this.snackBar.open('Invalid candidate ID', 'Close', { duration: 3000 });
      return;
    }

    this.interviewService.getCandidateById(this.candidateId).subscribe({
      next: (response) => {
        if (response.success && response.candidate) {
          const candidate = response.candidate;
          this.candidateName = `${candidate.first_name} ${candidate.last_name}`;
        }
      },
      error: (error) => {
        console.error('Error loading candidate details:', error);
        this.snackBar.open('Error loading candidate details', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadPositionDetails(): void {
    this.interviewService.getPositionById(this.positionId).subscribe({
      next: (response) => {
        if (response.success && response.position) {
          this.positionTitle = response.position.title;
        }
      },
      error: (error) => {
        console.error('Error loading position details:', error);
        this.snackBar.open('Error loading position details', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadInterviewSessions(): void {
    this.isLoading = true;
    this.interviewService
      .getInterviewSessions(this.candidateId, this.positionId)
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success && response.sessions) {
            // Change here from interviewSessions to sessions
            this.interviewSessions = response.sessions;
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error loading interview sessions:', error);
          this.snackBar.open('Error loading interview sessions', 'Close', {
            duration: 3000,
          });
        },
      });
  }

  getOutcomeChipClass(outcome: string): string {
    switch (outcome) {
      case 'Move to next round':
        return 'chip-progress';
      case 'Reject':
        return 'chip-reject';
      case 'Hold':
        return 'chip-hold';
      case 'Select':
        return 'chip-select';
      default:
        return '';
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  navigateToAddSession(): void {
    this.router.navigate([
      '/site/interview/sessions/add',
      this.candidateId,
      this.positionId,
    ]);
  }

  viewSession(session: InterviewSession, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation(); // Prevent event bubbling if needed
    }
    
    this.router.navigate([
      '/site/interview/sessions/view', 
      session.id,
      this.candidateId,
      this.positionId
    ]);
  }

  editSession(session: InterviewSession, event: Event): void {
    event.stopPropagation();

    console.log('Editing session with ID:', session.id);
    // Include candidate and position IDs in the navigation
    this.router.navigate([
      '/site/interview/sessions/edit',
      session.id,
      this.candidateId,
      this.positionId,
    ]);
  }
  deleteSession(session: InterviewSession): void {
    if (
      confirm(
        `Are you sure you want to delete this interview session for round "${session.round_title}"?`
      )
    ) {
      this.interviewService.deleteInterviewSession(session.id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open(
              'Interview session deleted successfully',
              'Close',
              { duration: 3000 }
            );
            this.loadInterviewSessions();
          } else {
            this.snackBar.open('Failed to delete interview session', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting interview session:', error);
          this.snackBar.open('Error deleting interview session', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/site/interview/candidates/view', this.candidateId]);
  }
}
