<div class="interview-sessions-container">
    <div class="header-section">
      <div class="title-section">
        <button mat-icon-button color="primary" (click)="goBack()" matTooltip="Back to Candidate">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1 class="page-title">Interview Sessions</h1>
      </div>
      <button mat-raised-button color="primary" (click)="navigateToAddSession()">
        <mat-icon>add</mat-icon> Schedule Interview
      </button>
    </div>
  
    <mat-card class="info-card">
      <mat-card-content>
        <div class="candidate-info">
          <h2>{{ candidateName }}</h2>
          <p>Position: {{ positionTitle }}</p>
        </div>
      </mat-card-content>
    </mat-card>
  
    <div class="table-container">
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading interview sessions...</p>
      </div>
  
      <ng-container *ngIf="!isLoading">
        <ng-container *ngIf="interviewSessions.length > 0; else noSessions">
          <table mat-table [dataSource]="interviewSessions" matSort class="sessions-table">
            <!-- Interview Date Column -->
            <ng-container matColumnDef="interview_date">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Date & Time </th>
              <td mat-cell *matCellDef="let session">
                {{ session.interview_date | date:'medium' }}
              </td>
            </ng-container>
  
            <!-- Round Title Column -->
            <ng-container matColumnDef="round_title">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Round </th>
              <td mat-cell *matCellDef="let session"> {{ session.round_title }} </td>
            </ng-container>
  
            <!-- Interview Mode Column -->
            <ng-container matColumnDef="interview_mode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Mode </th>
              <td mat-cell *matCellDef="let session"> {{ session.interview_mode }} </td>
            </ng-container>
  
            <!-- Interviewer Column -->
            <ng-container matColumnDef="interviewer_name">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Interviewer </th>
              <td mat-cell *matCellDef="let session"> {{ session.interviewer_name }} </td>
            </ng-container>
  
            <!-- Outcome Column -->
            <ng-container matColumnDef="outcome">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Outcome </th>
              <td mat-cell *matCellDef="let session">
                <span class="outcome-chip" [ngClass]="getOutcomeChipClass(session.outcome)">
                  {{ session.outcome }}
                </span>
              </td>
            </ng-container>

            <ng-container matColumnDef="updated_by">
              <th mat-header-cell *matHeaderCellDef>Last Updated By</th>
              <td mat-cell *matCellDef="let session">
                {{session.updated_by_name || 'N/A'}}
              </td>
            </ng-container>
  
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef> Actions </th>
              <td mat-cell *matCellDef="let session">
                <button mat-icon-button color="primary" (click)="viewSession(session, $event)" matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button color="accent" (click)="editSession(session, $event)" matTooltip="Edit Session">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteSession(session)" matTooltip="Delete Session">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </ng-container>
  
        <ng-template #noSessions>
          <div class="no-data-container">
            <mat-icon class="no-data-icon">event_busy</mat-icon>
            <p class="no-data-text">No interview sessions scheduled yet</p>
            <button mat-raised-button color="primary" (click)="navigateToAddSession()">
              Schedule First Interview
            </button>
          </div>
        </ng-template>
      </ng-container>
    </div>
  </div>