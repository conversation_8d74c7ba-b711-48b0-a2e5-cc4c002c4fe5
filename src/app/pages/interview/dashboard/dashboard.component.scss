// src/app/pages/interview/dashboard/dashboard.component.scss
.dashboard-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  
  .dashboard-title {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 500;
    color: #333;
  }
  
  .header-actions {
    display: flex;
    gap: 0.75rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  
  .stat-card {
    border-radius: 8px;
    transition: transform 0.2s, box-shadow 0.2s;
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    
    .stat-content {
      padding: 1rem;
      
      .stat-value {
        font-size: 2rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }
      
      .stat-info {
        display: flex;
        align-items: center;
        
        .stat-icon {
          margin-right: 0.5rem;
          color: #5c6bc0;
        }
        
        .stat-label {
          flex-grow: 1;
          font-size: 0.9rem;
          color: #666;
        }
      }
    }
  }
}

.dashboard-section {
  margin-top: 1rem;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    .section-title {
      font-size: 1.3rem;
      margin: 0;
      color: #333;
    }
    
    .view-all {
      color: #5c6bc0;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .section-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 1rem;
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem;
    color: #888;
  }
  
  .candidates-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    
    .candidate-card {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      border-radius: 4px;
      border: 1px solid #eee;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      .candidate-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
      }
      
      .candidate-details {
        flex-grow: 1;
        
        .candidate-name {
          font-weight: 500;
        }
        
        .candidate-position {
          font-size: 0.9rem;
          color: #666;
        }
        
        .candidate-status {
          font-size: 0.8rem;
          padding: 2px 8px;
          border-radius: 12px;
          display: inline-block;
          margin-top: 4px;
          
          &.status-new {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          &.status-in-process {
            background-color: #fff8e1;
            color: #ffa000;
          }
          
          &.status-selected {
            background-color: #e8f5e9;
            color: #388e3c;
          }
          
          &.status-rejected {
            background-color: #ffebee;
            color: #d32f2f;
          }
        }
      }
    }
  }
  
  .interviews-list {
    overflow-x: auto;
    
    .interviews-table {
      width: 100%;
      border-collapse: collapse;
      
      th {
        text-align: left;
        padding: 0.75rem;
        border-bottom: 2px solid #eee;
        color: #555;
        font-weight: 500;
      }
      
      td {
        padding: 0.75rem;
        border-bottom: 1px solid #eee;
      }
      
      tbody tr {
        cursor: pointer;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
  }
  
  .candidates-list {
    grid-template-columns: 1fr;
  }
}