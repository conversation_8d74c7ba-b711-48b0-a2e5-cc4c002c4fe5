<!-- src/app/pages/interview/dashboard/dashboard.component.html -->
<div class="dashboard-container">
  <div class="dashboard-header">
    <h1 class="dashboard-title">Interview Dashboard</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" [routerLink]="['/site/interview/candidates/add']">
        <mat-icon>person_add</mat-icon> Add Candidate
      </button>
      <button mat-raised-button color="accent" [routerLink]="['/site/interview/positions/add']">
        <mat-icon>add_business</mat-icon> Add Position
      </button>
    </div>
  </div>

  <div *ngIf="error" class="error-message">
    {{error}}
    <button mat-button color="primary" (click)="loadDashboardData()">Retry</button>
  </div>

  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <div class="dashboard-content" *ngIf="!isLoading && !error">
    <!-- Stats Cards -->
    <div class="stats-grid">
      <!-- Total Candidates Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.totalCandidates || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">people</mat-icon>
            <div class="stat-label">Total Candidates</div>
            <button mat-icon-button [routerLink]="['/site/interview/candidates']" matTooltip="View Candidates">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Open Positions Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.openPositions || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">work</mat-icon>
            <div class="stat-label">Open Positions</div>
            <button mat-icon-button [routerLink]="['/site/interview/positions']" matTooltip="View Positions">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- In Process Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.inProcessCandidates || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">hourglass_top</mat-icon>
            <div class="stat-label">In Process</div>
            <button mat-icon-button [routerLink]="['/site/interview/candidates']" matTooltip="View Candidates">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Selected Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.selectedCandidates || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">how_to_reg</mat-icon>
            <div class="stat-label">Selected</div>
            <button mat-icon-button [routerLink]="['/site/interview/candidates']" matTooltip="View Candidates">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Today's Interviews Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.todayInterviews || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">event_available</mat-icon>
            <div class="stat-label">Today's Interviews</div>
            <button mat-icon-button [routerLink]="['/site/interview/sessions']" matTooltip="View Sessions">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Total Vacancies Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.totalVacancies || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">groups</mat-icon>
            <div class="stat-label">Total Vacancies</div>
            <button mat-icon-button [routerLink]="['/site/interview/positions']" matTooltip="View Positions">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Total Interviews Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.totalInterviews || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">people_outline</mat-icon>
            <div class="stat-label">Total Interviews</div>
            <button mat-icon-button [routerLink]="['/site/interview/sessions']" matTooltip="View All">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Hired Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.hiredCandidates || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">check_circle</mat-icon>
            <div class="stat-label">Hired</div>
            <button mat-icon-button [routerLink]="['/site/interview/candidates']" matTooltip="View All">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>

      <!-- Rejected Card -->
      <mat-card class="stat-card">
        <div class="stat-content">
          <div class="stat-value">{{stats.rejectedCandidates || 0}}</div>
          <div class="stat-info">
            <mat-icon class="stat-icon">cancel</mat-icon>
            <div class="stat-label">Rejected</div>
            <button mat-icon-button [routerLink]="['/site/interview/candidates']" matTooltip="View All">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Recent Candidates Section -->
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">Recent Candidates</h2>
        <a class="view-all" [routerLink]="['/site/interview/candidates']">View All</a>
      </div>
      
      <div class="section-content">
        <div *ngIf="recentCandidates.length === 0" class="empty-state">
          No recent candidates found.
        </div>
        
        <div *ngIf="recentCandidates.length > 0" class="candidates-list">
          <div *ngFor="let candidate of recentCandidates" class="candidate-card">
            <div class="candidate-avatar">
              <mat-icon>person</mat-icon>
            </div>
            <div class="candidate-details">
              <div class="candidate-name">{{formatName(candidate)}}</div>
              <div class="candidate-position">{{candidate.position_title || 'No position'}}</div>
              <div class="candidate-status" [ngClass]="getStatusClass(candidate.status)">{{candidate.status}}</div>
            </div>
            <button mat-icon-button color="primary" [routerLink]="['/site/interview/candidates/view', candidate.id]" matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Interviews Section -->
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">Upcoming Interviews</h2>
        <a class="view-all" [routerLink]="['/site/interview/sessions']">View All</a>
      </div>
      
      <div class="section-content">
        <div *ngIf="upcomingSessions.length === 0" class="empty-state">
          No upcoming interviews scheduled.
        </div>
        
        <div *ngIf="upcomingSessions.length > 0" class="interviews-list">
          <table class="interviews-table">
            <thead>
              <tr>
                <th>Date & Time</th>
                <th>Candidate</th>
                <th>Position</th>
                <th>Interviewer</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let session of upcomingSessions" 
                  [routerLink]="['/site/interview/sessions/view', session.id, session.candidate_id, session.position_id]">
                <td>{{formatDate(session.interview_date)}}</td>
                <td>{{formatName(session)}}</td>
                <td>{{session.position_title}}</td>
                <td>{{session.interviewer_name}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>