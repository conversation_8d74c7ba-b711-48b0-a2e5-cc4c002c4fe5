// src/app/pages/interview/dashboard/dashboard.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InterviewService } from '../../../services/interview.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-interview-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTableModule,
    MatTooltipModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class InterviewDashboardComponent implements OnInit, OnDestroy {
  stats: any = {
    totalCandidates: 0,
    openPositions: 0,
    inProcessCandidates: 0,
    selectedCandidates: 0,
    todayInterviews: 0,
    totalVacancies: 0,
    totalInterviews: 0,
    hiredCandidates: 0,
    rejectedCandidates: 0,
  };

  recentCandidates: any[] = [];
  upcomingSessions: any[] = [];
  isLoading = true;
  error = '';

  // For cleaning up subscriptions
  private destroy$ = new Subject<void>();

  constructor(private interviewService: InterviewService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadDashboardData(): void {
    this.isLoading = true;
    this.error = '';

    this.interviewService
      .getDashboardStats()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            console.log('Dashboard stats received:', response.stats);
            this.stats = response.stats;
            this.loadRecentCandidates();
          } else {
            this.error =
              response.message || 'Failed to load dashboard statistics';
            this.isLoading = false;
          }
        },
        error: (error) => {
          console.error('Error loading dashboard stats:', error);
          this.error = 'Error loading dashboard data. Please try again.';
          this.isLoading = false;
        },
      });
  }

  loadRecentCandidates(): void {
    this.interviewService
      .getRecentCandidates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.recentCandidates = response.candidates || [];
            this.loadUpcomingSessions();
          } else {
            this.error = response.message || 'Failed to load recent candidates';
            this.isLoading = false;
          }
        },
        error: (error) => {
          console.error('Error loading recent candidates:', error);
          this.recentCandidates = []; // Set empty array to avoid errors
          // Continue with upcoming sessions
          this.loadUpcomingSessions();
        },
      });
  }

  loadUpcomingSessions(): void {
    this.interviewService
      .getUpcomingSessions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.upcomingSessions = response.sessions || [];
          } else {
            this.error = response.message || 'Failed to load upcoming sessions';
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading upcoming sessions:', error);
          this.upcomingSessions = []; // Set empty array to avoid errors
          this.isLoading = false;
        },
      });
  }

  formatDate(date: string): string {
    if (!date) return 'N/A';

    const dateObj = new Date(date);

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date';
    }

    // Format: "Apr 29, 2025, 2:30 PM"
    return dateObj.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  }

  // Helper method for candidate name
  formatName(item: any): string {
    if (!item) return '';
    return `${item.firstName || ''} ${item.lastName || ''}`.trim();
  }

  // Helper method to get status class
  getStatusClass(status: string): string {
    if (!status) return '';

    return 'status-' + status.toLowerCase().replace(' ', '-');
  }
}
