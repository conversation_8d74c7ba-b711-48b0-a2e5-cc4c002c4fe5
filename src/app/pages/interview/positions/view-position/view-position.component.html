<div class="view-position-container">
    <div class="component-header">
      <h1 class="component-title">Position Details</h1>
      <div class="header-actions">
        <button mat-raised-button color="primary" [routerLink]="['/site/interview/positions']">
          <mat-icon>arrow_back</mat-icon> Back to Positions
        </button>
      </div>
    </div>
  
    <div class="content-wrapper">
      <div *ngIf="error" class="error-message">
        {{error}}
        <button mat-button color="primary" (click)="loadPositionData()">Retry</button>
      </div>
      
      <div *ngIf="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading position data...</span>
      </div>
  
      <ng-container *ngIf="!loading && position">
        <!-- Position Details Card -->
        <mat-card class="position-card">
          <mat-card-header>
            <mat-card-title class="position-title">
              {{position.title}}
              <span class="status-badge" [ngClass]="'status-' + position.status.toLowerCase().replace(' ', '-')">
                {{position.status}}
              </span>
            </mat-card-title>
            <mat-card-subtitle>{{position.department_name}}</mat-card-subtitle>
          </mat-card-header>
  
          <mat-card-content>
            <div class="position-stats">
              <div class="stat-item">
                <div class="stat-label">Vacancies</div>
                <div class="stat-value">
                  <button mat-icon-button color="warn" matTooltip="Decrease" (click)="toggleVacancy(-1)">
                    <mat-icon>remove_circle</mat-icon>
                  </button>
                  <span>{{position.vacancies}}</span>
                  <button mat-icon-button color="primary" matTooltip="Increase" (click)="toggleVacancy(1)">
                    <mat-icon>add_circle</mat-icon>
                  </button>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Priority</div>
                <div class="stat-value">
                  <span class="priority-badge" [ngClass]="'priority-' + position.priority.toLowerCase()">
                    {{position.priority}}
                  </span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Experience</div>
                <div class="stat-value">{{position.min_experience}} - {{position.max_experience}} years</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Candidates</div>
                <div class="stat-value">{{candidates.length || 0}}</div>
              </div>
            </div>
  
            <mat-divider></mat-divider>
  
            <mat-tab-group>
              <mat-tab label="Details">
                <div class="tab-content">
                  <div class="detail-section">
                    <h3>Required Skills</h3>
                    <p>{{position.required_skills}}</p>
                  </div>
                  
                  <div class="detail-section">
                    <h3>Required Qualifications</h3>
                    <p>{{position.required_qualifications}}</p>
                  </div>
                  
                  <div class="detail-section">
                    <h3>Additional Information</h3>
                    <div class="info-grid">
                      <div class="info-item">
                        <span class="label">Created By:</span>
                        <span>{{position.created_by_username}}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">Created On:</span>
                        <span>{{position.created_at | date:'medium'}}</span>
                      </div>
                      <div class="info-item" *ngIf="position.updated_by_username">
                        <span class="label">Updated By:</span>
                        <span>{{position.updated_by_username}}</span>
                      </div>
                      <div class="info-item" *ngIf="position.updated_at">
                        <span class="label">Updated On:</span>
                        <span>{{position.updated_at | date:'medium'}}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </mat-tab>
              
              <mat-tab label="Candidates ({{candidates.length || 0}})">
                <div class="tab-content">
                  <div *ngIf="candidates.length === 0" class="no-data">
                    No candidates have applied for this position yet.
                  </div>
                  
                  <table *ngIf="candidates.length > 0" mat-table [dataSource]="candidates" class="candidate-table">
                    <!-- Name Column -->
                    <ng-container matColumnDef="name">
                      <th mat-header-cell *matHeaderCellDef>Name</th>
                      <td mat-cell *matCellDef="let candidate">
                        {{candidate.first_name}} {{candidate.last_name}}
                      </td>
                    </ng-container>
                    
                    <!-- Email Column -->
                    <ng-container matColumnDef="email">
                      <th mat-header-cell *matHeaderCellDef>Email</th>
                      <td mat-cell *matCellDef="let candidate">{{candidate.email}}</td>
                    </ng-container>
                    
                    <!-- Experience Column -->
                    <ng-container matColumnDef="experience">
                      <th mat-header-cell *matHeaderCellDef>Experience</th>
                      <td mat-cell *matCellDef="let candidate">{{candidate.total_experience}} years</td>
                    </ng-container>
                    
                    <!-- Current Company Column -->
                    <ng-container matColumnDef="currentCompany">
                      <th mat-header-cell *matHeaderCellDef>Current Company</th>
                      <td mat-cell *matCellDef="let candidate">{{candidate.current_organization || 'N/A'}}</td>
                    </ng-container>
                    
                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let candidate">
                        <span class="status-badge" [ngClass]="getStatusClass(candidate.status)">
                          {{candidate.status}}
                        </span>
                      </td>
                    </ng-container>
                    
                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let candidate">
                        <button mat-icon-button color="primary" (click)="viewCandidate(candidate.candidate_id)" matTooltip="View Candidate">
                          <mat-icon>person</mat-icon>
                        </button>
                        <button mat-icon-button color="accent" (click)="viewInterviews(candidate.candidate_id)" matTooltip="View Interviews">
                          <mat-icon>event_note</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" (click)="viewDecision(candidate.candidate_id)" matTooltip="View/Make Decision">
                          <mat-icon>gavel</mat-icon>
                        </button>
                      </td>
                    </ng-container>
                    
                    <tr mat-header-row *matHeaderRowDef="candidatesDisplayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: candidatesDisplayedColumns;"></tr>
                  </table>
                </div>
              </mat-tab>
            </mat-tab-group>
          </mat-card-content>
  
          <mat-card-actions>
            <button mat-raised-button color="primary" [routerLink]="['/site/interview/positions/edit', position.id]">
              <mat-icon>edit</mat-icon> Edit Position
            </button>
            <button mat-raised-button [color]="position.status === 'Open' ? 'warn' : 'accent'" (click)="toggleStatus()">
              <mat-icon>{{position.status === 'Open' ? 'pause' : 'play_arrow'}}</mat-icon>
              {{position.status === 'Open' ? 'Put On Hold' : 'Reopen Position'}}
            </button>
            <button mat-raised-button color="primary" [routerLink]="['/site/interview/candidates/add']" [queryParams]="{position_id: position.id}">
              <mat-icon>person_add</mat-icon> Add Candidate
            </button>
          </mat-card-actions>
        </mat-card>
      </ng-container>
    </div>
  </div>