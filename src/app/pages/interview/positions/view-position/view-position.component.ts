import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { InterviewService } from '../../../../services/interview.service';

@Component({
  selector: 'app-view-position',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatChipsModule,
    MatTooltipModule,
    MatTabsModule,
    MatDividerModule,
    MatSnackBarModule,
  ],
  templateUrl: './view-position.component.html',
  styleUrls: ['./view-position.component.scss'],
})
export class ViewPositionComponent implements OnInit {
  positionId!: number;
  position: any = null;
  candidates: any[] = [];
  loading = true;
  error = '';

  candidatesDisplayedColumns: string[] = [
    'name',
    'email',
    'experience',
    'currentCompany',
    'status',
    'actions',
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.positionId = +params['id'];
      this.loadPositionData();
    });
  }

  loadPositionData(): void {
    this.loading = true;

    // Load position details
    this.interviewService.getPositionById(this.positionId).subscribe({
      next: (response) => {
        if (response.success) {
          this.position = response.position;
          // After loading position, load its candidates
          this.loadCandidates();
        } else {
          this.error = response.message || 'Failed to load position details';
          this.loading = false;
        }
      },
      error: (err) => {
        console.error('Error loading position:', err);
        this.error = 'Error loading position details. Please try again later.';
        this.loading = false;
      },
    });
  }

  loadCandidates(): void {
    this.interviewService.getPositionCandidates(this.positionId).subscribe({
      next: (response) => {
        if (response.success) {
          this.candidates = response.candidates || [];
        } else {
          this.error = response.message || 'Failed to load candidates';
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading candidates:', err);
        this.error = 'Error loading candidates. Please try again later.';
        this.loading = false;
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'applied':
        return 'status-applied';
      case 'in process':
        return 'status-in-process';
      case 'selected':
        return 'status-selected';
      case 'rejected':
        return 'status-rejected';
      default:
        return '';
    }
  }

  toggleVacancy(amount: number): void {
    if (!this.position) return;

    const newVacancies = this.position.vacancies + amount;
    if (newVacancies < 0) {
      this.snackBar.open('Vacancies cannot be less than 0', 'Close', {
        duration: 3000,
      });
      return;
    }

    this.interviewService
      .updateVacancies(this.positionId, newVacancies)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.position.vacancies = newVacancies;
            this.position.status = newVacancies > 0 ? 'Open' : 'Filled';
            this.snackBar.open('Vacancies updated successfully', 'Close', {
              duration: 3000,
            });
          } else {
            this.snackBar.open(
              response.message || 'Failed to update vacancies',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (err) => {
          console.error('Error updating vacancies:', err);
          this.snackBar.open('Error updating vacancies', 'Close', {
            duration: 3000,
          });
        },
      });
  }

  toggleStatus(): void {
    if (!this.position) return;

    this.interviewService.togglePositionStatus(this.positionId).subscribe({
      next: (response) => {
        if (response.success) {
          // Update local status
          this.position.status =
            this.position.status === 'Open' ? 'On Hold' : 'Open';
          this.snackBar.open('Position status updated successfully', 'Close', {
            duration: 3000,
          });
        } else {
          this.snackBar.open(
            response.message || 'Failed to update status',
            'Close',
            { duration: 3000 }
          );
        }
      },
      error: (err) => {
        console.error('Error updating status:', err);
        this.snackBar.open('Error updating status', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  viewCandidate(candidateId: number): void {
    this.router.navigate(['/site/interview/candidates/view', candidateId]);
  }

  viewInterviews(candidateId: number): void {
    this.router.navigate([
      '/site/interview/sessions',
      candidateId,
      this.positionId,
    ]);
  }

  viewDecision(candidateId: number): void {
    this.router.navigate([
      '/site/interview/decision',
      candidateId,
      this.positionId,
    ]);
  }
}
