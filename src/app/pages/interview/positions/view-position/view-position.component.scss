.view-position-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
  
      .header-actions {
        button {
          display: flex;
          align-items: center;
          
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
  
    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 1000px;
      margin: 0 auto;
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background-color: white;
      border-radius: 4px;
      
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  
    .position-card {
      background-color: white;
      border-radius: 4px;
      margin-bottom: 24px;
      
      .position-title {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 22px;
        font-weight: 500;
      }
      
      .position-stats {
        display: flex;
        flex-wrap: wrap;
        margin: 16px 0;
        
        .stat-item {
          min-width: 150px;
          flex: 1;
          padding: 16px;
          border-right: 1px solid #eee;
          
          &:last-child {
            border-right: none;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }
          
          .stat-value {
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }
      
      .tab-content {
        padding: 24px 8px;
        
        .detail-section {
          margin-bottom: 24px;
          
          h3 {
            font-size: 18px;
            margin-bottom: 12px;
            color: #333;
          }
          
          p {
            margin: 0;
            white-space: pre-line;
            line-height: 1.5;
          }
          
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            
            .info-item {
              display: flex;
              flex-direction: column;
              
              .label {
                font-size: 14px;
                color: #666;
                margin-bottom: 4px;
              }
            }
          }
        }
      }
      
      .candidate-table {
        width: 100%;
        
        .mat-column-actions {
          width: 140px;
          text-align: center;
        }
      }
      
      .no-data {
        padding: 24px;
        text-align: center;
        color: #666;
      }
      
      mat-card-actions {
        padding: 16px;
        display: flex;
        gap: 16px;
        
        button {
          display: flex;
          align-items: center;
          
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    .status-badge, .priority-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-open {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    
    .status-filled {
      background-color: #e3f2fd;
      color: #0d47a1;
    }
    
    .status-on-hold {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    .status-applied {
      background-color: #e8eaf6;
      color: #3f51b5;
    }
    
    .status-in-process {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    .status-selected {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    
    .status-rejected {
      background-color: #ffebee;
      color: #b71c1c;
    }
    
    .priority-high {
      background-color: #ffebee;
      color: #b71c1c;
    }
    
    .priority-medium {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    
    .priority-low {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .view-position-container {
      .component-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
      
      .position-card {
        .position-stats {
          flex-direction: column;
          
          .stat-item {
            border-right: none;
            border-bottom: 1px solid #eee;
            padding: 12px 0;
            
            &:last-child {
              border-bottom: none;
            }
          }
        }
        
        mat-card-actions {
          flex-direction: column;
          align-items: stretch;
        }
      }
    }
  }