import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { SelectionModel } from '@angular/cdk/collections';
import { InterviewService } from '../../../../services/interview.service';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-list-positions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSelectModule,
    MatChipsModule,
    MatCardModule,
    MatDialogModule,
    MatSnackBarModule,
    MatCheckboxModule,
  ],
  templateUrl: './list-positions.component.html',
  styleUrls: ['./list-positions.component.scss'],
})
export class ListPositionsComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'title',
    'department_name',
    'vacancies',
    'priority',
    'status',
    'active_candidates',
    'actions',
  ];
  dataSource = new MatTableDataSource<any>([]);
  selection = new SelectionModel<any>(true, []);

  loading = false;
  error = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private interviewService: InterviewService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadPositions();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadPositions(): void {
    this.loading = true;
    this.interviewService.getAllPositions().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.positions;
        } else {
          this.error = response.message || 'Failed to load positions';
        }
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Error loading positions. Please try again later.';
        this.loading = false;
        console.error('Error loading positions:', err);
      },
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.data.forEach((row) => this.selection.select(row));
    }
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelectedPositions();
      }
    });
  }

  deleteSelectedPositions(): void {
    if (this.selection.selected.length === 0) {
      this.snackBar.open('No positions selected', 'Close', { duration: 3000 });
      return;
    }

    const ids = this.selection.selected.map((position) => position.id);
    console.log('Positions selected for deletion:', this.selection.selected);
    console.log('IDs for deletion:', ids);

    // For troubleshooting, let's try deleting one position at a time
    if (ids.length === 1) {
      // If only one position is selected, use the single delete method
      this.interviewService.deletePosition(ids[0]).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Position deleted successfully', 'Close', {
              duration: 3000,
            });
            this.selection.clear();
            this.loadPositions();
          } else {
            this.snackBar.open(
              response.message || 'Failed to delete position',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (err) => {
          console.error('Error deleting position:', err);
          this.snackBar.open('Error deleting position', 'Close', {
            duration: 3000,
          });
        },
      });
    } else {
      // Multi-delete
      this.interviewService.deletePositions(ids).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Positions deleted successfully', 'Close', {
              duration: 3000,
            });
            this.selection.clear();
            this.loadPositions();
          } else {
            this.snackBar.open(
              response.message || 'Failed to delete positions',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (err) => {
          console.error('Error deleting positions:', err);
          this.snackBar.open('Error deleting positions', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  togglePositionStatus(id: number): void {
    this.interviewService.togglePositionStatus(id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Position status updated', 'Close', {
            duration: 3000,
          });
          this.loadPositions();
        } else {
          this.snackBar.open(
            response.message || 'Failed to update position status',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (err) => {
        console.error('Error updating position status:', err);
        this.snackBar.open('Error updating position status', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Open':
        return 'status-open';
      case 'Filled':
        return 'status-filled';
      case 'On Hold':
        return 'status-hold';
      default:
        return '';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'High':
        return 'priority-high';
      case 'Medium':
        return 'priority-medium';
      case 'Low':
        return 'priority-low';
      default:
        return '';
    }
  }
}
