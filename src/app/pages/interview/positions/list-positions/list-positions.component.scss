.positions-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      margin-bottom: 24px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  
    .actions-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      
      .search-field {
        width: 300px;
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      button {
        margin-left: 16px;
      }
    }
    
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background-color: white;
      border-radius: 4px;
      
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  
    .table-container {
      background-color: white;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      table {
        width: 100%;
        
        .mat-column-select {
          width: 60px;
        }
        
        .mat-column-actions {
          width: 180px;
          text-align: center;
        }
        
        tr.selected-row {
          background-color: rgba(63, 81, 181, 0.1);
        }
      }
      
      .priority-badge, .status-badge {
        padding: 4px 8px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .priority-high {
        background-color: #ffebee;
        color: #b71c1c;
      }
      
      .priority-medium {
        background-color: #fff8e1;
        color: #ff8f00;
      }
      
      .priority-low {
        background-color: #e8f5e9;
        color: #2e7d32;
      }
      
      .status-open {
        background-color: #e8f5e9;
        color: #2e7d32;
      }
      
      .status-filled {
        background-color: #e3f2fd;
        color: #0d47a1;
      }
      
      .status-hold {
        background-color: #fff8e1;
        color: #ff8f00;
      }
    }
  }


  
  // Responsive adjustments
  @media (max-width: 768px) {
    .positions-container {
      .actions-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .search-field {
          width: 100%;
        }
      }
      
      .table-container {
        overflow-x: auto;
      }
    }
  }