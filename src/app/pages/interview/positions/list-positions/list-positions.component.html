<div class="positions-container">
    <div class="component-header">
      <h1 class="component-title">Positions Management</h1>
    </div>
  
    <div class="content-wrapper">
      <div class="actions-row">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search Positions</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Search by title, department, etc.">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
  
        <div class="action-buttons">
          <button mat-raised-button color="primary" routerLink="../positions/add">
            <mat-icon>add</mat-icon> Add Position
          </button>
          <button mat-raised-button color="warn" (click)="openDeleteConfirmationDialog()" [disabled]="!selection.hasValue()">
            <mat-icon>delete</mat-icon> Delete Selected
          </button>
        </div>
      </div>
  
      <div class="error-message" *ngIf="error">
        {{error}}
        <button mat-button color="primary" (click)="loadPositions()">Retry</button>
      </div>
      
      <div class="loading-indicator" *ngIf="loading">
        <div class="spinner"></div>
        <span>Loading positions...</span>
      </div>
      
      <div class="table-container" *ngIf="!loading && !error">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)">
              </mat-checkbox>
            </td>
          </ng-container>
  
          <!-- Title Column -->
          <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Position Title</th>
            <td mat-cell *matCellDef="let element">{{element.title}}</td>
          </ng-container>
  
          <!-- Department Column -->
          <ng-container matColumnDef="department_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Department</th>
            <td mat-cell *matCellDef="let element">{{element.department_name}}</td>
          </ng-container>
  
          <!-- Vacancies Column -->
          <ng-container matColumnDef="vacancies">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Vacancies</th>
            <td mat-cell *matCellDef="let element">{{element.vacancies}}</td>
          </ng-container>
  
          <!-- Priority Column -->
          <ng-container matColumnDef="priority">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Priority</th>
            <td mat-cell *matCellDef="let element">
              <span class="priority-badge" [ngClass]="getPriorityClass(element.priority)">
                {{element.priority}}
              </span>
            </td>
          </ng-container>
  
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let element">
              <span class="status-badge" [ngClass]="getStatusClass(element.status)">
                {{element.status}}
              </span>
            </td>
          </ng-container>
  
          <!-- Active Candidates Column -->
          <ng-container matColumnDef="active_candidates">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Applications</th>
            <td mat-cell *matCellDef="let element">{{element.active_candidates || 0}}</td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" [routerLink]="['../positions/view', element.id]" matTooltip="View Candidates">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent" [routerLink]="['../positions/edit', element.id]" matTooltip="Edit Position">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="togglePositionStatus(element.id); $event.stopPropagation()"
                      [matTooltip]="element.status === 'Open' ? 'Put On Hold' : 'Reopen Position'">
                <mat-icon>{{ element.status === 'Open' ? 'pause_circle' : 'play_circle' }}</mat-icon>
              </button>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
              (click)="selection.toggle(row)"
              [class.selected-row]="selection.isSelected(row)">
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>