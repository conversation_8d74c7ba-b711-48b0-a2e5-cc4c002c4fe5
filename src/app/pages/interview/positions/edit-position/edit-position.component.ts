import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { InterviewService } from '../../../../services/interview.service';
import { MasterserviceService } from '../../../../services/masterservice.service';

@Component({
  selector: 'app-edit-position',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    RouterModule,
  ],
  templateUrl: './edit-position.component.html',
  styleUrls: ['./edit-position.component.scss'],
})
export class EditPositionComponent implements OnInit {
  positionForm!: FormGroup;
  departments: any[] = [];
  priorities = ['High', 'Medium', 'Low'];
  statuses = ['Open', 'Filled', 'On Hold'];
  positionId!: number;
  loading = true;
  error = '';

  constructor(
    private fb: FormBuilder,
    private interviewService: InterviewService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadDepartments();
    this.route.params.subscribe((params) => {
      this.positionId = +params['id'];
      this.loadPosition();
    });
  }

  initForm(): void {
    this.positionForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      department_id: ['', Validators.required],
      required_skills: ['', Validators.required],
      required_qualifications: ['', Validators.required],
      min_experience: [0, [Validators.required, Validators.min(0)]],
      max_experience: [0, [Validators.required, Validators.min(0)]],
      vacancies: [1, [Validators.required, Validators.min(1)]],
      priority: ['Medium', Validators.required],
      status: ['Open', Validators.required],
    });
  }

  loadDepartments(): void {
    this.masterService.getAllDepartments().subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.departments;
        } else {
          this.error = 'Failed to load departments';
        }
      },
      error: (err) => {
        console.error('Error loading departments:', err);
        this.error = 'Error loading departments. Please try again later.';
      },
    });
  }

  loadPosition(): void {
    this.loading = true;
    this.interviewService.getPositionById(this.positionId).subscribe({
      next: (response) => {
        if (response.success) {
          this.positionForm.patchValue({
            title: response.position.title,
            department_id: response.position.department_id,
            required_skills: response.position.required_skills,
            required_qualifications: response.position.required_qualifications,
            min_experience: response.position.min_experience,
            max_experience: response.position.max_experience,
            vacancies: response.position.vacancies,
            priority: response.position.priority,
            status: response.position.status,
          });
        } else {
          this.error = response.message || 'Failed to load position details';
        }
        this.loading = false;
      },
      error: (err) => {
        this.loading = false;
        console.error('Error loading position:', err);
        this.error = 'Error loading position details. Please try again later.';
      },
    });
  }

  onSubmit(): void {
    if (this.positionForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.positionForm.controls).forEach((key) => {
        const control = this.positionForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.loading = true;
    const positionData = this.positionForm.value;

    this.interviewService
      .updatePosition(this.positionId, positionData)
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.success) {
            this.snackBar.open('Position updated successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['/site/interview/positions']);
          } else {
            this.error = response.message || 'Failed to update position';
          }
        },
        error: (err) => {
          this.loading = false;
          console.error('Error updating position:', err);
          this.error = 'Error updating position. Please try again later.';
        },
      });
  }

  cancel(): void {
    this.router.navigate(['/site/interview/positions']);
  }

  // Helper methods for form validation
  get title() {
    return this.positionForm.get('title');
  }
  get department_id() {
    return this.positionForm.get('department_id');
  }
  get required_skills() {
    return this.positionForm.get('required_skills');
  }
  get required_qualifications() {
    return this.positionForm.get('required_qualifications');
  }
  get min_experience() {
    return this.positionForm.get('min_experience');
  }
  get max_experience() {
    return this.positionForm.get('max_experience');
  }
  get vacancies() {
    return this.positionForm.get('vacancies');
  }
}
