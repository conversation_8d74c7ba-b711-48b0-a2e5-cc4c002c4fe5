<div class="edit-position-container">
    <div class="component-header">
      <h1 class="component-title">Edit Position</h1>
    </div>
  
    <div class="content-wrapper">
      <div *ngIf="error" class="error-message">
        {{error}}
        <button mat-button color="primary" (click)="error = ''">Dismiss</button>
      </div>
  
      <div *ngIf="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading position data...</span>
      </div>
  
      <mat-card *ngIf="!loading">
        <mat-card-content>
          <form [formGroup]="positionForm" (ngSubmit)="onSubmit()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Position Title</mat-label>
                <input matInput formControlName="title" placeholder="e.g. Pharmacist">
                <mat-error *ngIf="title?.errors?.['required']">Title is required</mat-error>
                <mat-error *ngIf="title?.errors?.['minlength']">Title must be at least 3 characters</mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Department</mat-label>
                <mat-select formControlName="department_id">
                  <mat-option *ngFor="let dept of departments" [value]="dept.id">
                    {{dept.name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="department_id?.errors?.['required']">Department is required</mat-error>
              </mat-form-field>
            </div>
  
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Minimum Experience (years)</mat-label>
                <input matInput type="number" formControlName="min_experience" min="0" step="0.5">
                <mat-error *ngIf="min_experience?.errors?.['required']">Minimum experience is required</mat-error>
                <mat-error *ngIf="min_experience?.errors?.['min']">Experience cannot be negative</mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Maximum Experience (years)</mat-label>
                <input matInput type="number" formControlName="max_experience" min="0" step="0.5">
                <mat-error *ngIf="max_experience?.errors?.['required']">Maximum experience is required</mat-error>
                <mat-error *ngIf="max_experience?.errors?.['min']">Experience cannot be negative</mat-error>
              </mat-form-field>
            </div>
  
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Number of Vacancies</mat-label>
                <input matInput type="number" formControlName="vacancies" min="1">
                <mat-error *ngIf="vacancies?.errors?.['required']">Number of vacancies is required</mat-error>
                <mat-error *ngIf="vacancies?.errors?.['min']">At least 1 vacancy is required</mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Priority</mat-label>
                <mat-select formControlName="priority">
                  <mat-option *ngFor="let priority of priorities" [value]="priority">
                    {{priority}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
  
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status">
                  <mat-option *ngFor="let status of statuses" [value]="status">
                    {{status}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
  
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Required Skills</mat-label>
              <textarea matInput formControlName="required_skills" rows="3" placeholder="List key skills separated by commas"></textarea>
              <mat-error *ngIf="required_skills?.errors?.['required']">Required skills are required</mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Required Qualifications</mat-label>
              <textarea matInput formControlName="required_qualifications" rows="3" placeholder="List qualifications needed for this position"></textarea>
              <mat-error *ngIf="required_qualifications?.errors?.['required']">Required qualifications are required</mat-error>
            </mat-form-field>
  
            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="loading">
                <mat-icon>save</mat-icon> Update Position
              </button>
              <button mat-raised-button color="warn" type="button" (click)="cancel()" [disabled]="loading">
                <mat-icon>cancel</mat-icon> Cancel
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>