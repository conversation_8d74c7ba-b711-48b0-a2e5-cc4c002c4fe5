.edit-position-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      margin-bottom: 24px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .content-wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 800px;
      margin: 0 auto;
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      background-color: white;
      border-radius: 4px;
      
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border-left-color: #3f51b5;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  
    mat-card {
      background-color: white;
      border-radius: 4px;
  
      mat-card-content {
        padding: 16px;
  
        form {
          display: flex;
          flex-direction: column;
          gap: 16px;
  
          .form-row {
            display: flex;
            gap: 16px;
            
            mat-form-field {
              flex: 1;
            }
          }
  
          .full-width {
            width: 100%;
          }
  
          .form-actions {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            margin-top: 24px;
  
            button {
              display: flex;
              align-items: center;
              
              mat-icon {
                margin-right: 8px;
              }
            }
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .edit-position-container {
      .content-wrapper {
        form {
          .form-row {
            flex-direction: column;
            gap: 0;
          }
        }
      }
    }
  }