<div class="schedule-interview-container">
    <div class="component-header">
      <div class="title-section">
        <button mat-icon-button color="primary" [routerLink]="['/site/interview/candidates/view', candidateId]" matTooltip="Back to Candidate">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1 class="component-title">Schedule Interview</h1>
      </div>
    </div>
  
    <div class="content-wrapper">
      <mat-card>
        <mat-card-content>
          <form [formGroup]="interviewForm" (ngSubmit)="onSubmit()">
            <!-- Position Selection -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Position</mat-label>
              <mat-select formControlName="position_id" required>
                <mat-option *ngFor="let position of positions" [value]="position.position_id">
                  {{position.position_title}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="interviewForm.get('position_id')?.hasError('required')">
                Position is required
              </mat-error>
            </mat-form-field>
  
            <!-- Round Title -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Interview Round</mat-label>
              <mat-select formControlName="round_title" required>
                <mat-option *ngFor="let round of roundTitles" [value]="round">
                  {{round}} Round
                </mat-option>
              </mat-select>
              <mat-error *ngIf="interviewForm.get('round_title')?.hasError('required')">
                Interview round is required
              </mat-error>
            </mat-form-field>
  
            <!-- Date and Time -->
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Interview Date</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="interview_date" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-error *ngIf="interviewForm.get('interview_date')?.hasError('required')">
                  Date is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Interview Time</mat-label>
                <input matInput type="time" formControlName="interview_time" required>
                <mat-error *ngIf="interviewForm.get('interview_time')?.hasError('required')">
                  Time is required
                </mat-error>
              </mat-form-field>
            </div>
  
            <!-- Interview Mode -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Interview Mode</mat-label>
              <mat-select formControlName="interview_mode" required>
                <mat-option *ngFor="let mode of interviewModes" [value]="mode">
                  {{mode}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="interviewForm.get('interview_mode')?.hasError('required')">
                Interview mode is required
              </mat-error>
            </mat-form-field>
  
            <!-- Comments -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Comments</mat-label>
              <textarea matInput formControlName="comments" rows="3" 
                        placeholder="Any special instructions or notes"></textarea>
            </mat-form-field>
  
            <!-- Form Actions -->
            <div class="form-actions">
              <button mat-button type="button" [routerLink]="['/site/interview/candidates/view', candidateId]">
                Cancel
              </button>
              <button mat-raised-button color="primary" type="submit" [disabled]="interviewForm.invalid || isSubmitting">
                Schedule Interview
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>