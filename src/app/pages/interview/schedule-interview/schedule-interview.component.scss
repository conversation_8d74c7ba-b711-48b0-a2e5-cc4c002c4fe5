.schedule-interview-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .title-section {
        display: flex;
        align-items: center;
        gap: 12px;
      }
  
      .component-title {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
  
    .content-wrapper {
      mat-card {
        padding: 16px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
    }
  
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
  
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
  
      mat-form-field {
        flex: 1;
      }
    }
  
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
    }
  
    @media (max-width: 600px) {
      .form-row {
        flex-direction: column;
        gap: 0;
      }
    }
  }