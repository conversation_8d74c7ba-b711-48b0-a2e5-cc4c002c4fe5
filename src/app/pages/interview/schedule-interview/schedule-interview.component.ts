import { Component, OnInit } from '@angular/core';
import {
  <PERSON><PERSON>uilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { InterviewService } from '../../../services/interview.service';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-schedule-interview',
  templateUrl: './schedule-interview.component.html',
  styleUrls: ['./schedule-interview.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatSnackBarModule,
  ],
})
export class ScheduleInterviewComponent implements OnInit {
  interviewForm: FormGroup;
  candidateId: number = 0; // Initialize here to fix the error
  positions: any[] = [];
  isSubmitting = false;

  interviewModes = ['In-person', 'Video', 'Phone'];
  roundTitles = ['Initial', 'Technical', 'Final'];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private interviewService: InterviewService,
    private snackBar: MatSnackBar
  ) {
    this.interviewForm = this.fb.group({});
  }
  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    this.candidateId = idParam ? +idParam : 0;

    if (!this.candidateId) {
      this.snackBar.open('Invalid candidate ID', 'Close', { duration: 3000 });
      this.router.navigate(['/site/interview/candidates']);
      return;
    }

    this.interviewForm = this.fb.group({
      candidate_id: [this.candidateId, Validators.required],
      position_id: ['', Validators.required],
      round_title: ['Initial', Validators.required],
      interview_date: ['', Validators.required],
      interview_time: ['', Validators.required],
      interview_mode: ['In-person', Validators.required],
      comments: [''],
    });

    this.loadPositions();
  }

  loadPositions(): void {
    this.interviewService.getCandidatePositions(this.candidateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.positions = response.positions || [];
          if (this.positions.length > 0) {
            this.interviewForm.patchValue({
              position_id: this.positions[0].position_id,
            });
          }
        }
      },
      error: (err) => {
        console.error('Error loading positions:', err);
        this.snackBar.open('Failed to load positions', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  onSubmit(): void {
    if (this.interviewForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.interviewForm.controls).forEach((key) => {
        this.interviewForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // Combine date and time
    const dateValue = this.interviewForm.get('interview_date')?.value;
    const timeValue = this.interviewForm.get('interview_time')?.value;

    if (!dateValue || !timeValue) {
      this.snackBar.open('Date and time are required', 'Close', {
        duration: 3000,
      });
      this.isSubmitting = false;
      return;
    }

    const interviewDate = new Date(dateValue);
    const [hours, minutes] = timeValue.split(':');
    interviewDate.setHours(parseInt(hours), parseInt(minutes));

    const formData = {
      ...this.interviewForm.value,
      interview_date: interviewDate.toISOString(),
    };

    // Remove the separate time field
    delete formData.interview_time;

    this.interviewService.addInterviewSession(formData).subscribe({
      next: (response) => {
        this.isSubmitting = false;

        if (response.success) {
          this.snackBar.open('Interview scheduled successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate([
            '/site/interview/candidates/view',
            this.candidateId,
          ]);
        } else {
          this.snackBar.open(
            'Failed to schedule interview: ' + response.message,
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (err) => {
        this.isSubmitting = false;
        this.snackBar.open('Error scheduling interview', 'Close', {
          duration: 3000,
        });
        console.error('Error scheduling interview:', err);
      },
    });
  }
}
