import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-add-employment-type',
  standalone: true,
  imports: [
    MatTableModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './add-employment-type.component.html',
  styleUrl: './add-employment-type.component.scss',
})
export class AddEmploymentTypeComponent {
  readonly name = new FormControl('', [
    Validators.required,
    Validators.minLength(2),
  ]);

  readonly description = new FormControl('', [
    Validators.required,
    Validators.minLength(2),
  ]);

  employmentTypeForm!: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService
  ) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.employmentTypeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      description: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    if (this.employmentTypeForm.valid) {
      this.masterService
        .addEmploymentType(this.employmentTypeForm.value)
        .subscribe(
          (res) => {
            this.snackBar.open('Employment Type Added Successfully', 'Close', {
              duration: 2000,
            });
            this.employmentTypeForm.reset();
          },
          (error) => {
            this.errorMessage.set(error.error.message);
          }
        );
    }
  }

  updateErrorMessage() {
    this.errorMessage.set('');
  }

  cancel() {
    this.employmentTypeForm.reset();
  }
}
