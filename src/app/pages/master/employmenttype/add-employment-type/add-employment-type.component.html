<div class="add-employment-type-container">
    <div class="component-header">
        <h1 class="component-title">Add New Employment Type</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="employmentTypeForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Employment Type Name</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (name.invalid) {
                        <mat-error>{{errorMessage()}}</mat-error>
                        }
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Description</mat-label>
                        <textarea matInput formControlName="description" (blur)="updateErrorMessage()" required></textarea>
                        @if (description.invalid) {
                        <mat-error>{{errorMessage()}}</mat-error>
                        }
                    </mat-form-field>

                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="employmentTypeForm.invalid">
                            <mat-icon>add</mat-icon> Add Employment Type
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>