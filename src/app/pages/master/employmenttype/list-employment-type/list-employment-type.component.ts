import { Component, OnInit, ViewChild, signal } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

interface EmploymentType {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  updated_at: string;
  created_at: string;
  created_by: number;
  created_by_username: string;
  updated_by: number;
  sl_no?: number;
}

@Component({
  selector: 'app-list-employment-types',
  templateUrl: './list-employment-type.component.html',
  styleUrls: ['./list-employment-type.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
    MatSlideToggleModule,
  ],
})
export class ListEmploymentTypeComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'description',
    'is_active',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<EmploymentType>([]);
  selection = new SelectionModel<EmploymentType>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchEmploymentTypesData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addEmploymentType() {
    this.router.navigate(['/site/master/employmenttype/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  editEmploymentType(employmentType: EmploymentType) {
    this.router.navigate([
      '/site/master/employmenttype/edit',
      employmentType.id,
    ]);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedEmploymentTypes = this.selection.selected;
    if (selectedEmploymentTypes.length > 0) {
      const employmentTypeIds = selectedEmploymentTypes.map(
        (employmentType) => employmentType.id
      );

      this.masterService.deleteEmploymentTypes(employmentTypeIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchEmploymentTypesData();
            this.snackBar.open(
              'Employment types deleted successfully',
              'Close',
              {
                duration: 2000,
              }
            );
          } else {
            console.error(
              'Failed to delete employment types:',
              response.message
            );
            this.snackBar.open('Error deleting employment types', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting employment types:', error);
        },
      });
    }
  }

  toggleStatus(employmentType: EmploymentType) {
    this.masterService.toggleEmploymentTypeStatus(employmentType.id).subscribe({
      next: (response) => {
        if (response.success) {
          employmentType.is_active = !employmentType.is_active;
          console.log(
            `Updated status for ${employmentType.name}:`,
            employmentType.is_active
          );
          this.snackBar.open(
            'Employment type status updated successfully',
            'Close',
            {
              duration: 2000,
            }
          );
        } else {
          console.error(
            'Failed to update employment type status:',
            response.message
          );
          this.snackBar.open('Error updating employment type status', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error updating employment type status:', error);
        this.snackBar.open('Error updating employment type status', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  private fetchEmploymentTypesData() {
    this.masterService.getAllEmploymentTypes().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.employmentTypes.map(
            (employmentType: any, index: any) => ({
              ...employmentType,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch employment types:', response.message);
        }
      },
      error: (error) =>
        console.error('Error fetching employment types:', error),
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
