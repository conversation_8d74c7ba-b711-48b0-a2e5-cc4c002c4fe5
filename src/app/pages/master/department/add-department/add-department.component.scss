.add-department-container {
    padding: 10px;
    background-color: #f5f5f5;
    height: 100vh;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .content-wrapper {
        display: flex;

        .action-buttons{
            display: flex;
            flex-direction: row;
            gap: 10px;
            margin-top: 20px;
        }
        form{
            display: flex;
            flex-direction: column;
        }
      .list-section {
        .actions-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
  
          .search-field {
            flex: 1;
            max-width: 300px;
          }
  
          .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
  
            button {
              display: flex;
              align-items: center;
  
              mat-icon {
                margin-right: 5px;
              }
            }
          }
        }
        
  
        .table-container {
          background: white;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          overflow: hidden;
  
          table {
            width: 100%;
  
         
 
  
         
            th.mat-header-cell,
            td.mat-cell {
              padding: 12px 16px;
            }
  
            tr.mat-row {
              &:hover {
                background-color: #f5f5f5;
              }
  
              &.selected-row {
                background-color: #e0e0e0;
              }
            }
          }
  
          .mat-paginator {
            border-top: 1px solid #e0e0e0;
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .list-designations-container {
      .content-wrapper {
        .list-section {
          .actions-row {
            flex-direction: column;
            align-items: stretch;
  
            .search-field {
              max-width: none;
              margin-bottom: 10px;
            }
  
            .action-buttons {
              justify-content: flex-start;
            }
          }
  
          .table-container {
            overflow-x: auto;
  
            table {
              .mat-column-actions {
                width: auto;
              }
            }
          }
        }
      }
    }
  }