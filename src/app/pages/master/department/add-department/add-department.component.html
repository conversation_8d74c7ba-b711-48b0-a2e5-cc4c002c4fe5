<div class="add-department-container">
    <div class="component-header">
        <h1 class="component-title">Add New Department</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Department Name</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (departmentForm.get('name')?.invalid && (departmentForm.get('name')?.dirty || departmentForm.get('name')?.touched)) {
                            <mat-error>
                                @if (departmentForm.get('name')?.errors?.['required']) {
                                    Department name is required.
                                } @else if (departmentForm.get('name')?.errors?.['minlength']) {
                                    Department name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>

                    <mat-slide-toggle formControlName="is_active" class="full-width">
                        Is Active
                    </mat-slide-toggle>

                    @if (errorMessage()) {
                        <mat-error class="server-error">{{ errorMessage() }}</mat-error>
                    }

                    <div class="action-buttons">
                        <button mat-raised-button color="primary" type="submit" [disabled]="departmentForm.invalid">
                            <mat-icon>add</mat-icon> Add Department
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>