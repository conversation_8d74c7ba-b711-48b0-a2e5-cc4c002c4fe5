import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

interface Department {
  id: number;
  name: string;
  is_active: boolean;
  updated_at: string;
  created_at: string;
  created_by: number;
  created_by_username: string;
  updated_by: number;
  sl_no?: number;
}

@Component({
  selector: 'app-list-departments',
  templateUrl: './list-departments.component.html',
  styleUrls: ['./list-departments.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSlideToggleModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListDepartmentsComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'is_active',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<Department>([]);
  selection = new SelectionModel<Department>(true, []);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  departmentToEdit: Department | null = null;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchDepartmentsData();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addDepartment() {
    this.router.navigate(['/site/master/department/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  toggleAddForm() {
    this.departmentToEdit = null;
  }

  onDepartmentAdded(newDepartment: any) {
    this.fetchDepartmentsData();
  }

  editDepartment(department: Department) {
    this.router.navigate(['/site/master/department/edit', department.id]);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedDepartments = this.selection.selected;
    if (selectedDepartments.length > 0) {
      const departmentIds = selectedDepartments.map(
        (department) => department.id
      );

      this.masterService.deleteDepartments(departmentIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchDepartmentsData();
            this.snackBar.open('Departments deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete departments:', response.message);
            this.snackBar.open('Error deleting departments', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting departments:', error);
          this.snackBar.open('Error deleting departments', 'Close', {
            duration: 2000,
          });
        },
      });
    }
  }

  onDepartmentUpdated() {
    this.fetchDepartmentsData();
  }

  toggleActive(department: Department) {
    this.masterService.toggleDepartmentStatus(department.id).subscribe({
      next: (response) => {
        if (response.success) {
          department.is_active = !department.is_active;
          this.snackBar.open(
            'Department status updated successfully',
            'Close',
            { duration: 2000 }
          );
        } else {
          this.snackBar.open('Failed to update department status', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error updating department status:', error);
        this.snackBar.open('Error updating department status', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  private fetchDepartmentsData() {
    this.masterService.getAllDepartments().subscribe({
      next: (response) => {
        if (response.success) {
          console.log(response);
          this.dataSource.data = response.departments.map(
            (department: any, index: any) => ({
              ...department,
              sl_no: index + 1,
            })
          );
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          console.error('Failed to fetch departments:', response.message);
          this.snackBar.open('Failed to fetch departments', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error fetching departments:', error);
        this.snackBar.open('Error fetching departments', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
