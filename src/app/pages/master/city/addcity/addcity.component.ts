import {
  Component,
  Output,
  EventEmitter,
  OnInit,
  Input,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-addcity',
  templateUrl: './addcity.component.html',
  styleUrls: ['./addcity.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSnackBarModule,
    MatIconModule,
    MatCardModule,
  ],
})
export class AddCityComponent implements OnInit {
  @Output() cityAdded = new EventEmitter<any>();
  @Output() cancelled = new EventEmitter<void>();
  @Output() cityUpdated = new EventEmitter<any>();
  @Input() citytoEdit: any;

  cityForm!: FormGroup;
  states: any[] = [];
  isEditMode = false;

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.initForm();
    this.loadStates();

    if (this.citytoEdit) {
      this.isEditMode = true;
      this.cityForm.patchValue(this.citytoEdit);
    }
  }

  private initForm() {
    this.cityForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      state_id: ['', Validators.required],
    });
  }

  loadStates() {
    this.masterService.getAllStates().subscribe({
      next: (response) => {
        if (response.success) {
          this.states = response.states;
        } else {
          this.snackBar.open('Failed to load states', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error loading states:', error);
        this.snackBar.open('Error loading states', 'Close', { duration: 3000 });
      },
    });
  }

  onSubmit() {
    if (this.cityForm.valid) {
      if (this.isEditMode) {
        // Edit city
        this.masterService
          .updateCity(this.citytoEdit.id, this.cityForm.value)
          .subscribe({
            next: (response: any) => {
              if (response.success) {
                this.snackBar.open('City updated successfully', 'Close', {
                  duration: 3000,
                });
                this.cityAdded.emit(response.city);
                this.cityUpdated.emit(response.city);
                this.resetForm();
              } else {
                this.snackBar.open('Failed to update city', 'Close', {
                  duration: 3000,
                });
              }
            },
            error: (error: any) => {
              console.error('Error updating city:', error);
              this.snackBar.open('Error updating city', 'Close', {
                duration: 3000,
              });
            },
          });
      } else {
        // Add city
        this.masterService.addCity(this.cityForm.value).subscribe({
          next: (response: any) => {
            if (response.success) {
              this.snackBar.open('City added successfully', 'Close', {
                duration: 3000,
              });
              this.cityAdded.emit(response.city);
              this.resetForm();
            } else {
              this.snackBar.open('Failed to add city', 'Close', {
                duration: 3000,
              });
            }
          },
          error: (error: any) => {
            console.error('Error adding city:', error);
            this.snackBar.open('Error adding city', 'Close', {
              duration: 3000,
            });
          },
        });
      }
    }
  }

  onCancel() {
    this.resetForm();
    this.cancelled.emit();
  }

  private resetForm() {
    this.cityForm.reset();
    Object.keys(this.cityForm.controls).forEach((key) => {
      const control = this.cityForm.get(key);
      control?.setErrors(null);
      control?.markAsPristine();
      control?.markAsUntouched();
      control?.updateValueAndValidity();
    });
    this.cityForm.updateValueAndValidity();
    this.isEditMode = false;
  }
}
