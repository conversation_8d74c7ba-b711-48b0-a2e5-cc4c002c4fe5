<div class="add-city-container">
  <div class="component-header">
    <h1 class="component-title">Add New City</h1>
   
  </div>

  <mat-card class="form-card">
    <mat-card-content>
      <form [formGroup]="cityForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>City Name</mat-label>
          <input matInput formControlName="name"   placeholder="Enter city name">
          <mat-error *ngIf="cityForm.get('name')?.hasError('required')">City name is required</mat-error>
          <mat-error *ngIf="cityForm.get('name')?.hasError('minlength')">City name must be at least 2 characters long</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>State</mat-label>
          <mat-select formControlName="state_id" >
            <mat-option *ngFor="let state of states" [value]="state.id">
              {{state.name}}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="cityForm.get('state_id')?.hasError('required')">State is required</mat-error>
        </mat-form-field>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="!cityForm.valid" *ngIf="!isEditMode">
            <mat-icon>add</mat-icon> Add City
          </button>

          <button mat-raised-button color="primary" type="submit" [disabled]="!cityForm.valid" *ngIf="isEditMode">
            <mat-icon>add</mat-icon> Update City
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>