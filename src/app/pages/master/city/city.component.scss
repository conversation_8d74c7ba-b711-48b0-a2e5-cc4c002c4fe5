.city-component-container {
    padding: 20px;
    background-color: #f5f5f5;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
  
      .add-city-button {
        transition: transform 0.3s ease;
  
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  
    .actions-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .search-field {
        width: 300px;
      }
  
      .action-buttons {
        display: flex;
        gap: 10px;
      }
    }
  
    .table-container {
      background: white;
      border-radius: 4px;
      overflow: hidden;
  
      table {
        width: 100%;
  
        th.mat-header-cell, td.mat-cell {
          padding: 12px 16px;
        }
  
        th.mat-header-cell {
          font-weight: bold;
          color: #555;
        }
  
        tr.mat-row:hover {
          background-color: #f0f0f0;
        }
  
        tr.selected-row {
          background-color: #e8eaf6;
        }
  
        .mat-column-actions {
          width: 120px;
          text-align: center;
        }
  
        .mat-icon-button {
          width: 30px;
          height: 30px;
          line-height: 30px;
  
          .mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            line-height: 18px;
          }
        }
      }
    }
  
    .mat-paginator {
      border-top: 1px solid #e0e0e0;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .city-component-container {
      .component-header {
        flex-direction: column;
        align-items: flex-start;
  
        .add-city-button {
          align-self: flex-end;
          margin-top: -40px; // Adjust as needed to overlap with the title
        }
      }
  
      .actions-row {
        flex-direction: column;
        align-items: stretch;
  
        .search-field {
          width: 100%;
          margin-bottom: 15px;
        }
  
        .action-buttons {
          justify-content: flex-end;
        }
      }
  
      .table-container {
        overflow-x: auto;
      }
    }
  }