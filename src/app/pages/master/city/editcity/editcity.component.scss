.edit-city-container {
    padding: 10px;
    background-color: #f5f5f5;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .form-card {
      background: white;
      border-radius: 4px;
      box-shadow: none;
      
      mat-card-content {
        padding: 20px;
  
        form {
          display: flex;
          flex-direction: column;
          gap: 20px;
          max-width: 500px;
  
          mat-form-field {
            width: 100%;
          }
  
          .form-actions {
            display: flex;
            justify-content: flex-start;
            margin-top: 20px;
  
            button {
              min-width: 120px;
            }
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 600px) {
    .add-city-container {
      .component-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
  
      .form-card {
        mat-card-content {
          padding: 15px;
        }
      }
    }
  }