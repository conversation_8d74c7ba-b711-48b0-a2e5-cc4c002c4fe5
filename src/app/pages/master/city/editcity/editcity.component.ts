import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { CitySignalService } from '../../../../services/city-signal.service';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { City } from '../../../../interfaces/city.interface';
import { State } from '../../../../interfaces/state.interface';
import { forkJoin } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-editcity',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSnackBarModule,
  ],
  templateUrl: './editcity.component.html',
  styleUrls: ['./editcity.component.scss'],
})
export class EditcityComponent implements OnInit {
  editCityForm!: FormGroup;
  states: State[] = [];
  cityToEdit: City | null = null;

  constructor(
    private citySignalService: CitySignalService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private fb: FormBuilder,
    private router: Router
  ) {}

  ngOnInit() {
    this.initForm();
    this.loadDataAndPopulateForm();
    if (!this.cityToEdit) {
      this.router.navigate(['site/master/city']);
    }
  }

  private initForm() {
    this.editCityForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      state_id: [null, Validators.required],
    });
  }

  private loadDataAndPopulateForm() {
    this.cityToEdit = this.citySignalService.getCityToEdit()();
    console.log('City to edit from signal:', this.cityToEdit);

    if (!this.cityToEdit) {
      this.snackBar.open('No city data to edit', 'Close', { duration: 3000 });
      return;
    }

    forkJoin({
      states: this.masterService.getAllStates(),
      city: this.masterService.getCityById(this.cityToEdit.id),
    }).subscribe({
      next: ({ states, city }) => {
        if (states.success) {
          this.states = states.states;
          console.log('Loaded states:', this.states);
        } else {
          this.snackBar.open('Failed to load states', 'Close', {
            duration: 3000,
          });
        }

        if (city.success) {
          this.cityToEdit = city.city;
          console.log('Loaded city details:', this.cityToEdit);
          this.populateForm();
        } else {
          this.snackBar.open('Failed to load city details', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.snackBar.open('Error loading data', 'Close', { duration: 3000 });
      },
    });
  }

  private populateForm() {
    if (this.cityToEdit) {
      console.log('Populating form with city:', this.cityToEdit);
      this.editCityForm.patchValue({
        name: this.cityToEdit.name,
        state_id: this.cityToEdit.state_id,
      });
      console.log('Form values after population:', this.editCityForm.value);

      // Check if the state_id exists in the loaded states
      const stateExists = this.states.some(
        (state) => state.id === this.cityToEdit?.state_id
      );
      if (!stateExists) {
        console.error(
          `State with id ${this.cityToEdit.state_id} not found in loaded states`
        );
        this.snackBar.open(
          "Error: City's state not found in the list",
          'Close',
          { duration: 5000 }
        );
      }
    }
  }

  onSubmit() {
    if (this.editCityForm.valid && this.cityToEdit) {
      const updatedCityData = this.editCityForm.value;
      console.log('Submitting updated city data:', updatedCityData);
      this.updateCity(updatedCityData);
    }
  }

  onCancel() {
    console.log('Cancel edit');
    // Implement cancel logic (e.g., navigate back)
  }

  private updateCity(cityData: Partial<City>) {
    if (this.cityToEdit) {
      this.masterService.updateCity(this.cityToEdit.id, cityData).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('City updated successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['site/master/city']);
            // Implement navigation back to city list or other appropriate action
          } else {
            this.snackBar.open('Failed to update city', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error updating city:', error);
          this.snackBar.open('Error updating city', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }
}
