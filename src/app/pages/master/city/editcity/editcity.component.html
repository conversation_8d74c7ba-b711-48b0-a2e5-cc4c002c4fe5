<div class="edit-city-container">
    <div class="component-header">
      <h1 class="component-title">Edit City</h1>
      <button mat-raised-button color="primary" (click)="onCancel()">
        <mat-icon>arrow_back</mat-icon> 
      </button>
    </div>
  
    <mat-card class="form-card">
      <mat-card-content>
        <form [formGroup]="editCityForm" (ngSubmit)="onSubmit()">
          <mat-form-field appearance="outline">
            <mat-label>City Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter city name">
            <mat-error *ngIf="editCityForm.get('name')?.hasError('required')">City name is required</mat-error>
            <mat-error *ngIf="editCityForm.get('name')?.hasError('minlength')">City name must be at least 2 characters long</mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline">
            <mat-label>State</mat-label>
            <mat-select formControlName="state_id">
              <mat-option *ngFor="let state of states" [value]="state.id">
                {{state.name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="editCityForm.get('state_id')?.hasError('required')">State is required</mat-error>
          </mat-form-field>
  
       
  
          <div class="form-actions">
            <button mat-raised-button color="primary" type="submit" [disabled]="!editCityForm.valid">
              <mat-icon>save</mat-icon> Update City
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>