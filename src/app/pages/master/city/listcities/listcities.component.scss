.list-cities-container {
  padding: 20px;
  background-color: #f5f5f5;

  .component-header {
    margin-bottom: 20px;

    .component-title {
      font-size: 24px;
      color: #333;
      margin: 0;
    }
  }

  .content-wrapper {
    display: flex;
    gap: 20px;

    .list-section {
      flex: 1;
      min-width: 0; // Allows flex item to shrink below its minimum content size
    }

    .form-section {
      width: 400px;
    }
  }

  .actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .search-field {
      width: 300px;
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .table-container {
    background: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    table {
      width: 100%;

      th.mat-header-cell, td.mat-cell {
        padding: 12px 16px;
      }

      th.mat-header-cell {
        font-weight: bold;
        color: white;
        background-color: #3f51b5; // Primary color, adjust as needed
        font-size: 14px;
      }

      td.mat-cell {
        font-size: 14px;
      }

      tr.mat-row {
        &:hover {
          background-color: #f5f5f5;
        }

        &.selected-row {
          background-color: #e8eaf6;
        }
      }

      .mat-column-select {
        width: 50px;
        padding-right: 0;
      }

      .mat-column-actions {
        width: 120px;
        text-align: center;
      }

      .mat-icon-button {
        width: 30px;
        height: 30px;
        line-height: 30px;

        .mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          line-height: 18px;
        }
      }
    }
  }

  .mat-paginator {
    border-top: 1px solid #e0e0e0;
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .list-cities-container {
    .content-wrapper {
      flex-direction: column;

      .form-section {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .list-cities-container {
    padding: 10px;

    .actions-row {
      flex-direction: column;
      align-items: stretch;

      .search-field {
        width: 100%;
        margin-bottom: 15px;
      }

      .action-buttons {
        justify-content: space-between;
      }
    }

    .table-container {
      overflow-x: auto;

      table {
        th.mat-header-cell, td.mat-cell {
          padding: 8px;
        }

        .mat-column-actions {
          width: auto;
        }
      }
    }
  }
}