<div class="list-cities-container">
  <div class="component-header">
    <h1 class="component-title">City Management</h1>
  </div>
  
  <div class="content-wrapper">
    <div class="list-section">
      <div class="actions-row">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search Cities</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Tiruppur">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="addCity()" class="add-city-button">
            <mat-icon>add</mat-icon> Add City
          </button>
          <button mat-raised-button color="warn" (click)="deleteSelected()" *ngIf="selection.hasValue()">
            <mat-icon>delete</mat-icon> Delete Selected ({{selection.selected.length}})
          </button>
        </div>
      </div>

    

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div class="table-container mat-elevation-z8" *ngIf="!errorMessage && dataSource.data.length > 0">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()"
                            [aria-label]="checkboxLabel()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)"
                            [aria-label]="checkboxLabel(row)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- Sl. No. Column -->
          <ng-container matColumnDef="sl_no">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
            <td mat-cell *matCellDef="let element"> {{element.sl_no}} </td>
          </ng-container>

          <!-- City Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> City Name </th>
            <td mat-cell *matCellDef="let element"> {{element.name}} </td>
          </ng-container>

          <!-- State Name Column -->
          <ng-container matColumnDef="state_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> State Name </th>
            <td mat-cell *matCellDef="let element"> {{element.state_name}} </td>
          </ng-container>

          <!-- Created By Username Column -->
          <ng-container matColumnDef="created_by_username">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let element"> {{element.created_by_username}} </td>
          </ng-container>

          <!-- Updated At Column -->
          <ng-container matColumnDef="updated_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated On </th>
            <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" (click)="viewCity(element); $event.stopPropagation()" matTooltip="View City">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent" (click)="editCity(element); $event.stopPropagation()" matTooltip="Edit City">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              (click)="selection.toggle(row)"
              [class.selected-row]="selection.isSelected(row)">
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons aria-label="Select page of cities"></mat-paginator>
      </div>

      <div *ngIf="!isLoading && !errorMessage && dataSource.data.length === 0" class="no-data-message">
        No cities found. Please add a new city.
      </div>
    </div>

 
  </div>
</div>