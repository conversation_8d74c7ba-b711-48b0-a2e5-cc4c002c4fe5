import { Component, OnInit, ViewChild, signal } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { AddCityComponent } from '../addcity/addcity.component';
import { CitySignalService } from '../../../../services/city-signal.service';
import { City } from '../../../../interfaces/city.interface';

@Component({
  selector: 'app-list-cities',
  templateUrl: './listcities.component.html',
  styleUrls: ['./listcities.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    AddCityComponent,
  ],
})
export class ListCitiesComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'state_name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<City>([]);
  selection = new SelectionModel<City>(true, []);
  showAddForm = false;
  isLoading = false;
  errorMessage: string | null = null;

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  cityToEdit: City | null = null;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private citySignalService: CitySignalService
  ) {}

  ngOnInit() {
    this.fetchCitiesData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  checkboxLabel(row?: City): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${
      row.sl_no
    }`;
  }

  onCityAdded(newCity: City) {
    this.fetchCitiesData();
    this.showAddForm = false;
  }

  viewCity(city: City) {
    console.log('View city:', city);
    // Implement view logic here
  }

  editCity(city: City) {
    console.log('Edit city:', city);
    this.citySignalService.setCityToEdit(city);
    this.router.navigate(['site/master/city/editcity']);
  }

  deleteSelected() {
    const selectedCities = this.selection.selected;
    if (selectedCities.length > 0) {
      const cityIds = selectedCities.map((city) => city.id);
      this.masterService.deleteCities(cityIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchCitiesData();
          } else {
            console.error('Failed to delete cities:', response.message);
            this.errorMessage =
              'Failed to delete selected cities. Please try again.';
          }
        },
        error: (error) => {
          console.error('Error deleting cities:', error);
          this.errorMessage =
            'An error occurred while deleting cities. Please try again.';
        },
      });
    }
  }

  onCityUpdated() {
    this.fetchCitiesData();
    this.showAddForm = false;
  }

  addCity() {
    this.router.navigate(['site/master/city/addcity']);
  }

  private fetchCitiesData() {
    this.isLoading = true;
    this.errorMessage = null;
    this.masterService.getAllCities().subscribe({
      next: (response) => {
        if (response.success) {
          console.log('Cities data received:', response);
          this.dataSource.data = response.cities.map(
            (city: any, index: number) => ({
              id: city.id,
              name: city.name,
              stateId: city.stateId,
              state_name: city.state_name || 'Unknown State',
              created_by_username: city.created_by_username || 'Unknown User',
              created_at: city.created_at,
              updated_at: city.updated_at,
              created_by: city.created_by,
              updated_by: city.updated_by,
              updated_by_username: city.updated_by_username,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch cities:', response.message);
          this.errorMessage = 'Failed to fetch cities. Please try again.';
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error fetching cities:', error);
        this.errorMessage =
          'An error occurred while fetching cities. Please try again.';
      },
    });
  }
}
