<div class="list-designations-container">
    <div class="component-header">
      <h1 class="component-title">Designation Management</h1>
    </div>
    
    <div class="content-wrapper">
      <div class="list-section">
        <div class="actions-row">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Search Designations</mat-label>
            <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Manager">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
  
          <div class="action-buttons">
            <button mat-raised-button color="primary" (click)="goToAddDesignation()" class="add-designation-button">
              <mat-icon>add</mat-icon> Add Designation
            </button>
            <button mat-raised-button color="warn" (click)="deleteSelected()" *ngIf="selection.hasValue()">
              <mat-icon>delete</mat-icon> Delete Selected
            </button>
          </div>
        </div>
  
        <div class="table-container">
          <table mat-table [dataSource]="dataSource" matSort>
            <!-- Checkbox Column -->
            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef>
                <mat-checkbox (change)="$event ? masterToggle() : null"
                              [checked]="selection.hasValue() && isAllSelected()"
                              [indeterminate]="selection.hasValue() && !isAllSelected()">
                </mat-checkbox>
              </th>
              <td mat-cell *matCellDef="let row">
                <mat-checkbox (click)="$event.stopPropagation()"
                              (change)="$event ? selection.toggle(row) : null"
                              [checked]="selection.isSelected(row)">
                </mat-checkbox>
              </td>
            </ng-container>
  
            <!-- Sl. No. Column -->
            <ng-container matColumnDef="sl_no">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
              <td mat-cell *matCellDef="let element"> {{element.sl_no}} </td>
            </ng-container>
  
            <!-- Designation Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation Name </th>
              <td mat-cell *matCellDef="let element"> {{element.name}} </td>
            </ng-container>
  
            <!-- Created By Username Column -->
            <ng-container matColumnDef="created_by_username">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
              <td mat-cell *matCellDef="let element"> {{element.created_by_username}} </td>
            </ng-container>
  
            <!-- Updated At Column -->
            <ng-container matColumnDef="updated_at">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated On </th>
              <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
            </ng-container>
  
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef> Actions </th>
              <td mat-cell *matCellDef="let element">
                <button mat-icon-button color="accent" (click)="editDesignation(element); $event.stopPropagation()" matTooltip="Edit Designation">
                  <mat-icon>edit</mat-icon>
                </button>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                (click)="selection.toggle(row)"
                [class.selected-row]="selection.isSelected(row)">
            </tr>
          </table>
  
          <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
        </div>
      </div>
  
     
    </div>
  </div>