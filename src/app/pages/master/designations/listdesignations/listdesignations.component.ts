import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule, DatePipe } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import {
  DesignationSignalService,
  Designation,
} from '../../../../services/designation-signal.service';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';

@Component({
  selector: 'app-list-designations',
  templateUrl: './listdesignations.component.html',
  styleUrls: ['./listdesignations.component.scss'],
  standalone: true,
  imports: [
    MatPaginatorModule,
    MatIconModule,
    MatTableModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatFormFieldModule,
    DatePipe,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
  ],
})
export class ListDesignationsComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<Designation>([]);
  selection = new SelectionModel<Designation>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  constructor(
    private masterService: MasterserviceService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router,
    private designationSignalService: DesignationSignalService
  ) {}

  ngOnInit() {
    this.fetchDesignationsData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  goToAddDesignation() {
    this.router.navigate(['site/master/designations/add']);
  }

  editDesignation(designation: Designation) {
    this.designationSignalService.setDesignationToEdit(designation);
    this.router.navigate(['site/master/designations/edit']);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedDesignations = this.selection.selected;
    if (selectedDesignations.length > 0) {
      const designationIds = selectedDesignations.map(
        (designation) => designation.id
      );
      this.masterService.deleteDesignations(designationIds).subscribe({
        next: (response: any) => {
          if (response.success) {
            this.selection.clear();
            this.fetchDesignationsData();
            this.snackBar.open('Designations deleted successfully', 'Close', {
              duration: 3000,
            });
          } else {
            this.snackBar.open('Failed to delete designations', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error: any) => {
          console.error('Error deleting designations:', error);
          this.snackBar.open('Error deleting designations', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  private fetchDesignationsData() {
    this.masterService.getAllDesignations().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.designations.map(
            (designation: any, index: number) => ({
              ...designation,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch designations:', response.message);
        }
      },
      error: (error) => console.error('Error fetching designations:', error),
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
