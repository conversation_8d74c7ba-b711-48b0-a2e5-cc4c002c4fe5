import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-adddesignation',
  standalone: true,
  imports: [
    MatTableModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './adddesignations.component.html',
  styleUrl: './adddesignations.component.scss',
})
export class AdddesignationsComponent {
  designationForm: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService
  ) {
    this.designationForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  get name() {
    return this.designationForm.get('name');
  }

  onSubmit() {
    if (this.designationForm.valid) {
      this.masterService.addDesignation(this.designationForm.value).subscribe(
        (res) => {
          this.snackBar.open('Designation Added Successfully', 'Close', {
            duration: 2000,
          });
          this.designationForm.reset();
          this.errorMessage.set('');
        },
        (error) => {
          console.error('Error adding designation:', error);
          this.errorMessage.set(
            error.error?.message ||
              'An error occurred while adding the designation.'
          );
        }
      );
    } else {
      this.errorMessage.set('Please fill in all required fields correctly.');
      this.designationForm.markAllAsTouched();
    }
  }

  updateErrorMessage() {
    if (this.name?.invalid && (this.name.dirty || this.name.touched)) {
      if (this.name.errors?.['required']) {
        this.errorMessage.set('Designation name is required.');
      } else if (this.name.errors?.['minlength']) {
        this.errorMessage.set(
          'Designation name must be at least 2 characters long.'
        );
      }
    } else {
      this.errorMessage.set('');
    }
  }

  cancel() {
    this.designationForm.reset();
    this.errorMessage.set('');
  }
}
