<div class="designation-container">
    <div class="component-header">
        <h1 class="component-title">Add New Designation</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="designationForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Designation</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (name?.invalid && (name?.dirty || name?.touched)) {
                            <mat-error>{{errorMessage()}}</mat-error>
                        }
                    </mat-form-field>

                

                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="designationForm.invalid">
                            <mat-icon>add</mat-icon> Add Designation
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>