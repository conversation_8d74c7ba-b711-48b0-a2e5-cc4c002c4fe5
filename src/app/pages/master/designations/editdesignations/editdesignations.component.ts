import { Component, OnInit, signal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { DesignationSignalService } from '../../../../services/designation-signal.service';

// Add imports for form components
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';

interface Designation {
  id: number;
  name: string;
}

@Component({
  selector: 'app-editdesignation',
  standalone: true,
  imports: [
    MatCardModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './editdesignations.component.html',
  styleUrl: './editdesignations.component.scss',
})
export class EditdesignationsComponent implements OnInit {
  editDesignationForm!: FormGroup;
  errorMessage = signal('');
  designationToEdit = this.designationSignalService.getDesignationToEdit();

  constructor(
    private designationSignalService: DesignationSignalService,
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.initForm();
  }

  ngOnInit() {
    const designation = this.designationToEdit();
    if (designation) {
      this.editDesignationForm.patchValue({
        name: designation.name,
      });
    } else {
      this.router.navigate(['site/master/designations']);
    }
  }

  private initForm() {
    this.editDesignationForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    if (this.editDesignationForm.valid) {
      const designationName = this.editDesignationForm.value.name;
      const designation = this.designationToEdit();
      if (designation) {
        this.masterService
          .updateDesignation(designation.id, { name: designationName })
          .subscribe({
            next: (response) => {
              // Handle successful update
              console.log(response);
              this.snackBar.open('Designation updated successfully', 'Close', {
                duration: 2000,
              });
              this.router.navigate(['site/master/designations']);
            },
            error: (error) => {
              this.errorMessage.set(error);
              this.snackBar.open('Error updating designation', 'Close', {
                duration: 2000,
              });
            },
          });
      }
    }
  }

  cancel() {
    this.router.navigate(['site/master/designations']);
  }
}
