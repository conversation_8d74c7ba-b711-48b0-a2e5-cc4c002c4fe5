<div class="edit-designation-container">
    <div class="component-header">
        <h1 class="component-title">Edit Designation</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="editDesignationForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Designation Name</mat-label>
                        <input matInput formControlName="name" required>
                        @if (editDesignationForm.get('name')?.invalid && (editDesignationForm.get('name')?.dirty || editDesignationForm.get('name')?.touched)) {
                            <mat-error>
                                @if (editDesignationForm.get('name')?.errors?.['required']) {
                                    Designation name is required.
                                } @else if (editDesignationForm.get('name')?.errors?.['minlength']) {
                                    Designation name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>
        
                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="editDesignationForm.invalid">
                            <mat-icon>save</mat-icon> Update Designation
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>