import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule, DatePipe } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';
import { StoreSignalService } from '../../../../services/store-signal.service';
import {
  Store,
  StoreResponse,
  StoreActionResponse,
} from '../../../../interfaces/store.interface'; // Updated import
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-list-stores',
  templateUrl: './list-stores.component.html',
  styleUrls: ['./list-stores.component.scss'],
  standalone: true,
  imports: [
    MatPaginatorModule,
    MatIconModule,
    MatTableModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatFormFieldModule,
    DatePipe,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTooltipModule,
  ],
})
export class ListStoresComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'city_name',
    'phone_number',
    'email',
    'manager_name',
    'is_active',
    'created_by_username',
    'actions',
  ];
  dataSource = new MatTableDataSource<Store>([]);
  selection = new SelectionModel<Store>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  constructor(
    private masterService: MasterserviceService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router,
    private storeSignalService: StoreSignalService
  ) {}

  ngOnInit() {
    this.fetchStoresData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  goToAddStore() {
    this.router.navigate(['site/master/stores/add']);
  }

  editStore(store: Store) {
    this.storeSignalService.setStoreToEdit(store);
    event?.stopPropagation();
    this.router.navigate(['/site/master/stores/edit', store.id]);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedStores = this.selection.selected;
    if (selectedStores.length > 0) {
      const storeIds = selectedStores.map((store) => store.id);
      this.masterService.deleteStores(storeIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchStoresData();
            this.snackBar.open('Stores deleted successfully', 'Close', {
              duration: 3000,
            });
          } else {
            this.snackBar.open('Failed to delete stores', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting stores:', error);
          this.snackBar.open('Error deleting stores', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  toggleStatus(store: Store) {
    this.masterService.toggleStoreStatus(store.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.fetchStoresData();
          this.snackBar.open('Store status updated successfully', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error toggling store status:', error);
        this.snackBar.open('Error updating store status', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  private fetchStoresData() {
    this.masterService.getAllStores().subscribe({
      next: (response: StoreResponse) => {
        if (response.success) {
          this.dataSource.data = response.stores.map((store, index) => ({
            ...store,
            sl_no: index + 1,
          }));
        } else {
          console.error(
            'Failed to fetch stores:',
            response.message || 'Unknown error'
          );
          this.snackBar.open(
            response.message || 'Failed to fetch stores',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (error) => {
        console.error('Error fetching stores:', error);
        this.snackBar.open('Error fetching stores', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  getStatusClass(isActive: boolean): string {
    return isActive ? 'status-badge active' : 'status-badge inactive';
  }

  goToStaffMapping(store: any) {
    this.router.navigate(['/site/staffs/stores', store.id]);
  }
}
