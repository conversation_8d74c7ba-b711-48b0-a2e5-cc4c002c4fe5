.list-stores-container {
    padding: 10px;
    background-color: #f5f5f5;
    height: 100vh;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }
  
    .content-wrapper {
      .list-section {
        .actions-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
  
          .search-field {
            flex: 1;
            max-width: 300px;
          }
  
          .action-buttons {
            display: flex;
            gap: 10px;
  
            button {
              display: flex;
              align-items: center;
  
              mat-icon {
                margin-right: 5px;
              }
            }
          }
        }
  
        .table-container {
          background: white;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          overflow: hidden;
  
          table {
            width: 100%;
  
            .mat-column-select {
              padding-left: 10px;
              width: 48px;
            }
  
            .mat-column-sl_no {
              width: 70px;
            }
  
            .mat-column-actions {
              width: 130px;
              text-align: center;
            }
  
            th.mat-header-cell,
            td.mat-cell {
              padding: 12px 16px;
            }
  
            tr.mat-row {
              &:hover {
                background-color: #f5f5f5;
              }
  
              &.selected-row {
                background-color: #e0e0e0;
              }
            }
  
            // Status badge styles
            .status-badge {
              padding: 6px 12px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              display: inline-block;
              min-width: 80px;
              text-align: center;
  
              &.active {
                background-color: rgba(76, 175, 80, 0.1);
                color: #4CAF50;
              }
  
              &.inactive {
                background-color: rgba(244, 67, 54, 0.1);
                color: #F44336;
              }
            }
  
            .mat-column-is_active {
              width: 100px;
            }
  
            // Action buttons styling
            .mat-column-actions {
              button {
                margin: 0 4px;
  
                &[color="primary"] {
                  color: #1976d2;
                }
  
                &[color="warn"] {
                  color: #f44336;
                }
  
                &[color="accent"] {
                  color: #ff4081;
                }
  
                &:hover {
                  background-color: rgba(0, 0, 0, 0.04);
                }
              }
            }
          }
  
          .mat-paginator {
            border-top: 1px solid #e0e0e0;
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .list-stores-container {
      .content-wrapper {
        .list-section {
          .actions-row {
            flex-direction: column;
            align-items: stretch;
  
            .search-field {
              max-width: none;
              margin-bottom: 10px;
            }
  
            .action-buttons {
              justify-content: flex-start;
            }
          }
  
          .table-container {
            overflow-x: auto;
  
            table {
              .mat-column-actions {
                width: auto;
                min-width: 130px;
              }
            }
          }
        }
      }
    }
  }
  
  // Small screen adjustments
  @media (max-width: 600px) {
    .list-stores-container {
      padding: 10px;
  
      .component-header {
        .component-title {
          font-size: 20px;
        }
      }
  
      .content-wrapper {
        .list-section {
          .action-buttons {
            flex-direction: column;
            
            button {
              width: 100%;
            }
          }
        }
      }
    }
  }
  
  // Custom scrollbar styles
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  
    &:hover {
      background: #666;
    }
  }
  
  // Row selection highlight
  .selected-row {
    background-color: rgba(0, 0, 0, 0.04) !important;
  }
  
  // Loading state
  .loading-shade {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.15);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  // Empty state
  .no-data {
    text-align: center;
    padding: 20px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.54);
  }