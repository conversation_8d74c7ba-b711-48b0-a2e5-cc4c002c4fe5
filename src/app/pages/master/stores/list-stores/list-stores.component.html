<div class="list-stores-container">
  <div class="component-header">
    <h1 class="component-title">Store Management</h1>
  </div>

  <div class="content-wrapper">
    <div class="list-section">
      <div class="actions-row">
        <mat-form-field appearance="outline">
          <mat-label>Search Stores</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Main Branch">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="goToAddStore()">
            <mat-icon>add</mat-icon>
            Add Store
          </button>
          <button mat-raised-button color="warn" [disabled]="!selection.selected.length" (click)="openDeleteConfirmationDialog()">
            <mat-icon>delete</mat-icon>
            Delete Selected
          </button>
        </div>
      </div>

      <div class="table-container">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- Serial Number Column -->
          <ng-container matColumnDef="sl_no">
            <th mat-header-cell *matHeaderCellDef>Sl. No.</th>
            <td mat-cell *matCellDef="let element">{{element.sl_no}}</td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Store Name</th>
            <td mat-cell *matCellDef="let element">{{element.name}}</td>
          </ng-container>

          <!-- City Column -->
          <ng-container matColumnDef="city_name">
            <th mat-header-cell *matHeaderCellDef>City</th>
            <td mat-cell *matCellDef="let element">{{element.city_name}}</td>
          </ng-container>

          <!-- Phone Column -->
          <ng-container matColumnDef="phone_number">
            <th mat-header-cell *matHeaderCellDef>Phone</th>
            <td mat-cell *matCellDef="let element">{{element.phone_number}}</td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef>Email</th>
            <td mat-cell *matCellDef="let element">{{element.email}}</td>
          </ng-container>

          <!-- Manager Column -->
          <ng-container matColumnDef="manager_name">
            <th mat-header-cell *matHeaderCellDef>Manager</th>
            <td mat-cell *matCellDef="let element">{{element.manager_name}}</td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="is_active">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let element">
              <span [class]="getStatusClass(element.is_active)">
                {{element.is_active ? 'Active' : 'Inactive'}}
              </span>
            </td>
          </ng-container>

          <!-- Created By Column -->
          <ng-container matColumnDef="created_by_username">
            <th mat-header-cell *matHeaderCellDef>Created By</th>
            <td mat-cell *matCellDef="let element">{{element.created_by_username}}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" (click)="editStore(element)" matTooltip="Edit Store">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" 
                      (click)="openDeleteConfirmationDialog()" 
                      matTooltip="Delete Store">
                <mat-icon>delete</mat-icon>
              </button>
              <button mat-icon-button color="accent" 
                      (click)="toggleStatus(element)" 
                      matTooltip="{{element.is_active ? 'Deactivate' : 'Activate'}} Store">
                <mat-icon>power_settings_new</mat-icon>
              </button>

              <button mat-icon-button color="primary" 
            (click)="goToStaffMapping(element); $event.stopPropagation()" 
            matTooltip="Manage Store Staff">
      <mat-icon>people</mat-icon>
    </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              [class.selected-row]="selection.isSelected(row)">
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of stores"></mat-paginator>
      </div>
    </div>
  </div>
</div>