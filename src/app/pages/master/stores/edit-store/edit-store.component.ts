import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { MasterserviceService } from '../../../../services/masterservice.service';

@Component({
  selector: 'app-edit-store',
  templateUrl: './edit-store.component.html',
  styleUrls: ['./edit-store.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatIconModule,
  ],
})
export class EditStoreComponent implements OnInit {
  storeForm!: FormGroup;
  states: any[] = [];
  cities: any[] = [];
  managers: any[] = [];
  storeId: number;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.storeId = Number(this.route.snapshot.params['id']);
    this.initForm();
  }

  ngOnInit() {
    this.loadMasterData();
    if (this.storeId) {
      this.loadStoreData();
    }
  }

  private initForm() {
    this.storeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      address: ['', [Validators.required]],
      state_id: ['', [Validators.required]],
      city_id: [{ value: '', disabled: true }, [Validators.required]],
      postal_code: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
      phone_number: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      email: ['', [Validators.required, Validators.email]],
      opening_hours: ['', [Validators.required]],
      closing_hours: ['', [Validators.required]],
      manager_id: ['', [Validators.required]],
      is_active: [true]
    });

    this.storeForm.get('state_id')?.valueChanges.subscribe((stateId) => {
      if (stateId) {
        this.loadCities(stateId);
        this.storeForm.get('city_id')?.enable();
      } else {
        this.storeForm.get('city_id')?.disable();
        this.cities = [];
      }
    });
  }

  private loadMasterData() {
    this.isLoading = true;
    Promise.all([
      this.masterService.getAllStates().toPromise(),
      this.masterService.getAllManagers().toPromise()
    ]).then(([statesResponse, managersResponse]) => {
      if (statesResponse.success) {
        this.states = statesResponse.states;
      }
      if (managersResponse.success) {
        this.managers = managersResponse.managers;
      }
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading master data:', error);
      this.snackBar.open('Error loading form data', 'Close', { duration: 3000 });
      this.isLoading = false;
    });
  }

  private loadCities(stateId: number) {
    this.masterService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = response.cities;
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.snackBar.open('Error loading cities', 'Close', { duration: 3000 });
      }
    });
  }

  private loadStoreData() {
    this.isLoading = true;
    this.masterService.getStoreById(this.storeId).subscribe({
      next: (response) => {
        if (response.success) {
          const store = response.store;
          this.loadCities(store.state_id);
          this.storeForm.patchValue({
            name: store.name,
            address: store.address,
            state_id: store.state_id,
            city_id: store.city_id,
            postal_code: store.postal_code,
            phone_number: store.phone_number,
            email: store.email,
            opening_hours: store.opening_hours,
            closing_hours: store.closing_hours,
            manager_id: store.manager_id,
            is_active: store.is_active
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading store:', error);
        this.snackBar.open('Error loading store data', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  onSubmit() {
    if (this.storeForm.valid) {
      this.isLoading = true;
      const formData = this.storeForm.getRawValue();

      this.masterService.updateStore(this.storeId, formData).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Store updated successfully', 'Close', {
              duration: 2000
            });
            this.router.navigate(['/site/master/stores']);
          }
        },
        error: (error) => {
          console.error('Error updating store:', error);
          this.snackBar.open(error.error?.message || 'Error updating store', 'Close', {
            duration: 3000
          });
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    } else {
      Object.keys(this.storeForm.controls).forEach(key => {
        const control = this.storeForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
    }
  }

  cancel() {
    this.router.navigate(['/site/master/stores']);
  }
}