.edit-store-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      margin-bottom: 24px;
  
      .component-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        color: #1976d2;
        margin: 0;
  
        mat-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }
      }
    }
  
    .content-wrapper {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
      form {
        padding: 24px;
      }
    }
  
    .form-section {
      margin-bottom: 32px;
  
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        color: #333;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e0e0e0;
  
        mat-icon {
          color: #1976d2;
        }
      }
  
      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        align-items: start;
  
        .full-width {
          grid-column: 1 / -1;
        }
  
        mat-form-field {
          width: 100%;
  
          &.mat-form-field-appearance-outline {
            .mat-form-field-wrapper {
              margin: 0;
            }
          }
        }
  
        .toggle-field {
          display: flex;
          align-items: center;
          min-height: 56px;
          padding-top: 4px;
        }
      }
    }
  
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #e0e0e0;
  
      button {
        min-width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 8px 24px;
  
        mat-icon {
          font-size: 20px;
        }
  
        &[color="primary"] {
          background-color: #1976d2;
          color: white;
  
          &:hover {
            background-color: #1565c0;
          }
  
          &:disabled {
            background-color: rgba(0, 0, 0, 0.12);
            color: rgba(0, 0, 0, 0.38);
          }
        }
  
        &[color="warn"] {
          color: #f44336;
          border-color: rgba(244, 67, 54, 0.5);
  
          &:hover {
            background-color: rgba(244, 67, 54, 0.04);
          }
        }
      }
    }
  
    // Material form field customizations
    ::ng-deep {
      .mat-form-field-appearance-outline {
        .mat-form-field-outline {
          background-color: #fafafa;
        }
  
        &.mat-focused {
          .mat-form-field-outline {
            background-color: white;
          }
        }
      }
  
      .mat-form-field-subscript-wrapper {
        margin-top: 0;
      }
  
      .mat-form-field-infix {
        width: auto;
        min-width: 0;
      }
  
      mat-select {
        .mat-select-trigger {
          height: 24px;
        }
      }
  
      .mat-select-panel {
        max-height: 300px;
      }
    }
  
    // Responsive adjustments
    @media screen and (max-width: 1200px) {
      padding: 16px;
  
      .form-section {
        .form-grid {
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
      }
    }
  
    @media screen and (max-width: 768px) {
      padding: 12px;
  
      .component-header {
        .component-title {
          font-size: 20px;
  
          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }
      }
  
      .content-wrapper {
        form {
          padding: 16px;
        }
      }
  
      .form-section {
        .section-title {
          font-size: 16px;
        }
  
        .form-grid {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }
  
      .form-actions {
        flex-direction: column;
        gap: 12px;
  
        button {
          width: 100%;
        }
      }
    }
  
    // Loading state
    &.loading {
      position: relative;
      pointer-events: none;
      opacity: 0.7;
  
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.5);
        z-index: 1000;
      }
    }
  
    // Custom scrollbar
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
  
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
  
      &:hover {
        background: #666;
      }
    }
  }