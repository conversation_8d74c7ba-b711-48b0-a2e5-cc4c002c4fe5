<p>edit-store works!</p>
<div class="edit-store-container">
    <div class="component-header">
      <h1 class="component-title">
        <mat-icon>edit</mat-icon>
        Edit Store
      </h1>
    </div>
  
    <mat-card class="content-wrapper">
      <form [formGroup]="storeForm" (ngSubmit)="onSubmit()">
        <div class="form-section">
          <h2 class="section-title">Store Information</h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Store Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter store name">
              <mat-error *ngIf="storeForm.get('name')?.hasError('required')">
                Store name is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('name')?.hasError('minlength')">
                Store name must be at least 2 characters
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Address</mat-label>
              <textarea matInput formControlName="address" placeholder="Enter store address" rows="3"></textarea>
              <mat-error *ngIf="storeForm.get('address')?.hasError('required')">
                Address is required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>State</mat-label>
              <mat-select formControlName="state_id">
                <mat-option *ngFor="let state of states" [value]="state.id">
                  {{state.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('state_id')?.hasError('required')">
                State is required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>City</mat-label>
              <mat-select formControlName="city_id">
                <mat-option *ngFor="let city of cities" [value]="city.id">
                  {{city.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('city_id')?.hasError('required')">
                City is required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Postal Code</mat-label>
              <input matInput formControlName="postal_code" placeholder="Enter postal code">
              <mat-error *ngIf="storeForm.get('postal_code')?.hasError('required')">
                Postal code is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('postal_code')?.hasError('pattern')">
                Please enter a valid 6-digit postal code
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <div class="form-section">
          <h2 class="section-title">Contact Information</h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phone_number" placeholder="Enter phone number">
              <mat-error *ngIf="storeForm.get('phone_number')?.hasError('required')">
                Phone number is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('phone_number')?.hasError('pattern')">
                Please enter a valid 10-digit phone number
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" placeholder="Enter email">
              <mat-error *ngIf="storeForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <div class="form-section">
          <h2 class="section-title">Operating Hours</h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Opening Hours</mat-label>
              <input matInput type="time" formControlName="opening_hours">
              <mat-error *ngIf="storeForm.get('opening_hours')?.hasError('required')">
                Opening hours are required
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Closing Hours</mat-label>
              <input matInput type="time" formControlName="closing_hours">
              <mat-error *ngIf="storeForm.get('closing_hours')?.hasError('required')">
                Closing hours are required
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <div class="form-section">
          <h2 class="section-title">Management</h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Manager</mat-label>
              <mat-select formControlName="manager_id">
                <mat-option *ngFor="let manager of managers" [value]="manager.id">
                  {{manager.firstName}} {{manager.lastName}} ({{manager.designation_name}})
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('manager_id')?.hasError('required')">
                Manager is required
              </mat-error>
            </mat-form-field>
            
            <div class="toggle-field">
              <mat-slide-toggle formControlName="is_active" color="primary">
                Active Status
              </mat-slide-toggle>
            </div>
          </div>
        </div>
  
        <div class="form-actions">
          <button type="button" mat-button color="warn" (click)="cancel()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="storeForm.invalid || isLoading">
            <mat-icon>save</mat-icon>
            Save Changes
          </button>
        </div>
      </form>
    </mat-card>
  </div>