<div class="add-store-container">
  <div class="component-header">
    <h1 class="component-title">Add New Store</h1>
  </div>

  <div class="content-wrapper">
    <form [formGroup]="storeForm" (ngSubmit)="onSubmit()" class="store-form">
      <mat-card>
        <mat-card-content>
          <!-- Basic Information Section -->
          <div class="section-header">
            <h2>Basic Information</h2>
          </div>
          
          <div class="form-row">
            <mat-form-field class="form-field">
              <mat-label>Store Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter store name">
              <mat-error *ngIf="storeForm.get('name')?.hasError('required')">
                Store name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" placeholder="Enter email">
              <mat-error *ngIf="storeForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field class="form-field">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phone_number" placeholder="Enter phone number">
              <mat-hint>10-digit mobile number</mat-hint>
              <mat-error *ngIf="storeForm.get('phone_number')?.hasError('required')">
                Phone number is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('phone_number')?.hasError('pattern')">
                Please enter a valid 10-digit phone number
              </mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Store Manager</mat-label>
              <mat-select formControlName="manager_id">
                <mat-option *ngFor="let manager of managers" [value]="manager.id">
                  {{manager.firstName}} {{manager.lastName}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('manager_id')?.hasError('required')">
                Store manager is required
              </mat-error>
              <mat-hint *ngIf="managers.length === 0">
                No active managers available
              </mat-hint>
            </mat-form-field>
          </div>

          <!-- Location Section -->
          <div class="section-header">
            <h2>Location Details</h2>
          </div>

          <div class="form-row">
            <mat-form-field class="form-field">
              <mat-label>Address</mat-label>
              <textarea matInput formControlName="address" placeholder="Enter store address" rows="3"></textarea>
              <mat-error *ngIf="storeForm.get('address')?.hasError('required')">
                Address is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field class="form-field">
              <mat-label>State</mat-label>
              <mat-select formControlName="state_id">
                <mat-option *ngFor="let state of states" [value]="state.id">
                  {{state.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('state_id')?.hasError('required')">
                State is required
              </mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>City</mat-label>
              <mat-select formControlName="city_id" [disabled]="!storeForm.get('state_id')?.value">
                <mat-option *ngFor="let city of cities" [value]="city.id">
                  {{city.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="storeForm.get('city_id')?.hasError('required')">
                City is required
              </mat-error>
              <mat-hint *ngIf="!storeForm.get('state_id')?.value">
                Please select a state first
              </mat-hint>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Postal Code</mat-label>
              <input matInput formControlName="postal_code" placeholder="Enter postal code">
              <mat-hint>6-digit postal code</mat-hint>
              <mat-error *ngIf="storeForm.get('postal_code')?.hasError('required')">
                Postal code is required
              </mat-error>
              <mat-error *ngIf="storeForm.get('postal_code')?.hasError('pattern')">
                Please enter a valid 6-digit postal code
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Operating Hours Section -->
          <div class="section-header">
            <h2>Operating Hours</h2>
          </div>

          <div class="form-row">
            <mat-form-field class="form-field">
              <mat-label>Opening Time</mat-label>
              <input matInput formControlName="opening_hours" type="time">
              <mat-error *ngIf="storeForm.get('opening_hours')?.hasError('required')">
                Opening time is required
              </mat-error>
            </mat-form-field>

            <mat-form-field class="form-field">
              <mat-label>Closing Time</mat-label>
              <input matInput formControlName="closing_hours" type="time">
              <mat-error *ngIf="storeForm.get('closing_hours')?.hasError('required')">
                Closing time is required
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Status Section -->
          <div class="section-header">
            <h2>Store Status</h2>
          </div>

          <div class="form-row status-row">
            <mat-slide-toggle formControlName="is_active" color="primary">
              Store Active
            </mat-slide-toggle>
            <div class="status-hint">
              Toggle to set the store's operational status
            </div>
          </div>

        </mat-card-content>

        <mat-card-actions class="form-actions">
          <button mat-button type="button" (click)="onCancel()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="storeForm.invalid">
            <mat-icon>save</mat-icon>
            Save Store
          </button>
        </mat-card-actions>
      </mat-card>
    </form>
  </div>
</div>