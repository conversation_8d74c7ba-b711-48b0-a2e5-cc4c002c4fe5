.add-store-container {
  padding: 24px;
  
  .component-header {
    margin-bottom: 24px;
    
    .component-title {
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
  }

  .store-form {
    .section-header {
      margin: 24px 0 16px;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        font-size: 18px;
        font-weight: 500;
        color: #444;
        margin: 0 0 8px;
      }
    }

    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      .form-field {
        flex: 1;
        min-width: 200px;
      }
    }

    .status-row {
      align-items: center;
      
      .status-hint {
        color: #666;
        font-size: 14px;
        margin-left: 16px;
      }
    }

    textarea {
      min-height: 100px;
    }
  }
}

mat-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  
  mat-card-content {
    padding: 24px;
  }

  mat-card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #eee;
    
    button {
      display: flex;
      align-items: center;
      gap: 8px;
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.mat-mdc-form-field {
  width: 100%;
}

// Responsive adjustments
@media (max-width: 768px) {
  .add-store-container {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 0 !important;

    .form-field {
      width: 100%;
    }
  }

  .status-row {
    flex-direction: column;
    align-items: flex-start !important;

    .status-hint {
      margin-left: 0 !important;
      margin-top: 8px;
    }
  }
}