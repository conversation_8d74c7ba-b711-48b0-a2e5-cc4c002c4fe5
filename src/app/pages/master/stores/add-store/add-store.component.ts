import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { StaffService } from '../../../../services/staff.service'; // Add this import
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { fork<PERSON>oin } from 'rxjs';

interface Staff {
  id: number;
  firstName: string;
  lastName: string;
  designationId: number;
  isActive: boolean;
}

@Component({
  selector: 'app-add-store',
  templateUrl: './add-store.component.html',
  styleUrls: ['./add-store.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatSlideToggleModule,
  ],
})
export class AddStoreComponent implements OnInit {
  storeForm: FormGroup;
  states: any[] = [];
  cities: any[] = [];
  managers: Staff[] = [];
  managerDesignationId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private staffService: StaffService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.storeForm = this.fb.group({
      name: ['', [Validators.required]],
      address: ['', [Validators.required]],
      city_id: ['', [Validators.required]],
      state_id: ['', [Validators.required]],
      postal_code: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{6}$')],
      ],
      phone_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      email: ['', [Validators.required, Validators.email]],
      opening_hours: ['', [Validators.required]],
      closing_hours: ['', [Validators.required]],
      manager_id: ['', [Validators.required]],
      is_active: [true],
    });

    // Load cities when state changes
    this.storeForm.get('state_id')?.valueChanges.subscribe((stateId) => {
      if (stateId) {
        this.loadCities(stateId);
      }
    });
  }

  ngOnInit() {
    // First get the manager designation ID
    this.masterService.getAllDesignations().subscribe({
      next: (response) => {
        const managerDesignation = response.designations.find(
          (d: any) => d.name.toLowerCase() === 'manager'
        );
        if (managerDesignation) {
          this.managerDesignationId = managerDesignation.id;
          this.loadInitialData();
        } else {
          this.snackBar.open('Manager designation not found', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading designations', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadInitialData() {
    forkJoin({
      states: this.masterService.getAllStates(),
      managers: this.masterService.getAllManagers(),
    }).subscribe({
      next: (results) => {
        this.states = results.states.states;
        this.managers = results.managers.managers;
      },
      error: (error) => {
        this.snackBar.open('Error loading initial data', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadCities(stateId: number) {
    this.masterService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        this.cities = response.cities;
      },
      error: (error) => {
        this.snackBar.open('Error loading cities', 'Close', { duration: 3000 });
      },
    });
  }

  // In add-store.component.ts
  onSubmit() {
    if (this.storeForm.valid) {
      // Log the form value
      console.log('Form Value:', this.storeForm.value);

      const storeData = {
        ...this.storeForm.value,
        // Ensure boolean is converted correctly
        is_active: this.storeForm.value.is_active ? 1 : 0,
      };

      // Log the data being sent
      console.log('Data being sent:', storeData);

      this.masterService.addStore(storeData).subscribe({
        next: (response) => {
          this.snackBar.open('Store added successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['/site/master/stores']);
        },
        error: (error) => {
          console.error('Error adding store:', error);
          this.snackBar.open(
            error.error?.message || 'Error adding store',
            'Close',
            { duration: 3000 }
          );
        },
      });
    }
  }

  onCancel() {
    this.router.navigate(['/site/master/stores']);
  }
}
