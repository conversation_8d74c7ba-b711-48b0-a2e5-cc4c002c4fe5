import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { ReligionSignalService } from '../../../../services/religion-signal.service';

@Component({
  selector: 'app-edit-religion',
  templateUrl: './edit-religion.component.html',
  styleUrls: ['./edit-religion.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
  ],
})
export class EditReligionComponent implements OnInit {
  religionForm: FormGroup;
  errorMessage = signal('');
  religionToEdit = this.religionSignalService.getReligionToEdit();

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router,
    private religionSignalService: ReligionSignalService
  ) {
    this.religionForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  ngOnInit() {
    const religion = this.religionToEdit();
    if (religion) {
      this.religionForm.patchValue({
        name: religion.name,
      });
    } else {
      this.router.navigate(['/site/master/religion']);
    }
  }

  updateErrorMessage() {
    const nameControl = this.religionForm.get('name');
    if (nameControl?.invalid && (nameControl.dirty || nameControl.touched)) {
      if (nameControl.errors?.['required']) {
        this.errorMessage.set('Religion name is required');
      } else if (nameControl.errors?.['minlength']) {
        this.errorMessage.set('Religion name must be at least 2 characters');
      }
    } else {
      this.errorMessage.set('');
    }
  }

  onSubmit() {
    if (this.religionForm.valid) {
      const religion = this.religionToEdit();
      if (religion) {
        this.masterService
          .updateReligion(religion.id, this.religionForm.value)
          .subscribe({
            next: (response) => {
              if (response.success) {
                this.snackBar.open('Religion updated successfully', 'Close', {
                  duration: 3000,
                });
                this.router.navigate(['/site/master/religion']);
              }
            },
            error: (error) => {
              this.snackBar.open('Error updating religion', 'Close', {
                duration: 3000,
              });
            },
          });
      }
    }
  }

  cancel() {
    this.router.navigate(['/site/master/religion']);
  }
}
