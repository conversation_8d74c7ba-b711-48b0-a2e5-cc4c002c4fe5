<div class="edit-religion-container">
    <div class="component-header">
        <h1 class="component-title">Edit Religion</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="religionForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Religion Name</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (religionForm.get('name')?.invalid && (religionForm.get('name')?.dirty || religionForm.get('name')?.touched)) {
                            <mat-error>{{errorMessage()}}</mat-error>
                        }
                    </mat-form-field>

                    <div class="action-buttons">
                        <button mat-raised-button color="primary" type="submit" [disabled]="religionForm.invalid">
                            <mat-icon>save</mat-icon> Update Religion
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>