<div class="list-religions-container">
  <div class="component-header">
    <h2>Religion Management</h2>
  </div>

  <div class="content-wrapper">
    <div class="actions-row">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Type to search">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <div class="action-buttons">
        <button mat-raised-button color="primary" (click)="addReligion()">
          <mat-icon>add</mat-icon> Add Religion
        </button>
        <button mat-raised-button color="warn" *ngIf="selection.hasValue()" (click)="deleteSelected()">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
      </div>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="dataSource" matSort>
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                         [checked]="selection.hasValue() && isAllSelected()"
                         [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Sl. No. Column -->
        <ng-container matColumnDef="sl_no">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
          <td mat-cell *matCellDef="let element"> {{element.sl_no}} </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
          <td mat-cell *matCellDef="let element"> {{element.name}} </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by_username">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
          <td mat-cell *matCellDef="let element"> {{element.created_by_username}} </td>
        </ng-container>

        <!-- Updated At Column -->
        <ng-container matColumnDef="updated_at">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated At </th>
          <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button color="primary" (click)="editReligion(element)" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" 
                     showFirstLastButtons
                     aria-label="Select page of religions">
      </mat-paginator>
    </div>
  </div>
</div>