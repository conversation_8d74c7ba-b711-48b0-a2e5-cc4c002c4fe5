import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { Router, RouterModule } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Religion } from '../../../../interfaces/religion.interface';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { ReligionSignalService } from '../../../../services/religion-signal.service';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-list-religions',
  templateUrl: './list-religions.component.html',
  styleUrls: ['./list-religions.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatCheckboxModule,
    RouterModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
})
export class ListReligionsComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<Religion>([]);
  selection = new SelectionModel<Religion>(true, []);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private religionSignalService: ReligionSignalService
  ) {}

  ngOnInit() {
    this.loadReligions();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  loadReligions() {
    this.masterService.getAllReligions().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.religions.map(
            (religion: Religion, index: number) => ({
              ...religion,
              sl_no: index + 1,
            })
          );
        }
      },
      error: (error) => {
        console.error('Error loading religions:', error);
        this.snackBar.open('Error loading religions', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  addReligion() {
    this.router.navigate(['/site/master/religion/add']);
  }

  editReligion(religion: Religion) {
    this.religionSignalService.setReligionToEdit(religion);
    this.router.navigate(['/site/master/religion/edit']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  deleteSelected() {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((religion) => religion.id);
        this.masterService.deleteReligions(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Religions deleted successfully', 'Close', {
                duration: 3000,
              });
              this.selection.clear();
              this.loadReligions();
            }
          },
          error: (error) => {
            console.error('Error deleting religions:', error);
            this.snackBar.open('Error deleting religions', 'Close', {
              duration: 3000,
            });
          },
        });
      }
    });
  }
}
