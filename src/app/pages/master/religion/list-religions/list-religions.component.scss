.list-religions-container {
    padding: 10px;
    background-color: #f5f5f5;
    height: 100vh;
  
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: end;
    }
  
    .content-wrapper {
      .list-section {
        .actions-row {
          display: flex;
          justify-content: flex-end; // Changed from space-between to flex-end
          align-items: center;
          margin-bottom: 20px;
          gap: 20px; // Added gap between search field and buttons
  
          .search-field {
            max-width: 300px;
            margin-right: auto; // This pushes the search field to the left
          }
  
          .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: end;
  
            button {
              display: flex;
              align-items: center;
  
              mat-icon {
                margin-right: 5px;
              }
            }
          }
        }
  
        .table-container {
          background: white;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          overflow: hidden;
  
          table {
            width: 100%;
  
            th.mat-header-cell,
            td.mat-cell {
              padding: 12px 16px;
            }
  
            tr.mat-row {
              &:hover {
                background-color: #f5f5f5;
              }
  
              &.selected-row {
                background-color: #e0e0e0;
              }
            }
          }
  
          .mat-paginator {
            border-top: 1px solid #e0e0e0;
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .list-religions-container {  // Fixed to use correct container class
      .content-wrapper {
        .list-section {
          .actions-row {
            flex-direction: column;
            align-items: stretch;
  
            .search-field {
              max-width: none;
              margin-bottom: 10px;
              margin-right: 0; // Remove margin-right on mobile
            }
  
            .action-buttons {
              justify-content: flex-end; // Keep buttons right-aligned on mobile
            }
          }
  
          .table-container {
            overflow-x: auto;
  
            table {
              .mat-column-actions {
                width: auto;
              }
            }
          }
        }
      }
    }
  }