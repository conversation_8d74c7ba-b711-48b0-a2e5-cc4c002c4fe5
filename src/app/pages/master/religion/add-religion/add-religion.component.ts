import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MasterserviceService } from '../../../../services/masterservice.service';

@Component({
  selector: 'app-add-religion',
  templateUrl: './add-religion.component.html',
  styleUrls: ['./add-religion.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
  ],
})
export class AddReligionComponent {
  religionForm: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.religionForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  updateErrorMessage() {
    const nameControl = this.religionForm.get('name');
    if (nameControl?.invalid && (nameControl.dirty || nameControl.touched)) {
      if (nameControl.errors?.['required']) {
        this.errorMessage.set('Religion name is required');
      } else if (nameControl.errors?.['minlength']) {
        this.errorMessage.set('Religion name must be at least 2 characters');
      }
    } else {
      this.errorMessage.set('');
    }
  }

  onSubmit() {
    if (this.religionForm.valid) {
      this.masterService.addReligion(this.religionForm.value).subscribe({
        next: (res) => {
          if (res.success) {
            this.snackBar.open('Religion Added Successfully', 'Close', {
              duration: 2000,
            });
            this.router.navigate(['/site/master/religion']);
          } else {
            this.snackBar.open(
              res.message || 'Failed to add religion',
              'Close',
              {
                duration: 3000,
              }
            );
          }
        },
        error: (error) => {
          console.error('Error adding religion:', error);
          this.snackBar.open(
            error.error?.message || 'Error adding religion',
            'Close',
            { duration: 3000 }
          );
        },
      });
    }
  }

  cancel() {
    this.router.navigate(['/site/master/religion']);
  }
}
