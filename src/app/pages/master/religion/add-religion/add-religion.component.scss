.add-religion-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  
    .component-header {
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        margin: 0;
      }
    }
  
    .content-wrapper {
      .form-card {
        max-width: 500px;
        margin: 0 auto;
        
        mat-card-content {
          padding: 20px;
  
          form {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 500px;
  
            .full-width {
              width: 100%;
            }
  
            .action-buttons {
              display: flex;
              gap: 10px;
              justify-content: flex-end;
              margin-top: 20px;
  
              button {
                min-width: 120px;
              }
            }
          }
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 600px) {
    .add-religion-container {
      .component-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
  
      .form-card {
        mat-card-content {
          padding: 15px;
        }
      }
    }
  }