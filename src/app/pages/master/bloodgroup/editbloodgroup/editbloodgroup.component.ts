import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BloodGroupSignalService } from '../../../../services/bloodgroup-signal.service';

interface BloodGroup {
  id: number;
  name: string;
}

@Component({
  selector: 'app-editbloodgroup',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatTableModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
  ],
  templateUrl: './editbloodgroup.component.html',
  styleUrl: './editbloodgroup.component.scss',
})
export class EditbloodgroupComponent {
  editBloodGroupForm!: FormGroup;
  bloodGroupToEdit = this.editBloogGroupSignal.getBloodGroupToEdit();
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private editBloogGroupSignal: BloodGroupSignalService
  ) {
    this.initForm();
  }

  ngOnInit() {
    const bloodGroup = this.bloodGroupToEdit();
    if (bloodGroup) {
      this.editBloodGroupForm.patchValue({
        name: bloodGroup.name,
      });
    } else {
      this.router.navigate(['site/master/bloodgroup']);
    }
  }

  private initForm() {
    this.editBloodGroupForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  cancel() {
    this.router.navigate(['site/master/bloodgroup']);
  }

  onSubmit() {
    if (this.editBloodGroupForm.valid) {
      const bloodGroupName = this.editBloodGroupForm.value.name;
      const bloodGroup = this.bloodGroupToEdit();
      if (bloodGroup) {
        this.masterService
          .updateBloodGroup(bloodGroup.id, { name: bloodGroupName })
          .subscribe({
            next: (response) => {
              // Handle successful update
              console.log(response);
              this.snackBar.open('Designation updated successfully', 'Close', {
                duration: 2000,
              });
              this.router.navigate(['site/master/bloodgroup']);
            },
            error: (error) => {
              this.errorMessage.set(error);
              this.snackBar.open('Error updating designation', 'Close', {
                duration: 2000,
              });
            },
          });
      }
    }
  }
}
