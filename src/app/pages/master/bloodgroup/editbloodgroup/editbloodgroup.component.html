<div class="edit-bloodgroup-container">
    <div class="component-header">
        <h1 class="component-title">Edit Blood Group</h1>
    </div>

<div class="content-wrapper">

    <mat-card class="form-card">
        <mat-card-content>
            <form [formGroup]="editBloodGroupForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Blood Group</mat-label>
                   
                    <input matInput formControlName="name" required>
                                   
                </mat-form-field>

                <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="editBloodGroupForm.invalid">
                        <mat-icon>save</mat-icon> Update Blood Group
                    </button>
                    <button mat-raised-button color="warn" type="button" (click)="cancel()">
                        <mat-icon>cancel</mat-icon> Cancel
                    </button>
                </div>
            </form>
        </mat-card-content>
        
    </mat-card>
</div>
</div>
