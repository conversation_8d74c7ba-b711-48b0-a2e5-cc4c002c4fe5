import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-addbloodgroup',
  standalone: true,
  imports: [
    MatTableModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './addbloodgroup.component.html',
  styleUrl: './addbloodgroup.component.scss',
})
export class AddbloodgroupComponent {
  readonly name = new FormControl('', [
    Validators.required,
    Validators.minLength(2),
  ]);

  bloodgroupForm!: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService
  ) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.bloodgroupForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    if (this.bloodgroupForm.valid) {
      this.masterService.addBloodGroup(this.bloodgroupForm.value).subscribe(
        (res) => {
          this.snackBar.open('Blood Group Added Successfully', 'Close', {
            duration: 2000,
          });
          this.bloodgroupForm.reset();
        },
        (error) => {
          this.errorMessage = signal(error.error.message);
        }
      );
    }
  }

  updateErrorMessage() {
    this.errorMessage = signal('');
  }

  cancel() {
    this.bloodgroupForm.reset();
  }
}
