<div class="add-bloodgroup-container">
    <div class="component-header">
        <h1 class="component-title">Add New Blood Group</h1>
    </div>

<div class="content-wrapper">

    <mat-card class="form-card">
        <mat-card-content>
            <form [formGroup]="bloodgroupForm" (ngSubmit)="onSubmit()">
                <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Blood Group</mat-label>
                    
                    <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                    
                    @if (name.invalid) {
                    <mat-error>{{errorMessage()}}</mat-error>
                    }
                    
                </mat-form-field>

                <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="bloodgroupForm.invalid">
                        <mat-icon>add</mat-icon> Add Blood Group
                    </button>
                    <button mat-raised-button color="warn" type="button" (click)="cancel()">
                        <mat-icon>cancel</mat-icon> Cancel
                    </button>
                </div>
            </form>
        </mat-card-content>
        
    </mat-card>
</div>
</div>