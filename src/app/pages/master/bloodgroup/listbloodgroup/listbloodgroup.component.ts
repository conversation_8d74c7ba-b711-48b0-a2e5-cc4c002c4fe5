import { Component, OnInit, ViewChild, signal } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { BloodGroupSignalService } from '../../../../services/bloodgroup-signal.service';

interface BloodGroup {
  id: number;
  name: string;
  updated_at: string;
  created_at: string;
  created_by: number;
  created_by_username: string;
  updated_by: number;
  sl_no?: number;
}

@Component({
  selector: 'app-list-blood-groups',
  templateUrl: './listbloodgroup.component.html',
  styleUrls: ['./listbloodgroup.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListBloodGroupsComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<BloodGroup>([]);
  selection = new SelectionModel<BloodGroup>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  bloodGroupToEdit: BloodGroup | null = null;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private bloodGroupSignalService: BloodGroupSignalService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchBloodGroupsData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addBloodGroup() {
    this.router.navigate(['/site/master/bloodgroup/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  toggleAddForm() {
    this.bloodGroupToEdit = null;
  }

  onBloodGroupAdded(newBloodGroup: any) {
    this.fetchBloodGroupsData();
  }

  editBloodGroup(bloodGroup: BloodGroup) {
    console.log(bloodGroup);
    this.bloodGroupSignalService.setBloodGroupToEdit(bloodGroup);
    this.router.navigate(['/site/master/bloodgroup/edit']);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedBloodGroups = this.selection.selected;
    console.log('Delete selected blood groups:', selectedBloodGroups);
    if (selectedBloodGroups.length > 0) {
      const bloodGroupIds = selectedBloodGroups.map(
        (bloodGroup) => bloodGroup.id
      );

      console.log(bloodGroupIds);
      this.masterService.deleteBloodGroups(bloodGroupIds).subscribe({
        next: (response) => {
          console.log('Response:', response);
          if (response.success) {
            this.selection.clear();
            this.fetchBloodGroupsData();
            this.snackBar.open('Blood groups deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete blood groups:', response.message);
            this.snackBar.open('Error deleting blood groups', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting blood groups:', error);
        },
      });
    }
  }

  onBloodGroupUpdated() {
    this.fetchBloodGroupsData();
  }

  private fetchBloodGroupsData() {
    this.masterService.getAllBloodGroups().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.bloodGroups.map(
            (bloodGroup: any, index: any) => ({
              ...bloodGroup,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch blood groups:', response.message);
        }
      },
      error: (error) => console.error('Error fetching blood groups:', error),
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
