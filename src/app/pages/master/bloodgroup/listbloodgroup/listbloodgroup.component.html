<div class="blood-group-container">
    <div class="component-header">
        <h1 class="component-title">Blood Group Management</h1>
    </div>

<div class="content-wrapper">
    <div class="list-section">
        <div class="actions-row">
            <mat-form-field appearance="outline" class="search-field">
                <mat-label>Search Blood Groups</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Ex. A+">
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <div class="action-buttons">
                <button mat-raised-button color="primary" (click)="addBloodGroup()" class="add-blood-group-button">
                    <mat-icon>add</mat-icon> Add Blood Group
                </button>
                <button mat-raised-button color="warn" (click)="openDeleteConfirmationDialog()" *ngIf="selection.hasValue()">
                    <mat-icon>delete</mat-icon> Delete Selected
                </button>
            </div>
        </div>


<div class="table-container">
    <table mat-table [dataSource]="dataSource" matSort>
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
                <mat-checkbox (change)="$event ? masterToggle() : null"
                              [checked]="selection.hasValue() && isAllSelected()"
                              [indeterminate]="selection.hasValue() && !isAllSelected()">
                </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
                <mat-checkbox (click)="$event.stopPropagation()"
                              (change)="$event ? selection.toggle(row) : null"
                              [checked]="selection.isSelected(row)">
                </mat-checkbox>
            </td>
        </ng-container>

        <!-- Sl. No. Column -->
        <ng-container matColumnDef="sl_no">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
            <td mat-cell *matCellDef="let element; let i = index"> {{ getSerialNumber(i) }} </td>
        </ng-container>

        <!-- Blood Group Name Column -->
        <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Blood Group Name </th>
            <td mat-cell *matCellDef="let element"> {{element.name}} </td>
        </ng-container>

        <!-- Created By Username Column -->
        <ng-container matColumnDef="created_by_username">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let element"> {{element.created_by_username}} </td>
        </ng-container>

        <ng-container matColumnDef="updated_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated At </th>
            <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
          </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let element">
                <button mat-icon-button (click)="editBloodGroup(element)" title="Edit Blood Group">
                    <mat-icon>edit</mat-icon>
                </button>
                
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
</div>




    </div>
</div>
</div>
