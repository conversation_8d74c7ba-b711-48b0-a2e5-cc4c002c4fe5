import { Component, OnInit, signal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  Gender,
  GenderSignalService,
} from '../../../../services/gender-signal-service';

@Component({
  selector: 'app-editgender',
  standalone: true,
  imports: [
    MatCardModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatSlideToggleModule,
  ],
  templateUrl: './edit-gender.component.html',
  styleUrl: './edit-gender.component.scss',
})
export class EditGenderComponent implements OnInit {
  editGenderForm!: FormGroup;
  errorMessage = signal('');
  genderToEdit: Gender | null = null;

  constructor(
    private genderSignalService: GenderSignalService,
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.genderToEdit = this.genderSignalService.getGenderToEdit()();
    if (this.genderToEdit) {
      this.populateForm();
    } else {
      // If the gender is not in the signal service, fetch it from the API
      const id = this.route.snapshot.params['id'];
      if (id) {
        this.masterService.getGenderById(id).subscribe({
          next: (response) => {
            if (response.success) {
              this.genderToEdit = response.gender;
              this.populateForm();
            } else {
              this.handleError('Failed to fetch gender details');
            }
          },
          error: (error) => {
            this.handleError('Error fetching gender details');
          },
        });
      } else {
        this.handleError('No gender ID provided');
      }
    }
  }

  private initForm() {
    this.editGenderForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      is_active: [true],
    });
  }

  private populateForm() {
    if (this.genderToEdit) {
      this.editGenderForm.patchValue({
        name: this.genderToEdit.name,
        is_active: this.genderToEdit.is_active,
      });
    }
  }

  onSubmit() {
    if (this.editGenderForm.valid && this.genderToEdit) {
      const genderData = this.editGenderForm.value;
      this.masterService
        .updateGender(this.genderToEdit.id, genderData)
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Gender updated successfully', 'Close', {
                duration: 2000,
              });
              this.router.navigate(['site/master/genders']);
            } else {
              this.handleError(response.message || 'Failed to update gender');
            }
          },
          error: (error) => {
            this.handleError('Error updating gender');
          },
        });
    }
  }

  private handleError(message: string) {
    this.errorMessage.set(message);
    this.snackBar.open(message, 'Close', {
      duration: 2000,
    });
  }

  cancel() {
    this.router.navigate(['site/master/genders']);
  }
}
