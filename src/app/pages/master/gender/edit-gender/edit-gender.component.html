<div class="edit-gender-container">
    <div class="component-header">
        <h1 class="component-title">Edit Gender</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="editGenderForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Gender Name</mat-label>
                        <input matInput formControlName="name" required>
                        @if (editGenderForm.get('name')?.invalid && (editGenderForm.get('name')?.dirty || editGenderForm.get('name')?.touched)) {
                            <mat-error>
                                @if (editGenderForm.get('name')?.errors?.['required']) {
                                    Gender name is required.
                                } @else if (editGenderForm.get('name')?.errors?.['minlength']) {
                                    Gender name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>

                    <mat-slide-toggle formControlName="is_active" class="full-width">
                        Is Active
                    </mat-slide-toggle>
        
                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="editGenderForm.invalid">
                            <mat-icon>save</mat-icon> Update Gender
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>