import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

interface Gender {
  id: number;
  name: string;
  is_active: boolean;
  updated_at: string;
  created_at: string;
  created_by: number;
  created_by_username: string;
  updated_by: number;
  sl_no?: number;
}

@Component({
  selector: 'app-list-genders',
  templateUrl: './list-gender.component.html',
  styleUrls: ['./list-gender.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSlideToggleModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListGendersComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'is_active',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<Gender>([]);
  selection = new SelectionModel<Gender>(true, []);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchGendersData();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addGender() {
    this.router.navigate(['/site/master/gender/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  editGender(gender: Gender) {
    this.router.navigate(['/site/master/gender/edit', gender.id]);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedGenders = this.selection.selected;
    if (selectedGenders.length > 0) {
      const genderIds = selectedGenders.map((gender) => gender.id);

      this.masterService.deleteGenders(genderIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchGendersData();
            this.snackBar.open('Genders deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete genders:', response.message);
            this.snackBar.open('Error deleting genders', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting genders:', error);
          this.snackBar.open('Error deleting genders', 'Close', {
            duration: 2000,
          });
        },
      });
    }
  }

  toggleActive(gender: Gender) {
    this.masterService.toggleGenderStatus(gender.id).subscribe({
      next: (response) => {
        if (response.success) {
          gender.is_active = !gender.is_active;
          this.snackBar.open('Gender status updated successfully', 'Close', {
            duration: 2000,
          });
        } else {
          this.snackBar.open('Failed to update gender status', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error updating gender status:', error);
        this.snackBar.open('Error updating gender status', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  private fetchGendersData() {
    this.masterService.getAllGenders().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.genders.map(
            (gender: any, index: number) => ({
              ...gender,
              sl_no: index + 1,
            })
          );
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          console.error('Failed to fetch genders:', response.message);
          this.snackBar.open('Failed to fetch genders', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error fetching genders:', error);
        this.snackBar.open('Error fetching genders', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  getSerialNumber(index: number): number {
    if (this.paginator) {
      return this.paginator.pageIndex * this.paginator.pageSize + index + 1;
    }
    return index + 1;
  }
}
