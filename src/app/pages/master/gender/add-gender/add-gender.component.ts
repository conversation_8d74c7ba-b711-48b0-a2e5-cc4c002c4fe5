import { Component, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Component({
  selector: 'app-add-gender',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatSlideToggleModule,
  ],
  templateUrl: './add-gender.component.html',
  styleUrl: './add-gender.component.scss',
})
export class AddGenderComponent {
  genderForm!: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService,
    private router: Router
  ) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.genderForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      is_active: [true], // Default to true
    });
  }

  onSubmit() {
    if (this.genderForm.valid) {
      const genderData = {
        name: this.genderForm.get('name')?.value,
        is_active: this.genderForm.get('is_active')?.value,
      };

      this.masterService.addGender(genderData).subscribe({
        next: (res) => {
          if (res.success) {
            this.snackBar.open('Gender Added Successfully', 'Close', {
              duration: 2000,
            });
            this.genderForm.reset({ is_active: true });
            this.router.navigate(['/site/master/gender']);
          } else {
            this.errorMessage.set(res.message || 'Failed to add gender');
            this.snackBar.open('Error adding gender', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          this.errorMessage.set(error.error.message || 'An error occurred');
          this.snackBar.open('Error adding gender', 'Close', {
            duration: 2000,
          });
        },
      });
    }
  }

  updateErrorMessage() {
    this.errorMessage.set('');
  }

  cancel() {
    this.genderForm.reset({ is_active: true });
    this.router.navigate(['/site/master/gender']);
  }
}
