<div class="add-gender-container">
    <div class="component-header">
        <h1 class="component-title">Add New Gender</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="genderForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Gender Name</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (genderForm.get('name')?.invalid && (genderForm.get('name')?.dirty || genderForm.get('name')?.touched)) {
                            <mat-error>
                                @if (genderForm.get('name')?.errors?.['required']) {
                                    Gender name is required.
                                } @else if (genderForm.get('name')?.errors?.['minlength']) {
                                    Gender name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>

                    <mat-slide-toggle formControlName="is_active" class="full-width">
                        Is Active
                    </mat-slide-toggle>

                    @if (errorMessage()) {
                        <mat-error class="server-error">{{ errorMessage() }}</mat-error>
                    }

                    <div class="action-buttons">
                        <button mat-raised-button color="primary" type="submit" [disabled]="genderForm.invalid">
                            <mat-icon>add</mat-icon> Add Gender
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>