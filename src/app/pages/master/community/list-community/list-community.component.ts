// list-community.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { CommunitySignalService } from '../../../../services/community-signal.service';

interface Community {
  id: number;
  name: string;
  updated_at: string;
  created_at: string;
  created_by: number;
  created_by_username: string;
  updated_by: number;
  sl_no?: number;
}

@Component({
  selector: 'app-list-communities',
  templateUrl: './list-community.component.html',
  styleUrls: ['./list-community.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListCommunitiesComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<Community>([]);
  selection = new SelectionModel<Community>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private communitySignalService: CommunitySignalService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchCommunitiesData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addCommunity() {
    this.router.navigate(['/site/master/community/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  editCommunity(community: Community) {
    this.communitySignalService.setCommunityToEdit(community);
    this.router.navigate(['/site/master/community/edit']);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedCommunities = this.selection.selected;
    if (selectedCommunities.length > 0) {
      const communityIds = selectedCommunities.map((community) => community.id);

      this.masterService.deleteCommunities(communityIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchCommunitiesData();
            this.snackBar.open('Communities deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete communities:', response.message);
            this.snackBar.open('Error deleting communities', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting communities:', error);
          this.snackBar.open('Error deleting communities', 'Close', {
            duration: 2000,
          });
        },
      });
    }
  }

  private fetchCommunitiesData() {
    this.masterService.getAllCommunities().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.communities.map(
            (community: any, index: any) => ({
              ...community,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch communities:', response.message);
          this.snackBar.open('Failed to fetch communities', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error fetching communities:', error);
        this.snackBar.open('Error fetching communities', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
