<!-- list-community.component.html -->
<div class="community-container">
    <div class="component-header">
        <h1 class="component-title">Community Management</h1>
    </div>

    <div class="content-wrapper">
        <div class="list-section">
            <div class="actions-row">
                <mat-form-field appearance="outline" class="search-field">
                    <mat-label>Search Communities</mat-label>
                    <input matInput (keyup)="applyFilter($event)" placeholder="Ex. Hindu">
                    <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>

                <div class="action-buttons">
                    <button mat-raised-button color="primary" (click)="addCommunity()" class="add-community-button">
                        <mat-icon>add</mat-icon> Add Community
                    </button>
                    <button mat-raised-button color="warn" (click)="openDeleteConfirmationDialog()" *ngIf="selection.hasValue()">
                        <mat-icon>delete</mat-icon> Delete Selected
                    </button>
                </div>
            </div>

            <div class="table-container">
                <table mat-table [dataSource]="dataSource" matSort>
                    <!-- Checkbox Column -->
                    <ng-container matColumnDef="select">
                        <th mat-header-cell *matHeaderCellDef>
                            <mat-checkbox (change)="$event ? masterToggle() : null"
                                          [checked]="selection.hasValue() && isAllSelected()"
                                          [indeterminate]="selection.hasValue() && !isAllSelected()">
                            </mat-checkbox>
                        </th>
                        <td mat-cell *matCellDef="let row">
                            <mat-checkbox (click)="$event.stopPropagation()"
                                          (change)="$event ? selection.toggle(row) : null"
                                          [checked]="selection.isSelected(row)">
                            </mat-checkbox>
                        </td>
                    </ng-container>

                    <!-- Sl. No. Column -->
                    <ng-container matColumnDef="sl_no">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
                        <td mat-cell *matCellDef="let element; let i = index"> {{ getSerialNumber(i) }} </td>
                    </ng-container>

                    <!-- Community Name Column -->
                    <ng-container matColumnDef="name">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Community Name </th>
                        <td mat-cell *matCellDef="let element"> {{element.name}} </td>
                    </ng-container>

                    <!-- Created By Username Column -->
                    <ng-container matColumnDef="created_by_username">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
                        <td mat-cell *matCellDef="let element"> {{element.created_by_username}} </td>
                    </ng-container>

                    <ng-container matColumnDef="updated_at">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated At </th>
                        <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                        <th mat-header-cell *matHeaderCellDef> Actions </th>
                        <td mat-cell *matCellDef="let element">
                            <button mat-icon-button (click)="editCommunity(element)" title="Edit Community">
                                <mat-icon>edit</mat-icon>
                            </button>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
            </div>

            <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" showFirstLastButtons></mat-paginator>
        </div>
    </div>
</div>