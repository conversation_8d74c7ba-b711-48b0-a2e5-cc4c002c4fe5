// edit-community.component.ts
import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommunitySignalService } from '../../../../services/community-signal.service';

interface Community {
  id: number;
  name: string;
}

@Component({
  selector: 'app-edit-community',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatTableModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatButtonModule,
  ],
  templateUrl: './editcommunity.component.html',
  styleUrl: './editcommunity.component.scss',
})
export class EditcommunityComponent {
  editCommunityForm!: FormGroup;
  communityToEdit = this.editCommunitySignal.getCommunityToEdit();
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private editCommunitySignal: CommunitySignalService
  ) {
    this.initForm();
  }

  ngOnInit() {
    const community = this.communityToEdit();
    if (community) {
      this.editCommunityForm.patchValue({
        name: community.name,
      });
    } else {
      this.router.navigate(['site/master/community']);
    }
  }

  private initForm() {
    this.editCommunityForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  cancel() {
    this.router.navigate(['site/master/community']);
  }

  onSubmit() {
    if (this.editCommunityForm.valid) {
      const communityName = this.editCommunityForm.value.name;
      const community = this.communityToEdit();
      if (community) {
        this.masterService
          .updateCommunity(community.id, { name: communityName })
          .subscribe({
            next: (response) => {
              console.log(response);
              this.snackBar.open('Community updated successfully', 'Close', {
                duration: 2000,
              });
              this.router.navigate(['site/master/community']);
            },
            error: (error) => {
              this.errorMessage.set(error.error.message);
              this.snackBar.open('Error updating community', 'Close', {
                duration: 2000,
              });
            },
          });
      }
    }
  }
}
