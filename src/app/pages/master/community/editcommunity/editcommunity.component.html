<!-- edit-community.component.html -->
<div class="edit-community-container">
    <div class="component-header">
        <h1 class="component-title">Edit Community</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="editCommunityForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Community Name</mat-label>
                        <input matInput formControlName="name" required>
                        @if (editCommunityForm.get('name')?.invalid && (editCommunityForm.get('name')?.dirty || editCommunityForm.get('name')?.touched)) {
                            <mat-error>
                                @if (editCommunityForm.get('name')?.errors?.['required']) {
                                    Community name is required.
                                } @else if (editCommunityForm.get('name')?.errors?.['minlength']) {
                                    Community name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>

                    @if (errorMessage()) {
                        <mat-error class="server-error">{{ errorMessage() }}</mat-error>
                    }

                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="editCommunityForm.invalid">
                            <mat-icon>save</mat-icon> Update Community
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>