<!-- add-community.component.html -->
<div class="add-community-container">
    <div class="component-header">
        <h1 class="component-title">Add New Community</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="communityForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Community Name</mat-label>
                        <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                        @if (communityForm.get('name')?.invalid && (communityForm.get('name')?.dirty || communityForm.get('name')?.touched)) {
                            <mat-error>
                                @if (communityForm.get('name')?.errors?.['required']) {
                                    Community name is required.
                                } @else if (communityForm.get('name')?.errors?.['minlength']) {
                                    Community name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>

                    @if (errorMessage()) {
                        <mat-error class="server-error">{{ errorMessage() }}</mat-error>
                    }

                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="communityForm.invalid">
                            <mat-icon>add</mat-icon> Add Community
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>