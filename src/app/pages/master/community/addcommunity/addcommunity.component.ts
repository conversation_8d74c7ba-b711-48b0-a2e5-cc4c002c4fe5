// add-community.component.ts
import { Component, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

@Component({
  selector: 'app-addcommunity',
  standalone: true,
  imports: [
    MatTableModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './addcommunity.component.html',
  styleUrl: './addcommunity.component.scss',
})
export class AddCommunityComponent {
  readonly name = new FormControl('', [
    Validators.required,
    Validators.minLength(2),
  ]);

  communityForm!: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService,
    private router: Router
  ) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.communityForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    if (this.communityForm.valid) {
      this.masterService.addCommunity(this.communityForm.value).subscribe(
        (res) => {
          this.snackBar.open('Community Added Successfully', 'Close', {
            duration: 2000,
          });
          this.communityForm.reset();
          this.router.navigate(['/site/master/community']);
        },
        (error) => {
          this.errorMessage.set(error.error.message);
        }
      );
    }
  }

  updateErrorMessage() {
    this.errorMessage.set('');
  }

  cancel() {
    this.communityForm.reset();
    this.router.navigate(['/site/master/community']);
  }
}
