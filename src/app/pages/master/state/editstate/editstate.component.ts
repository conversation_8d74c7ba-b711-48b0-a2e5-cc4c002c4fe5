import { Component, OnInit, signal } from '@angular/core';
import { StateSignalService } from '../../../../services/state-signal.service';
import { MatCardModule } from '@angular/material/card';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router } from '@angular/router';

interface State {
  id: number;
  name: string;
  // Add other properties as needed
}

@Component({
  selector: 'app-editstate',
  standalone: true,
  imports: [
    MatCardModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './editstate.component.html',
  styleUrl: './editstate.component.scss',
})
export class EditstateComponent implements OnInit {
  editStateForm!: FormGroup;
  errorMessage = signal('');
  stateToEdit = this.stateSignalService.getStateToEdit();

  constructor(
    private stateSignalService: StateSignalService,
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private router: Router
  ) {
    this.initForm();
  }

  ngOnInit() {
    const state = this.stateToEdit();
    if (state) {
      this.editStateForm.patchValue({
        name: state.name,
      });
    } else {
      this.router.navigate(['site/master/state']);
    }
  }

  private initForm() {
    this.editStateForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    if (this.editStateForm.valid) {
      const stateName = this.editStateForm.value.name;
      const state = this.stateToEdit();
      if (state) {
        this.masterService
          .updateState(state.id, { name: stateName })
          .subscribe({
            next: (response) => {
              // Handle successful update
              console.log(response);
              this.router.navigate(['site/master/state']);
            },
            error: (error) => {
              this.errorMessage.set(error);
            },
          });
      }
    }
  }

  cancel() {
    // Implement cancel logic
  }
}
