<div class="edit-state-container">
    <div class="component-header">
        <h1 class="component-title">Edit State</h1>
    </div>

    <div class="content-wrapper">
        <mat-card class="form-card">
            <mat-card-content>
                <form [formGroup]="editStateForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>State Name</mat-label>
                        <input matInput formControlName="name" required>
                        @if (editStateForm.get('name')?.invalid && (editStateForm.get('name')?.dirty || editStateForm.get('name')?.touched)) {
                            <mat-error>
                                @if (editStateForm.get('name')?.errors?.['required']) {
                                    State name is required.
                                } @else if (editStateForm.get('name')?.errors?.['minlength']) {
                                    State name must be at least 2 characters long.
                                }
                            </mat-error>
                        }
                    </mat-form-field>
        
                    <div class="form-actions">
                        <button mat-raised-button color="primary" type="submit" [disabled]="editStateForm.invalid">
                            <mat-icon>save</mat-icon> Update State
                        </button>
                        <button mat-raised-button color="warn" type="button" (click)="cancel()">
                            <mat-icon>cancel</mat-icon> Cancel
                        </button>
                    </div>
                </form>
            </mat-card-content>
        </mat-card>
    </div>
</div>