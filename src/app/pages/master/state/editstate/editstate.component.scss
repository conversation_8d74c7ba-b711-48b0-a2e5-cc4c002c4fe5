.edit-state-container{
    padding: 10px;
    background-color: #f5f5f5;
    height: 100vh;
}

.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .component-title {
        font-size: 24px;
        color: #333;
        margin: 0;
    }
}

.content-wrapper {
    width: 30%;

    .form-card {
        display: flex;
        box-shadow: none;
        border-radius: 0px;
        background-color: white;

        mat-form-field {
            width: 100%;
        }

        .form-actions {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            flex-direction: row;
            gap: 10px;
        }
    }

    .form-container {
        background: white;
        border-radius: 4px;
        box-shadow: none;

        form {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 500px;
            padding: 20px;

            mat-form-field {
                width: 100%;
            }

.form-action-buttons{
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;

    button{
        min-width: 120px;
    }
}


        }
    }
}