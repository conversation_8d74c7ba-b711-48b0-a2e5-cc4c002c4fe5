import { Component, OnInit, ViewChild, signal } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { StateSignalService } from '../../../../services/state-signal.service';
import { DeleteConfirmationDialogComponent } from '../../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

interface State {
  id: number;
  name: string;
  updated_at: string;
  created_by: number;
  created_by_username: string;
  sl_no?: number;
}

@Component({
  selector: 'app-list-states',
  templateUrl: './liststate.component.html',
  styleUrls: ['./liststate.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListstateComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'name',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource = new MatTableDataSource<State>([]);
  selection = new SelectionModel<State>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  stateToEdit: State | null = null;

  constructor(
    private masterService: MasterserviceService,
    private router: Router,
    private snackBar: MatSnackBar,
    private stateSignalService: StateSignalService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.fetchStatesData();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addState() {
    this.router.navigate(['/site/master/state/addstate']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  toggleAddForm() {
    this.stateToEdit = null;
  }

  onStateAdded(newState: any) {
    this.fetchStatesData();
    // Optionally, you can keep the form open or close it:
    // this.showAddForm = false;
  }

  viewState(state: State) {
    console.log('View state:', state);
    // Implement view logic here, e.g., navigate to a detail page
    // this.router.navigate(['/state', state.id]);
  }

  editState(state: State) {
    this.stateSignalService.setStateToEdit(state);
    this.router.navigate(['/site/master/state/editstate']);
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedStates = this.selection.selected;
    console.log('Delete selected states:', selectedStates);
    if (selectedStates.length > 0) {
      const stateIds = selectedStates.map((state) => state.id);
      this.masterService.deleteStates(stateIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchStatesData();
            this.snackBar.open('States deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete states:', response.message);
            this.snackBar.open('Error deleting states', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting states:', error);
        },
      });
    }
  }

  onStateUpdated() {
    this.fetchStatesData();
  }

  private fetchStatesData() {
    this.masterService.getAllStates().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.states.map(
            (state: any, index: any) => ({
              ...state,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch states:', response.message);
        }
      },
      error: (error) => console.error('Error fetching states:', error),
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }
}
