<div class="add-state-container">
    <div class="component-header">
        <h1 class="component-title">Add New State</h1>
    </div>

<div class="content-wrapper">

<mat-card class="form-card">
<mat-card-content>
        <form [formGroup]="stateForm" (ngSubmit)="onSubmit()">
            <mat-form-field appearance="outline" class="full-width">
                <mat-label>State Name</mat-label>
                
                <input matInput formControlName="name" (blur)="updateErrorMessage()" required>
                
                @if (name.invalid) {
                <mat-error>{{errorMessage()}}</mat-error>
                }
                
            </mat-form-field>

            <div class="form-actions">
                <button mat-raised-button color="primary" type="submit" [disabled]="stateForm.invalid">
                    <mat-icon>add</mat-icon> Add State
                </button>
                <button mat-raised-button color="warn" type="button" (click)="cancel()">
                    <mat-icon>cancel</mat-icon> Cancel
                </button>
            </div>
        </form>
    </mat-card-content>
    

</mat-card>
</div>

</div>


