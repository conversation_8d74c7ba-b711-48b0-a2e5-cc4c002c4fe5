import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MasterserviceService } from '../../../../services/masterservice.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-addstate',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatTableModule,
    MatInputModule,
    MatIconModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './addstate.component.html',
  styleUrl: './addstate.component.scss',
})
export class AddstateComponent {
  readonly name = new FormControl('', [
    Validators.required,
    Validators.minLength(2),
  ]);
  stateForm!: FormGroup;
  errorMessage = signal('');

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private masterService: MasterserviceService
  ) {}

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    this.stateForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
    });
  }

  onSubmit() {
    this.masterService.addState(this.stateForm.value).subscribe({
      next: (response) => {
        this.snackBar.open('State added successfully', 'Close', {
          duration: 2000,
        });

        this.stateForm.reset();
      },
      error: (error) => {
        this.snackBar.open('Error adding state', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  updateErrorMessage() {
    if (this.stateForm.hasError('required')) {
      this.errorMessage.set('You must enter a value');
    } else if (this.stateForm.hasError('minlength')) {
      this.errorMessage.set('Value must be at least 2 characters long');
    } else {
      this.errorMessage.set('');
    }
  }

  cancel() {
    this.stateForm.reset();
  }
}
