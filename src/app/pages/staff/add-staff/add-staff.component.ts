import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { MasterserviceService } from '../../../services/masterservice.service';
import { StaffService, Staff } from '../../../services/staff.service';
import { CommonModule, formatDate } from '@angular/common';
import { ReactiveFormsModule, FormArray } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { forkJoin } from 'rxjs';

interface MasterData {
  id: number;
  name: string;
  description?: string;
}

interface Sibling {
  name: string;
  date_of_birth: Date | string | null;
  gender: string;
  occupation?: string;
  marital_status?: string;
  contact?: string;
  status?: string;
  is_emergency_contact: boolean;
  additional_info?: string;
}

@Component({
  selector: 'app-add-staff',
  templateUrl: './add-staff.component.html',
  styleUrls: ['./add-staff.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
  ],
})
export class AddStaffComponent implements OnInit {
  staffForm!: FormGroup;
  genders: MasterData[] = [];
  departments: MasterData[] = [];
  designations: MasterData[] = [];
  employmentTypes: MasterData[] = [];
  bloodGroups: MasterData[] = [];
  selectedFile: File | null = null;
  isLoading = false;
  states: any[] = [];
  cities: any[] = [];
  religions: any[] = [];
  communities: any[] = [];

  educationLevels = [
    'High School',
    'Diploma',
    "Bachelor's Degree",
    "Master's Degree",
    'Ph.D.',
    'Other',
  ];

  employmentStatuses: Staff['employmentStatus'][] = [
    'Active',
    'On Leave',
    'Terminated',
  ];

  constructor(
    private fb: FormBuilder,
    private masterService: MasterserviceService,
    private staffService: StaffService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.staffForm = this.fb.group({
      // Personal Information
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      dateOfBirth: [null, [Validators.required]],
      genderId: [null, [Validators.required]],
      email: [
        '',
        [
          Validators.required,
          Validators.email,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
          ),
        ],
      ],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      emergencyContactName: [
        '',
        [Validators.required, Validators.minLength(2)],
      ],
      emergencyContactNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],

      // Address Information
      address: ['', [Validators.required, Validators.minLength(5)]],
      state: [null, [Validators.required]],
      city: [{ value: null, disabled: true }, [Validators.required]],
      postalCode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],

      // Employment Information
      employeeId: [
        '',
        { validators: [Validators.required], updateOn: 'change' },
      ], // Auto-generated and readonly
      hireDate: [new Date(), [Validators.required]],
      departmentId: [null, [Validators.required]],
      designationId: [null, [Validators.required]],
      employmentTypeId: [null, [Validators.required]],
      educationLevel: ['', [Validators.required]],
      degrees: [''],
      salary: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]*$'),
          Validators.maxLength(10),
        ],
      ],

      // Additional Information
      bloodGroupId: [null, [Validators.required]],
      idProof: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          Validators.maxLength(12),
          Validators.pattern('^[A-Za-z0-9]+$'),
        ],
      ],
      religion: [''],
      community: [''],
      isActive: [true],
      employmentStatus: ['Active', [Validators.required]],

      // Family Information
      father_name: ['', [Validators.minLength(2)]],
      father_occupation: [''],
      father_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      father_status: ['Living'],
      mother_name: ['', [Validators.minLength(2)]],
      mother_occupation: [''],
      mother_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      mother_status: ['Living'],
      family_address: ['', [Validators.minLength(5)]],

      // Siblings Array
      siblings: this.fb.array([]),
    });

    // Handle state-city dependency
    this.staffForm.get('state')?.valueChanges.subscribe((stateId) => {
      const cityControl = this.staffForm.get('city');
      if (stateId) {
        cityControl?.enable();
        this.loadCitiesForState(stateId); // Make sure this method is defined
      } else {
        cityControl?.disable();
        cityControl?.setValue(null);
      }
    });

    // Watch for value changes on required numeric fields to ensure they're numbers
    [
      'genderId',
      'departmentId',
      'designationId',
      'employmentTypeId',
      'bloodGroupId',
    ].forEach((field) => {
      this.staffForm.get(field)?.valueChanges.subscribe((value) => {
        if (value !== null && value !== '') {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            this.staffForm.get(field)?.setErrors({ notANumber: true });
          }
        }
      });
    });

    // Watch salary field to ensure it's a valid number
    this.staffForm.get('salary')?.valueChanges.subscribe((value) => {
      if (value !== null && value !== '') {
        const numValue = Number(value);
        if (isNaN(numValue) || numValue < 0) {
          this.staffForm.get('salary')?.setErrors({ invalidSalary: true });
        }
      }
    });
  }

  // Helper method to create sibling form group

  ngOnInit(): void {
    this.loadMasterData();
    this.setupStateChangeListener();
    this.setNextEmployeeId();
  }

  private setupStateChangeListener(): void {
    this.staffForm.get('state')?.valueChanges.subscribe((selectedStateId) => {
      console.log('Selected State ID:', selectedStateId);

      if (selectedStateId) {
        this.loadCitiesForState(selectedStateId);
        this.staffForm.get('city')?.enable();
      } else {
        this.cities = [];
        this.staffForm.get('city')?.disable();
        this.staffForm.patchValue({ city: null });
      }
    });
  }

  loadMasterData(): void {
    this.isLoading = true;
    forkJoin({
      states: this.masterService.getAllStates(),
      genders: this.masterService.getAllGenders(),
      departments: this.masterService.getAllDepartments(),
      designations: this.masterService.getAllDesignations(),
      employmentTypes: this.masterService.getAllEmploymentTypes(),
      bloodGroups: this.masterService.getAllBloodGroups(),
      religions: this.masterService.getAllReligions(),
      communities: this.masterService.getAllCommunities(),
    }).subscribe({
      next: (data) => {
        this.states = data.states.states;
        this.genders = data.genders.genders;
        this.departments = data.departments.departments;
        this.designations = data.designations.designations;
        this.employmentTypes = data.employmentTypes.employmentTypes;
        this.bloodGroups = data.bloodGroups.bloodGroups;
        this.religions = data.religions.religions;
        this.communities = data.communities.communities;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading master data:', error);
        this.snackBar.open('Error loading form data', 'Close', {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  private loadCitiesForState(stateId: number): void {
    this.isLoading = true;
    this.masterService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        console.log('Cities loaded:', response);
        this.cities = response.cities;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.snackBar.open('Error loading cities', 'Close', { duration: 3000 });
        this.isLoading = false;
      },
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    const maxSize = 5 * 1024 * 1024; // 5MB
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

    if (file) {
      if (!validTypes.includes(file.type)) {
        this.snackBar.open(
          'Please select a valid image file (JPEG/PNG)',
          'Close',
          { duration: 3000 }
        );
        event.target.value = '';
        return;
      }

      if (file.size > maxSize) {
        this.snackBar.open('File size should not exceed 5MB', 'Close', {
          duration: 3000,
        });
        event.target.value = '';
        return;
      }

      this.selectedFile = file;
    }
  }

  private appendToFormData(formData: FormData, key: string, value: any): void {
    if (value === null || value === undefined) {
      return;
    }

    if (value instanceof Blob) {
      formData.append(key, value);
    } else if (typeof value === 'object') {
      formData.append(key, JSON.stringify(value));
    } else {
      formData.append(key, String(value));
    }
  }

  // add-staff.component.ts
  onSubmit(): void {
    if (this.staffForm.valid) {
      this.isLoading = true;
      const formData = new FormData();
      const formValue = this.staffForm.getRawValue();

      // Create staff data object with proper type conversions
      const staffData = {
        firstName: String(formValue.firstName),
        lastName: String(formValue.lastName),
        dateOfBirth: formatDate(formValue.dateOfBirth, 'yyyy-MM-dd', 'en-US'),
        genderId: String(formValue.genderId),
        email: String(formValue.email),
        phoneNumber: String(formValue.phoneNumber),
        emergencyContactName: String(formValue.emergencyContactName),
        emergencyContactNumber: String(formValue.emergencyContactNumber),
        address: String(formValue.address),
        state: String(formValue.state),
        city: String(formValue.city),
        postalCode: String(formValue.postalCode),
        employeeId: String(formValue.employeeId),
        hireDate: formatDate(formValue.hireDate, 'yyyy-MM-dd', 'en-US'),
        departmentId: String(formValue.departmentId),
        designationId: String(formValue.designationId),
        employmentTypeId: String(formValue.employmentTypeId),
        educationLevel: String(formValue.educationLevel),
        degrees: formValue.degrees ? String(formValue.degrees) : '',
        salary: String(formValue.salary),
        bloodGroupId: String(formValue.bloodGroupId),
        religion: formValue.religion ? String(formValue.religion) : '',
        community: formValue.community ? String(formValue.community) : '',
        idProof: String(formValue.idProof),
        isActive: '1', // Set explicitly to '1' for new staff
        employmentStatus: String(formValue.employmentStatus),
        father_name: formValue.father_name ? String(formValue.father_name) : '',
        father_occupation: formValue.father_occupation
          ? String(formValue.father_occupation)
          : '',
        father_contact: formValue.father_contact
          ? String(formValue.father_contact)
          : '',
        father_status: String(formValue.father_status),
        mother_name: formValue.mother_name ? String(formValue.mother_name) : '',
        mother_occupation: formValue.mother_occupation
          ? String(formValue.mother_occupation)
          : '',
        mother_contact: formValue.mother_contact
          ? String(formValue.mother_contact)
          : '',
        mother_status: String(formValue.mother_status),
        family_address: formValue.family_address
          ? String(formValue.family_address)
          : '',
      };

      // Add profile picture if selected
      if (this.selectedFile) {
        formData.append('profilePicture', this.selectedFile);
      }

      // Append all staff data to FormData using helper function
      Object.entries(staffData).forEach(([key, value]) => {
        this.appendToFormData(formData, key, value);
      });

      // Handle siblings array separately if present
      if (formValue.siblings?.length > 0) {
        const siblingsData = formValue.siblings.map((sibling: Sibling) => ({
          name: String(sibling.name),
          date_of_birth: sibling.date_of_birth
            ? formatDate(sibling.date_of_birth, 'yyyy-MM-dd', 'en-US')
            : null,
          gender: String(sibling.gender),
          occupation: sibling.occupation ? String(sibling.occupation) : '',
          marital_status: sibling.marital_status
            ? String(sibling.marital_status)
            : 'Single',
          contact: sibling.contact ? String(sibling.contact) : '',
          status: sibling.status ? String(sibling.status) : 'Living',
          is_emergency_contact: sibling.is_emergency_contact ? '1' : '0',
          additional_info: sibling.additional_info
            ? String(sibling.additional_info)
            : '',
        }));
        formData.append('siblings', JSON.stringify(siblingsData));
      }

      // Submit the form
      this.staffService.addStaff(formData).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.snackBar.open('Staff Added Successfully', 'Close', {
              duration: 2000,
            });
            this.router.navigate(['/site/staffs/list']);
          } else {
            this.snackBar.open(
              response.message || 'Error adding staff',
              'Close',
              {
                duration: 3000,
              }
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error adding staff:', error);
          this.snackBar.open(
            error.error?.message || 'Error adding staff',
            'Close',
            {
              duration: 5000,
            }
          );
        },
      });
    } else {
      this.markFormGroupTouched(this.staffForm);
      this.snackBar.open('Please fill all required fields correctly', 'Close', {
        duration: 2000,
      });
    }
  }

  private submitStaffData(data: any): void {
    this.staffService.addStaff(data).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Staff Added Successfully', 'Close', {
            duration: 2000,
          });
          this.router.navigate(['/site/staffs/list']);
        } else {
          this.snackBar.open(
            response.message || 'Error adding staff',
            'Close',
            {
              duration: 3000,
            }
          );
        }
      },
      error: (error) => {
        console.error('Error adding staff:', error);
        this.snackBar.open(
          error.error?.message || 'Error adding staff',
          'Close',
          { duration: 5000 }
        );
      },
    });
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.staffForm.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  getErrorMessage(fieldName: string): string {
    const control = this.staffForm.get(fieldName);
    if (control?.errors) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['email']) return 'Invalid email address';
      if (control.errors['minlength'])
        return `Minimum length is ${control.errors['minlength'].requiredLength}`;
      if (control.errors['pattern']) {
        switch (fieldName) {
          case 'phoneNumber':
          case 'emergencyContactNumber':
            return 'Please enter a valid 10-digit number';
          case 'postalCode':
            return 'Please enter a valid 6-digit postal code';
          case 'salary':
            return 'Please enter a valid number';
          default:
            return 'Invalid format';
        }
      }
    }
    return '';
  }

  get siblings() {
    return this.staffForm.get('siblings') as FormArray;
  }

  private generateNextEmployeeId(
    currentYear: number,
    lastNumber: number
  ): string {
    const nextNumber = lastNumber + 1;
    return `BG${currentYear}${nextNumber.toString().padStart(3, '0')}`;
  }

  private setNextEmployeeId() {
    const currentYear = new Date().getFullYear();

    this.staffService.getLatestEmployeeId().subscribe({
      next: (response) => {
        let nextId: string;

        if (response.success && response.latestId) {
          // Extract the numeric part after year (last 3 digits)
          const lastIdStr = response.latestId;
          const yearStr = currentYear.toString();
          const lastNumber =
            parseInt(lastIdStr.substring(yearStr.length + 2)) || 0;
          nextId = this.generateNextEmployeeId(currentYear, lastNumber);
        } else {
          // If no existing IDs, start with 001
          nextId = this.generateNextEmployeeId(currentYear, 0);
        }

        this.staffForm.patchValue({
          employeeId: nextId,
        });
      },
      error: (error) => {
        console.error('Error fetching latest employee ID:', error);
        // Fallback to a basic format if there's an error
        const fallbackId = `BG${currentYear}001`;
        this.staffForm.patchValue({
          employeeId: fallbackId,
        });
      },
    });
  }

  createSiblingFormGroup(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      date_of_birth: [null, [Validators.required]],
      gender: ['', [Validators.required]],
      occupation: [''],
      marital_status: ['Single'],
      contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      status: ['Living'],
      is_emergency_contact: [false],
      additional_info: [''],
    });
  }

  addSibling() {
    this.siblings.push(this.createSiblingFormGroup());
  }

  removeSibling(index: number): void {
    this.siblings.removeAt(index);
  }

  maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];

  onCancel(): void {
    this.router.navigate(['/site/staffs/list']);
  }
}
