<div class="add-staff-container">
    <div class="component-header">
      <h1 class="component-title">
        <mat-icon>person_add</mat-icon>
        Add New Staff
      </h1>
    </div>
  
    <mat-card class="content-wrapper">
      <form [formGroup]="staffForm" (ngSubmit)="onSubmit()">
        <!-- Personal Information Section -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>person</mat-icon>
            Personal Information
          </h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>First Name</mat-label>
              <input matInput formControlName="firstName" placeholder="Enter first name">
              <mat-error *ngIf="staffForm.get('firstName')?.hasError('required')">First name is required</mat-error>
              <mat-error *ngIf="staffForm.get('firstName')?.hasError('minlength')">First name must be at least 2 characters</mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Last Name</mat-label>
              <input matInput formControlName="lastName" placeholder="Enter last name">
              <mat-error *ngIf="staffForm.get('lastName')?.hasError('required')">Last name is required</mat-error>
              <mat-error *ngIf="staffForm.get('lastName')?.hasError('minlength')">Last name must be at least 2 characters</mat-error>
            </mat-form-field>
  
            <mat-form-field>
                <mat-label>Date of Birth</mat-label>
                <input matInput [matDatepicker]="dobPicker" formControlName="dateOfBirth">
                <mat-datepicker-toggle matIconSuffix [for]="dobPicker"></mat-datepicker-toggle>
                <mat-datepicker #dobPicker></mat-datepicker>
                <mat-error *ngIf="staffForm.get('dateOfBirth')?.hasError('required')">Date of birth is required</mat-error>
              </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Gender</mat-label>
              <mat-select formControlName="genderId">
                <mat-option *ngFor="let gender of genders" [value]="gender.id">
                  {{gender.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="staffForm.get('genderId')?.hasError('required')">Gender is required</mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Contact Information Section -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>contact_mail</mat-icon>
            Contact Information
          </h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email" placeholder="Enter email">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="staffForm.get('email')?.hasError('required')">Email is required</mat-error>
              <mat-error *ngIf="staffForm.get('email')?.hasError('email')">Please enter a valid email</mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phoneNumber" placeholder="Enter 10-digit number"
                     maxlength="10">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="staffForm.get('phoneNumber')?.hasError('required')">Phone number is required</mat-error>
              <mat-error *ngIf="staffForm.get('phoneNumber')?.hasError('pattern')">Please enter a valid 10-digit number</mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Emergency Contact Name</mat-label>
              <input matInput formControlName="emergencyContactName" placeholder="Enter emergency contact name">
              <mat-icon matSuffix>person_outline</mat-icon>
              <mat-error *ngIf="staffForm.get('emergencyContactName')?.hasError('required')">Emergency contact name is required</mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Emergency Contact Number</mat-label>
              <input matInput formControlName="emergencyContactNumber" placeholder="Enter 10-digit number"
                     maxlength="10">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="staffForm.get('emergencyContactNumber')?.hasError('required')">Emergency contact number is required</mat-error>
              <mat-error *ngIf="staffForm.get('emergencyContactNumber')?.hasError('pattern')">Please enter a valid 10-digit number</mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="form-section">
            <h2 class="section-title">
              <mat-icon>location_on</mat-icon>
              Address Information
            </h2>
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Address</mat-label>
                <input matInput formControlName="address" placeholder="Enter full address">
                <mat-icon matSuffix>home</mat-icon>
                <mat-error *ngIf="staffForm.get('address')?.hasError('required')">Address is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>State</mat-label>
                <mat-select formControlName="state">
                  <mat-option *ngFor="let state of states" [value]="state.id">
                    {{state.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>map</mat-icon>
                <mat-error *ngIf="staffForm.get('state')?.hasError('required')">State is required</mat-error>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>City</mat-label>
                <mat-select formControlName="city" [disabled]="!staffForm.get('state')?.value">
                  <mat-option *ngFor="let city of cities" [value]="city.id">
                    {{city.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>location_city</mat-icon>
                <mat-error *ngIf="staffForm.get('city')?.hasError('required')">City is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Postal Code</mat-label>
                <input matInput formControlName="postalCode" placeholder="Enter 6-digit postal code" maxlength="6">
                <mat-icon matSuffix>local_post_office</mat-icon>
                <mat-error *ngIf="staffForm.get('postalCode')?.hasError('required')">Postal code is required</mat-error>
                <mat-error *ngIf="staffForm.get('postalCode')?.hasError('pattern')">Please enter a valid 6-digit postal code</mat-error>
              </mat-form-field>
            </div>
          </div>
        
          <!-- Add Employment Section -->
          <div class="form-section">
            <h2 class="section-title">
              <mat-icon>work</mat-icon>
              Employment Information
            </h2>
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Employee ID</mat-label>
                <input matInput formControlName="employeeId" placeholder="Enter employee ID">
                <mat-icon matSuffix>badge</mat-icon>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Hire Date</mat-label>
                <input matInput [matDatepicker]="hireDatePicker" formControlName="hireDate">
                <mat-datepicker-toggle matSuffix [for]="hireDatePicker"></mat-datepicker-toggle>
                <mat-datepicker #hireDatePicker></mat-datepicker>
                <mat-error *ngIf="staffForm.get('hireDate')?.hasError('required')">Hire date is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Department</mat-label>
                <mat-select formControlName="departmentId">
                  <mat-option *ngFor="let dept of departments" [value]="dept.id">
                    {{dept.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>business</mat-icon>
                <mat-error *ngIf="staffForm.get('departmentId')?.hasError('required')">Department is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Designation</mat-label>
                <mat-select formControlName="designationId">
                  <mat-option *ngFor="let desig of designations" [value]="desig.id">
                    {{desig.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>work_outline</mat-icon>
                <mat-error *ngIf="staffForm.get('designationId')?.hasError('required')">Designation is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Employment Type</mat-label>
                <mat-select formControlName="employmentTypeId">
                  <mat-option *ngFor="let type of employmentTypes" [value]="type.id">
                    {{type.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>assignment_ind</mat-icon>
                <mat-error *ngIf="staffForm.get('employmentTypeId')?.hasError('required')">Employment type is required</mat-error>
              </mat-form-field>
            </div>
          </div>
        
          <!-- Add Education & Salary Section -->
          <div class="form-section">
            <h2 class="section-title">
              <mat-icon>school</mat-icon>
              Education & Salary Information
            </h2>
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Education Level</mat-label>
                <mat-select formControlName="educationLevel">
                  <mat-option *ngFor="let level of educationLevels" [value]="level">
                    {{level}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>grade</mat-icon>
                <mat-error *ngIf="staffForm.get('educationLevel')?.hasError('required')">Education level is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Degrees</mat-label>
                <input matInput formControlName="degrees" placeholder="Enter degrees">
                <mat-icon matSuffix>school</mat-icon>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Salary</mat-label>
                <input matInput type="number" formControlName="salary" placeholder="Enter salary">
                <mat-icon matSuffix>payments</mat-icon>
                <mat-error *ngIf="staffForm.get('salary')?.hasError('required')">Salary is required</mat-error>
                <mat-error *ngIf="staffForm.get('salary')?.hasError('min')">Salary must be greater than 0</mat-error>
              </mat-form-field>
            </div>
          </div>
        
          <!-- Add Additional Information Section -->
          <div class="form-section">
            <h2 class="section-title">
              <mat-icon>more_horiz</mat-icon>
              Additional Information
            </h2>
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Blood Group</mat-label>
                <mat-select formControlName="bloodGroupId">
                  <mat-option *ngFor="let bg of bloodGroups" [value]="bg.id">
                    {{bg.name}}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>bloodtype</mat-icon>
                <mat-error *ngIf="staffForm.get('bloodGroupId')?.hasError('required')">Blood group is required</mat-error>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>Religion</mat-label>
                <mat-select formControlName="religion">
                  <mat-option *ngFor="let religion of religions" [value]="religion.id">
                    {{religion.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Community</mat-label>
                <mat-select formControlName="community">
                  <mat-option *ngFor="let community of communities" [value]="community.id">
                    {{community.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
        
              <mat-form-field appearance="outline">
                <mat-label>ID Proof</mat-label>
                <input matInput formControlName="idProof" placeholder="Enter ID proof details">
                <mat-icon matSuffix>badge</mat-icon>
                <mat-error *ngIf="staffForm.get('idProof')?.hasError('required')">ID proof is required</mat-error>
              </mat-form-field>
            </div>
          </div>


          <!-- Family Information Section -->
<div class="form-section">
  <h2 class="section-title">
    <mat-icon>family_restroom</mat-icon>
    Family Information
  </h2>

  <div class="form-grid">
    <!-- Father's Information -->
    <mat-form-field appearance="outline">
      <mat-label>Father's Name</mat-label>
      <input matInput formControlName="father_name" placeholder="Enter father's name">
      <mat-icon matSuffix>person</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Father's Occupation</mat-label>
      <input matInput formControlName="father_occupation" placeholder="Enter father's occupation">
      <mat-icon matSuffix>work</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Father's Contact</mat-label>
      <input matInput formControlName="father_contact" placeholder="Enter father's contact">
      <mat-icon matSuffix>phone</mat-icon>
      <mat-error *ngIf="isFieldInvalid('father_contact')">Please enter a valid 10-digit number</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Father's Status</mat-label>
      <mat-select formControlName="father_status">
        <mat-option value="Living">Living</mat-option>
        <mat-option value="Deceased">Deceased</mat-option>
      </mat-select>
      <mat-icon matSuffix>info</mat-icon>
    </mat-form-field>

    <!-- Mother's Information -->
    <mat-form-field appearance="outline">
      <mat-label>Mother's Name</mat-label>
      <input matInput formControlName="mother_name" placeholder="Enter mother's name">
      <mat-icon matSuffix>person</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Mother's Occupation</mat-label>
      <input matInput formControlName="mother_occupation" placeholder="Enter mother's occupation">
      <mat-icon matSuffix>work</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Mother's Contact</mat-label>
      <input matInput formControlName="mother_contact" placeholder="Enter mother's contact">
      <mat-icon matSuffix>phone</mat-icon>
      <mat-error *ngIf="isFieldInvalid('mother_contact')">Please enter a valid 10-digit number</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Mother's Status</mat-label>
      <mat-select formControlName="mother_status">
        <mat-option value="Living">Living</mat-option>
        <mat-option value="Deceased">Deceased</mat-option>
      </mat-select>
      <mat-icon matSuffix>info</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Family Address</mat-label>
      <textarea matInput formControlName="family_address" rows="3" placeholder="Enter family address"></textarea>
      <mat-icon matSuffix>home</mat-icon>
    </mat-form-field>
  </div>
</div>

<!-- Siblings Section -->
<div class="form-section">
  <h2 class="section-title">
    <mat-icon>people_outline</mat-icon>
    Siblings
  </h2>

  <div formArrayName="siblings">
    <div *ngFor="let sibling of siblings.controls; let i=index" [formGroupName]="i" class="sibling-form">
      <div class="sibling-header">
        <h3>Sibling {{i + 1}}</h3>
        <button mat-icon-button color="warn" (click)="removeSibling(i)">
          <mat-icon>delete</mat-icon>
        </button>
      </div>

      <div class="form-grid">
        <mat-form-field appearance="outline">
          <mat-label>Name</mat-label>
          <input matInput formControlName="name" placeholder="Enter sibling's name">
          <mat-icon matSuffix>person</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Date of Birth</mat-label>
          <input matInput [matDatepicker]="siblingDob" formControlName="date_of_birth">
          <mat-datepicker-toggle matSuffix [for]="siblingDob"></mat-datepicker-toggle>
          <mat-datepicker #siblingDob></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Gender</mat-label>
          <mat-select formControlName="gender">
            <mat-option value="Male">Male</mat-option>
            <mat-option value="Female">Female</mat-option>
            <mat-option value="Other">Other</mat-option>
          </mat-select>
          <mat-icon matSuffix>person_outline</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Occupation</mat-label>
          <input matInput formControlName="occupation" placeholder="Enter occupation">
          <mat-icon matSuffix>work</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Marital Status</mat-label>
          <mat-select formControlName="marital_status">
            <mat-option *ngFor="let status of maritalStatusOptions" [value]="status">
              {{status}}
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>family_restroom</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Contact</mat-label>
          <input matInput formControlName="contact" placeholder="Enter contact number">
          <mat-icon matSuffix>phone</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select formControlName="status">
            <mat-option value="Living">Living</mat-option>
            <mat-option value="Deceased">Deceased</mat-option>
          </mat-select>
          <mat-icon matSuffix>info</mat-icon>
        </mat-form-field>

        <div class="emergency-contact-toggle">
          <mat-slide-toggle formControlName="is_emergency_contact">
            Emergency Contact
          </mat-slide-toggle>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Additional Information</mat-label>
          <textarea matInput formControlName="additional_info" rows="2" placeholder="Enter any additional information"></textarea>
          <mat-icon matSuffix>note</mat-icon>
        </mat-form-field>
      </div>
    </div>
  </div>

  <button type="button" mat-raised-button color="primary" (click)="addSibling()">
    <mat-icon>add</mat-icon>
    Add Sibling
  </button>
</div>
       

        
  
        <!-- Status Section -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>settings</mat-icon>
            Status & Profile Picture
          </h2>
          <div class="form-grid">
            <div class="status-controls">
              <mat-slide-toggle formControlName="isActive" color="primary">
                Is Active
              </mat-slide-toggle>
            
              <mat-form-field appearance="outline">
                <mat-label>Employment Status</mat-label>
                <mat-select formControlName="employmentStatus">
                  <mat-option value="Active">Active</mat-option>
                  <mat-option value="On Leave">On Leave</mat-option>
                  <mat-option value="Terminated">Terminated</mat-option>
                </mat-select>
                <mat-icon matSuffix>work_history</mat-icon>
              </mat-form-field>
            </div>
  
            <div class="file-input-container">
              <label class="file-input-label">Profile Picture</label>
              <div class="file-input-wrapper">
                <button type="button" mat-raised-button (click)="fileInput.click()">
                  <mat-icon>cloud_upload</mat-icon>
                  Choose File
                </button>
                <input #fileInput type="file" (change)="onFileSelected($event)" 
                       accept="image/jpeg,image/png" style="display: none">
                <span class="file-name" *ngIf="selectedFile">{{selectedFile.name}}</span>
                <span class="file-size" *ngIf="selectedFile">({{(selectedFile.size / 1024 / 1024).toFixed(2)}} MB)</span>
              </div>
              <small class="file-hint">Allowed formats: JPG, PNG. Max size: 5MB</small>
            </div>
          </div>
        </div>
  
        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-stroked-button color="warn" (click)="onCancel()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="staffForm.invalid || !staffForm.dirty">
            <mat-icon>save</mat-icon>
            Save Staff
          </button>
        </div>
      </form>
    </mat-card>
  </div>