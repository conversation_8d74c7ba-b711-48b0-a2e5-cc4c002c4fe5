.add-staff-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .component-header {
    margin-bottom: 24px;

    .component-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 24px;
      color: #1976d2;
      margin: 0;

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }
    }
  }

  .content-wrapper {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    form {
      padding: 24px;
    }
  }

  .form-section {
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e0e0e0;

      mat-icon {
        color: #1976d2;
      }
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      align-items: start;

      .full-width {
        grid-column: 1 / -1;
      }

      mat-form-field {
        width: 100%;

        &.mat-form-field-appearance-outline {
          .mat-form-field-wrapper {
            margin: 0;
          }
        }
      }
    }
  }

  .status-controls {
    display: flex;
    flex-direction: column;
    gap: 16px;

    mat-slide-toggle {
      margin-top: 8px;
    }

    mat-form-field {
      margin-top: 8px;
    }
  }

  .file-input-container {
    .file-input-label {
      display: block;
      margin-bottom: 8px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 14px;
    }

    .file-input-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        
        mat-icon {
          font-size: 20px;
        }
      }

      .file-name {
        color: rgba(0, 0, 0, 0.87);
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;

    button {
      min-width: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px 24px;

      mat-icon {
        font-size: 20px;
      }

      &[color="primary"] {
        background-color: #1976d2;
        color: white;

        &:hover {
          background-color: #1565c0;
        }

        &:disabled {
          background-color: rgba(0, 0, 0, 0.12);
          color: rgba(0, 0, 0, 0.38);
        }
      }

      &[color="warn"] {
        color: #f44336;
        border-color: rgba(244, 67, 54, 0.5);

        &:hover {
          background-color: rgba(244, 67, 54, 0.04);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 1200px) {
    padding: 16px;

    .form-section {
      .form-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .component-header {
      .component-title {
        font-size: 20px;

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }
    }

    .form-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
      }

      .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .file-input-container {
      .file-input-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .file-name {
          max-width: 100%;
        }
      }
    }

    .form-actions {
      flex-direction: column-reverse;
      gap: 12px;

      button {
        width: 100%;
      }
    }
  }

  // Custom scrollbar styles
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;

    &:hover {
      background: #666;
    }
  }

  // Material form field customizations
  ::ng-deep {
    .mat-form-field-appearance-outline {
      .mat-form-field-outline {
        background-color: #fafafa;
      }

      &.mat-focused {
        .mat-form-field-outline {
          background-color: white;
        }
      }
    }

    .mat-form-field-subscript-wrapper {
      margin-top: 0;
    }

    .mat-form-field-infix {
      width: auto;
      min-width: 0;
    }

    mat-datepicker-toggle {
      color: rgba(0, 0, 0, 0.54);
    }

    .mat-select-panel {
      max-height: 300px;
    }
  }

  .sibling-form {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
  
    .sibling-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
  
      h3 {
        margin: 0;
        color: #1976d2;
      }
    }
  }
  
  button[mat-raised-button] {
    margin-top: 16px;
  }
}