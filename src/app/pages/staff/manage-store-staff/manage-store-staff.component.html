<div class="manage-store-staff-container">
  <div class="component-header">
    <h2 class="component-title">
      <mat-icon>store</mat-icon>
      Store Manage Staff - {{storeName}}
    </h2>
  </div>

  <div class="content-wrapper">
    <div class="actions-row">
      <mat-form-field class="search-field" appearance="outline">
        <mat-label>Search Staff</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Search staff members..." #input>
        <mat-icon matPrefix>search</mat-icon>
      </mat-form-field>

      <div class="action-buttons">
        <button mat-raised-button color="warn" [disabled]="!selection.hasValue()" (click)="bulkDeleteSelected()">
          <mat-icon>delete</mat-icon>
          Remove Selected
        </button>
        <button mat-raised-button color="primary" (click)="assignStaff()">
          <mat-icon>person_add</mat-icon>
          Assign Staff
        </button>
        <button mat-raised-button (click)="back()">
          <mat-icon>arrow_back</mat-icon>
          Back to Stores
        </button>
      </div>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="dataSource" matSort>
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                         [checked]="selection.hasValue() && isAllSelected()"
                         [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Employee ID Column -->
        <ng-container matColumnDef="employeeId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee ID </th>
          <td mat-cell *matCellDef="let element"> {{element.employeeId}} </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
          <td mat-cell *matCellDef="let element"> {{element.firstName}} {{element.lastName}} </td>
        </ng-container>

        <!-- Designation Column -->
        <ng-container matColumnDef="designation_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation </th>
          <td mat-cell *matCellDef="let element"> {{element.designation_name || 'N/A'}} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="mapping_active">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td mat-cell *matCellDef="let element">
            <mat-icon class="status-icon" [ngClass]="{'active': element.mapping_active}" 
                     (click)="toggleStatus(element)">
              {{element.mapping_active ? 'check_circle' : 'cancel'}}
            </mat-icon>
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by_username">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
          <td mat-cell *matCellDef="let element"> {{element.created_by_username || 'N/A'}} </td>
        </ng-container>

        <!-- Updated At Column -->
        <ng-container matColumnDef="updated_at">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Updated At </th>
          <td mat-cell *matCellDef="let element"> {{element.updated_at | date:'medium'}} </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button color="warn" (click)="confirmRemoveStaff(element.id)"
                    matTooltip="Remove staff member">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of staff members">
      </mat-paginator>
    </div>
  </div>
</div>