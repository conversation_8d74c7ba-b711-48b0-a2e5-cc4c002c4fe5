.manage-store-staff-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);

  .component-header {
    margin-bottom: 20px;

    .component-title {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #666;
      }
    }
  }

  .content-wrapper {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .list-section {
      .actions-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .search-field {
          width: 300px;
        }

        .action-buttons {
          display: flex;
          gap: 10px;

          button {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .table-container {
        overflow-x: auto;

        table {
          width: 100%;

          .mat-column-select {
            width: 48px;
          }

          .mat-column-employeeId {
            width: 100px;
          }

          .mat-column-fullName {
            min-width: 150px;
          }

          .mat-column-designation_name {
            min-width: 120px;
          }

          .mat-column-mapping_active {
            width: 80px;
            text-align: center;

            .status-icon {
              cursor: pointer;
              
              &.active {
                color: #4CAF50;
              }

              &:not(.active) {
                color: #F44336;
              }
            }
          }

          .mat-column-created_by_username {
            min-width: 120px;
          }

          .mat-column-updated_at {
            min-width: 160px;
          }

          .mat-column-actions {
            width: 80px;
            text-align: center;
          }

          // Header styles
          th.mat-header-cell {
            color: #666;
            font-weight: 500;
            font-size: 14px;
            padding: 0 8px;
            white-space: nowrap;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);

            &:first-child {
              padding-left: 16px;
            }

            &:last-child {
              padding-right: 16px;
            }
          }

          // Cell styles
          td.mat-cell {
            padding: 12px 8px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);

            &:first-child {
              padding-left: 16px;
            }

            &:last-child {
              padding-right: 16px;
            }
          }

          // Row styles
          tr.mat-row {
            height: 48px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }

            &.selected-row {
              background-color: rgba(0, 0, 0, 0.04);
            }
          }
        }

        // Empty state
        .no-data {
          padding: 20px;
          text-align: center;
          color: #666;
          font-size: 16px;
        }
      }
    }
  }
}

// Additional utility classes
.primary-button {
  background-color: #1976d2;
  color: white;
}

.warn-button {
  background-color: #f44336;
  color: white;
}

.default-button {
  background-color: #ffffff;
  border: 1px solid #dddddd;
}

// Responsive styles
@media screen and (max-width: 960px) {
  .manage-store-staff-container {
    padding: 16px;

    .content-wrapper {
      .list-section {
        .actions-row {
          flex-direction: column;
          gap: 16px;

          .search-field {
            width: 100%;
          }

          .action-buttons {
            width: 100%;
            justify-content: space-between;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 600px) {
  .manage-store-staff-container {
    padding: 12px;

    .component-header {
      .component-title {
        font-size: 20px;
      }
    }

    .content-wrapper {
      padding: 12px;

      .action-buttons {
        flex-direction: column;
        
        button {
          width: 100%;
        }
      }
    }
  }
}

// Material overrides
::ng-deep {
  .mat-mdc-checkbox {
    .mdc-checkbox {
      padding: 0;
      
      .mdc-checkbox__background {
        border-color: rgba(0, 0, 0, 0.54);
      }
    }
  }

  .mat-mdc-paginator {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
  }

  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: white;
    }
  }
}