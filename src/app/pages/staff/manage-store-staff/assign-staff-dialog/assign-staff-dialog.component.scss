// assign-staff-dialog.component.scss

.assign-staff-dialog {
    padding: 0;
    max-width: 1200px;
    width: 100%;
  
    h2 {
      margin: 0;
      padding: 16px 24px;
      background: #f5f5f5;
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);
    }
  
    .mat-dialog-content {
      margin: 0;
      padding: 20px;
      max-height: 70vh;
  
      .search-container {
        margin-bottom: 16px;
  
        .search-field {
          width: 100%;
        }
      }
  
      .table-container {
        overflow: auto;
        margin: -16px;  // Compensate for table cell padding
        
        table {
          width: 100%;
  
          .mat-column-select {
            width: 48px;
            padding-right: 8px;
          }
  
          .mat-column-employeeId {
            min-width: 120px;
          }
  
          .mat-column-name {
            min-width: 200px;
          }
  
          .mat-column-designation_name {
            min-width: 150px;
          }
  
          .mat-column-department_name {
            min-width: 150px;
          }
  
          th.mat-header-cell {
            background: #fafafa;
            padding: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
          }
  
          td.mat-cell {
            padding: 16px;
          }
  
          tr.mat-row {
            cursor: pointer;
  
            &:hover {
              background: rgba(0, 0, 0, 0.04);
            }
          }
        }
      }
    }
  
    .mat-dialog-actions {
      padding: 16px 24px;
      margin: 0;
      border-top: 1px solid rgba(0, 0, 0, 0.12);
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }
  
  // Scrollbar styling
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  
    &:hover {
      background: #666;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 600px) {
    .assign-staff-dialog {
      .mat-dialog-content {
        max-height: calc(100vh - 152px);  // Account for header and actions
  
        .table-container {
          margin: 0;
          
          table {
            th.mat-header-cell,
            td.mat-cell {
              padding: 12px 8px;
            }
          }
        }
      }
  
      .mat-dialog-actions {
        padding: 12px;
      }
    }
  }