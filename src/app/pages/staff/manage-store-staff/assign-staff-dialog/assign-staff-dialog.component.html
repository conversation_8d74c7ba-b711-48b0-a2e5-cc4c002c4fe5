<div class="assign-staff-dialog">
  <h2 mat-dialog-title class="dialog-title">Assign Staff to Store</h2>

  <div mat-dialog-content class="dialog-content">
      <!-- Search field -->
      <div class="search-container">
          <mat-form-field appearance="outline" class="w-full">
              <mat-label>Search Staff</mat-label>
              <input matInput (keyup)="applyFilter($event)" placeholder="Search by name, ID, designation...">
              <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
      </div>

      <!-- Loading spinner -->
      <div *ngIf="loading" class="loading-spinner-container">
          <mat-spinner diameter="40"></mat-spinner>
      </div>

      <!-- Table container -->
      <div class="table-container" [class.loading]="loading">
          <table mat-table [dataSource]="dataSource" matSort>
              <!-- Checkbox Column -->
              <ng-container matColumnDef="select">
                  <th mat-header-cell *matHeaderCellDef>
                      <mat-checkbox
                          (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                          [aria-label]="'Select all staff'">
                      </mat-checkbox>
                  </th>
                  <td mat-cell *matCellDef="let row">
                      <mat-checkbox
                          (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)"
                          [disabled]="!isSelectable(row)"
                          [aria-label]="'Select ' + row.firstName">
                      </mat-checkbox>
                  </td>
              </ng-container>

              <!-- Employee ID Column -->
              <ng-container matColumnDef="employeeId">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee ID </th>
                  <td mat-cell *matCellDef="let element"> {{element.employeeId}} </td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
                  <td mat-cell *matCellDef="let element"> {{element.firstName}} {{element.lastName}} </td>
              </ng-container>

              <!-- Designation Column -->
              <ng-container matColumnDef="designation_name">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation </th>
                  <td mat-cell *matCellDef="let element"> {{element.designation_name}} </td>
              </ng-container>

              <!-- Department Column -->
              <ng-container matColumnDef="department_name">
                  <th mat-header-cell *matHeaderCellDef mat-sort-header> Department </th>
                  <td mat-cell *matCellDef="let element"> {{element.department_name}} </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                  (click)="toggleRow(row)"
                  [ngClass]="getRowClass(row)"
                  [class.non-selectable]="!isSelectable(row)">
              </tr>
          </table>

          <!-- No data message -->
          <div *ngIf="!loading && dataSource.data.length === 0" class="no-data-message">
              No staff available for assignment
          </div>

          <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" 
                        [pageSize]="10"
                        showFirstLastButtons
                        aria-label="Select page of staff members">
          </mat-paginator>
      </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button 
              color="primary" 
              [disabled]="!selection.hasValue() || loading"
              (click)="assignSelected()">
          Assign Selected Staff
      </button>
  </div>
</div>