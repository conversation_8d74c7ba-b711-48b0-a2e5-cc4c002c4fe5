import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { StaffService } from '../../../../services/staff.service';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StaffStoreService } from '../../../../services/staff-store.service';

export interface AssignStaffDialogData {
  storeId: number;
  existingStaffIds: number[];
}

interface StaffTableData {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  designation_name: string;
  department_name: string;
}

@Component({
  selector: 'app-assign-staff-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatTableModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatPaginatorModule,
    MatSortModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './assign-staff-dialog.component.html',
  styleUrls: ['./assign-staff-dialog.component.scss'],
})
export class AssignStaffDialogComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'employeeId',
    'name',
    'designation_name',
    'department_name',
  ];
  dataSource = new MatTableDataSource<any>([]);
  selection = new SelectionModel<any>(true, []);
  loading = false;

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    if (paginator) {
      this.dataSource.paginator = paginator;
    }
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    if (sort) {
      this.dataSource.sort = sort;
    }
  }

  constructor(
    public dialogRef: MatDialogRef<AssignStaffDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AssignStaffDialogData,
    private staffService: StaffService,
    private staffStoreService: StaffStoreService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadAvailableStaff();
    this.setupFilterPredicate();
  }

  private setupFilterPredicate() {
    this.dataSource.filterPredicate = (
      data: StaffTableData,
      filter: string
    ) => {
      const searchStr = filter.toLowerCase();
      const fullName = `${data.firstName} ${data.lastName}`.toLowerCase();
      return (
        data.employeeId?.toLowerCase().includes(searchStr) ||
        fullName.includes(searchStr) ||
        data.designation_name?.toLowerCase().includes(searchStr) ||
        data.department_name?.toLowerCase().includes(searchStr)
      );
    };
  }

  loadAvailableStaff() {
    this.loading = true;
    this.staffStoreService.getUnassignedStaff().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.staff;
          if (this.sort) {
            this.dataSource.sort = this.sort;
          }
          if (this.paginator) {
            this.dataSource.paginator = this.paginator;
          }
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading staff:', error);
        this.snackBar.open('Error loading staff members', 'Close', {
          duration: 3000,
        });
        this.loading = false;
      },
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.filteredData.length;
    return numSelected > 0 && numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.filteredData.forEach((row) => this.selection.select(row));
    }
  }

  toggleRow(row: StaffTableData) {
    if (this.isSelectable(row)) {
      this.selection.toggle(row);
    }
  }

  assignSelected() {
    console.log('Assign button clicked');
    if (!this.selection.selected.length) {
      this.snackBar.open('Please select at least one staff member', 'Close', {
        duration: 3000,
      });
      return;
    }

    const selectedStaff = this.selection.selected.map((staff) => ({
      staff_id: staff.id,
      store_id: this.data.storeId,
    }));

    this.loading = true;
    this.staffStoreService.bulkAssignStaffToStore(selectedStaff).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Staff assigned successfully', 'Close', {
            duration: 3000,
          });
          this.dialogRef.close({ success: true });
        } else {
          this.snackBar.open('Failed to assign staff', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error assigning staff:', error);
        this.snackBar.open('Error assigning staff', 'Close', {
          duration: 3000,
        });
      },
      complete: () => {
        this.loading = false;
      },
    });
  }

  onCancel(): void {
    this.dialogRef.close({
      success: false,
    });
  }

  isSelectable(row: StaffTableData): boolean {
    return !this.data.existingStaffIds.includes(row.id);
  }

  getTableHeight(): string {
    return this.dataSource.data.length === 0 ? '100px' : 'auto';
  }

  // Optional: Add method to get row class
  getRowClass(row: StaffTableData): string {
    return this.selection.isSelected(row) ? 'selected-row' : '';
  }
}
