import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { StaffStoreService } from '../../../services/staff-store.service';
import { MasterserviceService } from '../../../services/masterservice.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignStaffDialogComponent } from './assign-staff-dialog/assign-staff-dialog.component';
import { ConfirmDialogComponent } from '../../../components/confirm-dialog/confirm-dialog.component';

interface StaffMapping {
  id: number;
  firstName: string;
  lastName: string;
  employeeId: string;
  designationId: number;
  designation_name?: string;
  department_name?: string;
  mapping_active?: boolean;
  store_id: number;
  created_at: string;
  updated_at: string;
  created_by: number;
  created_by_username?: string;
  isActive: number;
}

@Component({
  selector: 'app-manage-store-staff',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './manage-store-staff.component.html',
  styleUrls: ['./manage-store-staff.component.scss'],
})
export class ManageStoreStaffComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'employeeId',
    'name',
    'designation_name',
    'mapping_active',
    'created_by_username',
    'updated_at',
    'actions',
  ];
  dataSource: MatTableDataSource<StaffMapping>;
  selection = new SelectionModel<StaffMapping>(true, []);
  storeId: number;
  storeName: string = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private staffStoreService: StaffStoreService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.storeId = Number(this.route.snapshot.params['id']);
    this.dataSource = new MatTableDataSource<StaffMapping>([]);
  }

  ngOnInit() {
    this.loadStoreDetails();
    this.loadStoreStaff();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.setupFilter();
  }

  private setupFilter() {
    this.dataSource.filterPredicate = (data: StaffMapping, filter: string) => {
      const searchStr = filter.toLowerCase();
      return (
        data.employeeId?.toLowerCase().includes(searchStr) ||
        '' ||
        data.firstName?.toLowerCase().includes(searchStr) ||
        '' ||
        data.lastName?.toLowerCase().includes(searchStr) ||
        '' ||
        (data.designation_name?.toLowerCase() || '').includes(searchStr)
      );
    };
  }

  loadStoreDetails() {
    this.masterService.getStoreById(this.storeId).subscribe({
      next: (response) => {
        if (response.success) {
          this.storeName = response.store.name;
        }
      },
      error: (error) => {
        console.error('Error loading store details:', error);
        this.snackBar.open('Error loading store details', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadStoreStaff() {
    this.masterService.getStoreStaff(this.storeId).subscribe({
      next: (response) => {
        if (response.success) {
          // Transform the data to match our needs
          const transformedData = response.staff.map((staff: StaffMapping) => ({
            ...staff,
            mapping_active: staff.isActive === 1,
            // Add any additional transformations needed
          }));
          console.log('Transformed data:', transformedData);
          this.dataSource.data = transformedData;
        }
      },
      error: (error) => {
        console.error('Error loading store staff:', error);
        this.snackBar.open('Error loading store staff', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  assignStaff() {
    const dialogRef = this.dialog.open(AssignStaffDialogComponent, {
      width: '800px',
      data: {
        storeId: this.storeId,
        existingStaffIds: this.dataSource.data.map((staff) => staff.id),
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadStoreStaff();
      }
    });
  }

  confirmRemoveStaff(staffId: number) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Removal',
        message: 'Are you sure you want to remove this staff member?',
        confirmText: 'Remove',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.removeStaff(staffId);
      }
    });
  }

  back() {
    this.router.navigate(['/site/master/stores']);
  }

  removeSelectedStaff() {
    if (!this.selection.selected.length) {
      this.snackBar.open('Please select staff members to remove', 'Close', {
        duration: 3000,
      });
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Removal',
        message: `Are you sure you want to remove ${this.selection.selected.length} staff members?`,
        confirmText: 'Remove',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Get all staff IDs and their store IDs
        const promises = this.selection.selected.map((staff) =>
          this.staffStoreService
            .removeStaffFromStore(staff.id, this.storeId)
            .toPromise()
        );

        Promise.all(promises)
          .then(() => {
            this.snackBar.open('Staff members removed successfully', 'Close', {
              duration: 3000,
            });
            this.selection.clear();
            this.loadStoreStaff();
          })
          .catch((error) => {
            console.error('Error removing staff members:', error);
            this.snackBar.open('Error removing staff members', 'Close', {
              duration: 3000,
            });
          });
      }
    });
  }

  removeStaff(staffId: number) {
    this.staffStoreService
      .removeStaffFromStore(staffId, this.storeId)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Staff removed successfully', 'Close', {
              duration: 3000,
            });
            this.loadStoreStaff();
          } else {
            this.snackBar.open('Failed to remove staff', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error removing staff:', error);
          this.snackBar.open('Error removing staff', 'Close', {
            duration: 3000,
          });
        },
      });
  }

  toggleStatus(staff: StaffMapping) {
    if (!staff.id) {
      this.snackBar.open('Invalid staff mapping', 'Close', {
        duration: 3000,
      });
      return;
    }

    const newStatus = !staff.mapping_active;

    this.staffStoreService
      .updateStaffStoreStatus(staff.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Status updated successfully', 'Close', {
              duration: 3000,
            });
            this.loadStoreStaff();
          } else {
            this.snackBar.open('Failed to update status', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error updating status:', error);
          this.snackBar.open('Error updating status', 'Close', {
            duration: 3000,
          });
        },
      });
  }

  // Replace your existing bulkDeleteSelected with this:
  bulkDeleteSelected() {
    this.removeSelectedStaff(); // This will handle the confirmation dialog and removal
  }
}
