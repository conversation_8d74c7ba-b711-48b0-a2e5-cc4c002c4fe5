<!-- edit-staff.component.html -->
<div class="edit-staff-container">
    <div class="component-header">
      <h1 class="component-title">
        <mat-icon>edit</mat-icon>
        Edit Staff Member
      </h1>
    </div>
  
    <mat-card class="content-wrapper">
      <form [formGroup]="staffForm" (ngSubmit)="onSubmit()">
        <!-- Personal Information Section -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>person</mat-icon>
            Personal Information
          </h2>
          <div class="profile-picture-section">
            <div class="current-picture" *ngIf="currentProfilePicture">
              <img [src]="'http://localhost:3000/uploads/' + currentProfilePicture" alt="Profile Picture">
            </div>
            <div class="upload-section">
              <button type="button" mat-raised-button (click)="fileInput.click()">
                <mat-icon>cloud_upload</mat-icon>
                Change Profile Picture
              </button>
              <input #fileInput type="file" (change)="onFileSelected($event)" 
                     accept="image/jpeg,image/png" style="display: none">
              <span *ngIf="selectedFile">{{selectedFile.name}}</span>
            </div>
          </div>
          
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>First Name</mat-label>
              <input matInput formControlName="firstName">
              <mat-error *ngIf="isFieldInvalid('firstName')">
                {{getErrorMessage('firstName')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Last Name</mat-label>
              <input matInput formControlName="lastName">
              <mat-error *ngIf="isFieldInvalid('lastName')">
                {{getErrorMessage('lastName')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Date of Birth</mat-label>
              <input matInput [matDatepicker]="dobPicker" formControlName="dateOfBirth">
              <mat-datepicker-toggle matSuffix [for]="dobPicker"></mat-datepicker-toggle>
              <mat-datepicker #dobPicker></mat-datepicker>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Gender</mat-label>
              <mat-select formControlName="genderId">
                <mat-option *ngFor="let gender of genders" [value]="gender.id">
                  {{gender.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Contact Information -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>contact_mail</mat-icon>
            Contact Information
          </h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email">
              <mat-error *ngIf="isFieldInvalid('email')">
                {{getErrorMessage('email')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phoneNumber">
              <mat-error *ngIf="isFieldInvalid('phoneNumber')">
                {{getErrorMessage('phoneNumber')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Emergency Contact Name</mat-label>
              <input matInput formControlName="emergencyContactName">
              <mat-error *ngIf="isFieldInvalid('emergencyContactName')">
                {{getErrorMessage('emergencyContactName')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Emergency Contact Number</mat-label>
              <input matInput formControlName="emergencyContactNumber">
              <mat-error *ngIf="isFieldInvalid('emergencyContactNumber')">
                {{getErrorMessage('emergencyContactNumber')}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Employment Information -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>work</mat-icon>
            Employment Information
          </h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Employee ID</mat-label>
              <input matInput formControlName="employeeId" readonly>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Department</mat-label>
              <mat-select formControlName="departmentId">
                <mat-option *ngFor="let dept of departments" [value]="dept.id">
                  {{dept.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Designation</mat-label>
              <mat-select formControlName="designationId">
                <mat-option *ngFor="let desig of designations" [value]="desig.id">
                  {{desig.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Employment Type</mat-label>
              <mat-select formControlName="employmentTypeId">
                <mat-option *ngFor="let type of employmentTypes" [value]="type.id">
                  {{type.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Hire Date</mat-label>
              <input matInput [matDatepicker]="hireDatePicker" formControlName="hireDate">
              <mat-datepicker-toggle matSuffix [for]="hireDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #hireDatePicker></mat-datepicker>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Salary</mat-label>
              <input matInput type="number" formControlName="salary">
              <mat-error *ngIf="isFieldInvalid('salary')">
                {{getErrorMessage('salary')}}
              </mat-error>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Employment Status</mat-label>
              <mat-select formControlName="employmentStatus">
                <mat-option *ngFor="let status of employmentStatuses" [value]="status">
                  {{status}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Additional Information -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>more_horiz</mat-icon>
            Additional Information
          </h2>
          <div class="form-grid">
            <mat-form-field appearance="outline">
              <mat-label>Blood Group</mat-label>
              <mat-select formControlName="bloodGroupId">
                <mat-option *ngFor="let bg of bloodGroups" [value]="bg.id">
                  {{bg.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Religion</mat-label>
              <mat-select formControlName="religion">
                <mat-option *ngFor="let religion of religions" [value]="religion.id">
                  {{religion.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
            
            <mat-form-field appearance="outline">
              <mat-label>Community</mat-label>
              <mat-select formControlName="community">
                <mat-option *ngFor="let community of communities" [value]="community.id">
                  {{community.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>


            <mat-form-field appearance="outline">
              <mat-label>ID Proof</mat-label>
              <input matInput formControlName="idProof">
            </mat-form-field>
          </div>
  
          <mat-slide-toggle formControlName="isActive" color="primary">
            Active Status
          </mat-slide-toggle>
        </div>
  
        <!-- Siblings Section -->
        <div class="form-section">
          <h2 class="section-title">
            <mat-icon>people</mat-icon>
            Siblings
            <button type="button" mat-raised-button color="primary" (click)="addSibling()">
              <mat-icon>add</mat-icon> Add Sibling
            </button>
          </h2>
  
          <div formArrayName="siblings">
            <div *ngFor="let sibling of siblings.controls; let i=index" [formGroupName]="i" class="sibling-form">
              <div class="sibling-header">
                <h3>Sibling {{i + 1}}</h3>
                <button type="button" mat-icon-button color="warn" (click)="removeSibling(i)">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
  
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Name</mat-label>
                  <input matInput formControlName="name">
                </mat-form-field>
  
                <mat-form-field appearance="outline">
                  <mat-label>Date of Birth</mat-label>
                  <input matInput [matDatepicker]="siblingDob" formControlName="date_of_birth">
                  <mat-datepicker-toggle matSuffix [for]="siblingDob"></mat-datepicker-toggle>
                  <mat-datepicker #siblingDob></mat-datepicker>
                </mat-form-field>
  
                <mat-form-field appearance="outline">
                  <mat-label>Gender</mat-label>
                  <mat-select formControlName="gender">
                    <mat-option value="Male">Male</mat-option>
                    <mat-option value="Female">Female</mat-option>
                    <mat-option value="Other">Other</mat-option>
                  </mat-select>
                </mat-form-field>
  
                <mat-form-field appearance="outline">
                  <mat-label>Occupation</mat-label>
                  <input matInput formControlName="occupation">
                </mat-form-field>
  
                <mat-form-field appearance="outline">
                  <mat-label>Contact</mat-label>
                  <input matInput formControlName="contact">
                </mat-form-field>
  
                <mat-form-field appearance="outline">
                  <mat-label>Marital Status</mat-label>
                  <mat-select formControlName="marital_status">
                    <mat-option *ngFor="let status of maritalStatusOptions" [value]="status">
                      {{status}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
  
              <mat-slide-toggle formControlName="is_emergency_contact" color="primary">
                Emergency Contact
              </mat-slide-toggle>
            </div>
          </div>
        </div>
  
        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-stroked-button color="warn" (click)="cancel()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="staffForm.invalid || isLoading">
            <mat-icon>save</mat-icon>
            Update Staff
          </button>
        </div>
      </form>
    </mat-card>
  </div>