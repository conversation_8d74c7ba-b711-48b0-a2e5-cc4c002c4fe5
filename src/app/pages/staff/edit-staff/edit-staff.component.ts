// edit-staff.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { StaffService } from '../../../services/staff.service';
import { MasterserviceService } from '../../../services/masterservice.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { forkJoin } from 'rxjs';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'app-edit-staff',
  templateUrl: './edit-staff.component.html',
  styleUrls: ['./edit-staff.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
  ],
})
export class EditStaffComponent implements OnInit {
  staffForm!: FormGroup;
  staffId!: number;
  selectedFile: File | null = null;
  currentProfilePicture: string | null = null;
  isLoading = false;

  // Master data arrays
  genders: any[] = [];
  departments: any[] = [];
  designations: any[] = [];
  employmentTypes: any[] = [];
  bloodGroups: any[] = [];
  states: any[] = [];
  cities: any[] = [];
  religions: any[] = [];
  communities: any[] = [];

  // Constants
  educationLevels = [
    'High School',
    'Diploma',
    "Bachelor's Degree",
    "Master's Degree",
    'Ph.D.',
    'Other',
  ];

  employmentStatuses: string[] = ['Active', 'On Leave', 'Terminated'];
  maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];

  constructor(
    private fb: FormBuilder,
    private staffService: StaffService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.loadMasterData().subscribe(() => {
      this.route.params.subscribe((params) => {
        this.staffId = +params['id'];
        if (this.staffId) {
          this.loadStaffData();
        }
      });
    });
  }

  private initForm() {
    this.staffForm = this.fb.group({
      // Personal Information
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      dateOfBirth: [null, [Validators.required]],
      genderId: [null, [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      emergencyContactName: ['', [Validators.required]],
      emergencyContactNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],

      // Address Information
      address: ['', [Validators.required]],
      state: [null, [Validators.required]],
      city: [{ value: null, disabled: true }, [Validators.required]],
      postalCode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],

      // Employment Information
      employeeId: [{ value: '', disabled: true }],
      hireDate: [null, [Validators.required]],
      departmentId: [null, [Validators.required]],
      designationId: [null, [Validators.required]],
      employmentTypeId: [null, [Validators.required]],
      educationLevel: ['', [Validators.required]],
      degrees: [''],
      salary: ['', [Validators.required, Validators.min(0)]],

      // Additional Information
      bloodGroupId: [null, [Validators.required]],
      religion: [null],
      community: [null],
      idProof: ['', [Validators.required]],
      isActive: [true],
      employmentStatus: ['Active', [Validators.required]],

      // Family Information
      father_name: [''],
      father_occupation: [''],
      father_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      father_status: ['Living'],
      mother_name: [''],
      mother_occupation: [''],
      mother_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      mother_status: ['Living'],
      family_address: [''],

      // Siblings FormArray
      siblings: this.fb.array([]),
    });

    // Handle state-city dependency
    this.staffForm.get('state')?.valueChanges.subscribe((stateId) => {
      const cityControl = this.staffForm.get('city');
      if (stateId) {
        cityControl?.enable();
        this.loadCitiesForState(stateId);
      } else {
        cityControl?.disable();
        cityControl?.setValue(null);
      }
    });
  }

  private loadMasterData() {
    this.isLoading = true;
    return forkJoin({
      states: this.masterService.getAllStates(),
      genders: this.masterService.getAllGenders(),
      departments: this.masterService.getAllDepartments(),
      designations: this.masterService.getAllDesignations(),
      employmentTypes: this.masterService.getAllEmploymentTypes(),
      bloodGroups: this.masterService.getAllBloodGroups(),
      religions: this.masterService.getAllReligions(),
      communities: this.masterService.getAllCommunities(),
    }).pipe(
      tap((data) => {
        this.states = data.states.states;
        this.genders = data.genders.genders;
        this.departments = data.departments.departments;
        this.designations = data.designations.designations;
        this.employmentTypes = data.employmentTypes.employmentTypes;
        this.bloodGroups = data.bloodGroups.bloodGroups;
        this.religions = data.religions.religions;
        this.communities = data.communities.communities;
        this.isLoading = false;
        console.log('Master Data Loaded - Religions:', this.religions);
        console.log('Master Data Loaded - Communities:', this.communities);
      })
    );
  }

  private loadStaffData() {
    this.staffService.getStaffById(this.staffId).subscribe({
      next: (response) => {
        if (response.success) {
          const staffData = response.staff;
          console.log('Staff Data Loaded:', staffData);
          this.currentProfilePicture = staffData.profilePicture;

          // Find religion and community IDs
          const religionId = this.religions.find(
            (r) => r.name.toLowerCase() === staffData.religion?.toLowerCase()
          )?.id;
          const communityId = this.communities.find(
            (c) => c.name.toLowerCase() === staffData.community?.toLowerCase()
          )?.id;

          console.log(
            'Found Religion ID:',
            religionId,
            'for religion:',
            staffData.religion
          );
          console.log(
            'Found Community ID:',
            communityId,
            'for community:',
            staffData.community
          );

          // Load cities for the staff's state
          if (staffData.state) {
            this.loadCitiesForState(staffData.state);
          }

          // Update form with staff data
          this.staffForm.patchValue({
            firstName: staffData.firstName,
            lastName: staffData.lastName,
            dateOfBirth: new Date(staffData.dateOfBirth),
            genderId: staffData.genderId,
            email: staffData.email,
            phoneNumber: staffData.phoneNumber,
            emergencyContactName: staffData.emergencyContactName,
            emergencyContactNumber: staffData.emergencyContactNumber,
            address: staffData.address,
            state: staffData.state,
            city: staffData.city,
            postalCode: staffData.postalCode,
            employeeId: staffData.employeeId,
            hireDate: new Date(staffData.hireDate),
            departmentId: staffData.departmentId,
            designationId: staffData.designationId,
            employmentTypeId: staffData.employmentTypeId,
            educationLevel: staffData.educationLevel,
            degrees: staffData.degrees,
            salary: staffData.salary,
            bloodGroupId: staffData.bloodGroupId,
            religion: religionId,
            community: communityId,
            idProof: staffData.idProof,
            isActive: staffData.isActive,
            employmentStatus: staffData.employmentStatus,
            father_name: staffData.father_name,
            father_occupation: staffData.father_occupation,
            father_contact: staffData.father_contact,
            father_status: staffData.father_status,
            mother_name: staffData.mother_name,
            mother_occupation: staffData.mother_occupation,
            mother_contact: staffData.mother_contact,
            mother_status: staffData.mother_status,
            family_address: staffData.family_address,
          });

          // Load siblings if any
          if (staffData.siblings && staffData.siblings.length > 0) {
            const siblingsFormArray = this.staffForm.get(
              'siblings'
            ) as FormArray;
            siblingsFormArray.clear();
            staffData.siblings.forEach((sibling: any) => {
              siblingsFormArray.push(this.createSiblingFormGroup(sibling));
            });
          }
        }
      },
      error: (error) => {
        console.error('Error loading staff data:', error);
        this.snackBar.open('Error loading staff data', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  private loadCitiesForState(stateId: number) {
    this.masterService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = response.cities;
          this.staffForm.get('city')?.enable();
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.snackBar.open('Error loading cities', 'Close', { duration: 3000 });
      },
    });
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      const maxSize = 5 * 1024 * 1024; // 5MB
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

      if (!validTypes.includes(file.type)) {
        this.snackBar.open(
          'Please select a valid image file (JPEG/PNG)',
          'Close',
          { duration: 3000 }
        );
        event.target.value = '';
        return;
      }

      if (file.size > maxSize) {
        this.snackBar.open('File size should not exceed 5MB', 'Close', {
          duration: 3000,
        });
        event.target.value = '';
        return;
      }

      this.selectedFile = file;
    }
  }

  addSibling() {
    const siblings = this.staffForm.get('siblings') as FormArray;
    siblings.push(this.createSiblingFormGroup());
  }

  removeSibling(index: number) {
    const siblings = this.staffForm.get('siblings') as FormArray;
    siblings.removeAt(index);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  cancel() {
    this.router.navigate(['/site/staffs/list']);
  }

  // Helper methods
  get siblings() {
    return this.staffForm.get('siblings') as FormArray;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.staffForm.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  getErrorMessage(fieldName: string): string {
    const control = this.staffForm.get(fieldName);
    if (control?.errors) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['email']) return 'Invalid email address';
      if (control.errors['minlength'])
        return `Minimum length is ${control.errors['minlength'].requiredLength}`;
      if (control.errors['pattern']) {
        switch (fieldName) {
          case 'phoneNumber':
          case 'emergencyContactNumber':
            return 'Please enter a valid 10-digit number';
          case 'postalCode':
            return 'Please enter a valid 6-digit postal code';
          case 'salary':
            return 'Please enter a valid number';
          default:
            return 'Invalid format';
        }
      }
    }
    return '';
  }

  onStateChange(event: any) {
    const stateId = event.value;
    if (stateId) {
      this.loadCitiesForState(stateId);
    } else {
      this.cities = [];
      this.staffForm.patchValue({ city: null });
    }
  }

  formatDate(date: string | Date): string {
    return date ? new Date(date).toISOString().split('T')[0] : '';
  }

  isSiblingFormValid(index: number): boolean {
    const siblingForm = (this.staffForm.get('siblings') as FormArray).at(
      index
    ) as FormGroup;
    return siblingForm.valid;
  }

  // Add these methods to edit-staff.component.ts
  private transformSiblingData(siblings: any[]): any[] {
    return siblings.map((sibling) => {
      // Keep the id if it exists (for existing siblings)
      const transformedSibling: any = {
        id: sibling.id || null,
        name: sibling.name,
        date_of_birth: sibling.date_of_birth
          ? new Date(sibling.date_of_birth).toISOString().split('T')[0]
          : null,
        gender: sibling.gender,
        occupation: sibling.occupation || '',
        marital_status: sibling.marital_status || 'Single',
        contact: sibling.contact || '',
        status: sibling.status || 'Living',
        is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
        additional_info: sibling.additional_info || '',
      };
      return transformedSibling;
    });
  }

  // Update the createSiblingFormGroup method
  createSiblingFormGroup(sibling: any = null) {
    return this.fb.group({
      id: [sibling?.id || null],
      name: [sibling?.name || '', [Validators.required]],
      date_of_birth: [
        sibling?.date_of_birth ? new Date(sibling.date_of_birth) : null,
        [Validators.required],
      ],
      gender: [sibling?.gender || '', [Validators.required]],
      occupation: [sibling?.occupation || ''],
      marital_status: [sibling?.marital_status || 'Single'],
      contact: [sibling?.contact || '', [Validators.pattern('^[0-9]{10}$')]],
      status: [sibling?.status || 'Living'],
      is_emergency_contact: [sibling?.is_emergency_contact || false],
      additional_info: [sibling?.additional_info || ''],
    });
  }

  // Update the onSubmit method
  onSubmit() {
    if (this.staffForm.valid) {
      const formData = new FormData();
      const formValue = this.staffForm.getRawValue();

      // Handle religion and community separately
      const religionObj = this.religions.find(
        (r) => r.id === formValue.religion
      );
      const communityObj = this.communities.find(
        (c) => c.id === formValue.community
      );

      console.log(
        'Submitting religion:',
        religionObj?.name,
        'from ID:',
        formValue.religion
      );
      console.log(
        'Submitting community:',
        communityObj?.name,
        'from ID:',
        formValue.community
      );

      // Create a modified form value without religion and community
      const { religion, community, siblings, ...otherFormValues } = formValue;

      // Append all other form values
      Object.keys(otherFormValues).forEach((key) => {
        if (
          otherFormValues[key] !== null &&
          otherFormValues[key] !== undefined
        ) {
          if (key === 'dateOfBirth' || key === 'hireDate') {
            formData.append(
              key,
              new Date(otherFormValues[key]).toISOString().split('T')[0]
            );
          } else if (key === 'isActive') {
            formData.append(key, otherFormValues[key] ? '1' : '0');
          } else {
            formData.append(key, otherFormValues[key]);
          }
        }
      });

      // Append religion and community names (not IDs)
      formData.append('religion', religionObj ? religionObj.name : '');
      formData.append('community', communityObj ? communityObj.name : '');

      // Handle siblings data
      if (this.siblings.length > 0) {
        const siblingsData = this.siblings.controls.map((control) => {
          const siblingValue = control.value;
          return {
            id: siblingValue.id || null,
            name: siblingValue.name,
            date_of_birth: siblingValue.date_of_birth
              ? new Date(siblingValue.date_of_birth).toISOString().split('T')[0]
              : null,
            gender: siblingValue.gender,
            occupation: siblingValue.occupation || '',
            marital_status: siblingValue.marital_status || 'Single',
            contact: siblingValue.contact || '',
            status: siblingValue.status || 'Living',
            is_emergency_contact: siblingValue.is_emergency_contact ? 1 : 0,
            additional_info: siblingValue.additional_info || '',
          };
        });
        formData.append('siblings', JSON.stringify(siblingsData));
      }

      // Add profile picture if selected
      if (this.selectedFile) {
        formData.append('profilePicture', this.selectedFile);
      }

      // Log the final form data being sent
      formData.forEach((value, key) => {
        console.log(`${key}:`, value);
      });

      this.isLoading = true;
      this.staffService.updateStaff(this.staffId, formData).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Staff updated successfully', 'Close', {
              duration: 2000,
            });
            this.router.navigate(['/site/staffs/list']);
          } else {
            this.snackBar.open(
              response.message || 'Error updating staff',
              'Close',
              {
                duration: 3000,
              }
            );
            if (response.siblingError) {
              console.error('Sibling update error:', response.siblingError);
            }
          }
        },
        error: (error) => {
          console.error('Error updating staff:', error);
          this.snackBar.open(
            error.error?.message || 'Error updating staff',
            'Close',
            {
              duration: 3000,
            }
          );
        },
        complete: () => {
          this.isLoading = false;
        },
      });
    } else {
      this.markFormGroupTouched(this.staffForm);
      this.snackBar.open('Please fill all required fields correctly', 'Close', {
        duration: 2000,
      });
    }
  }
}
