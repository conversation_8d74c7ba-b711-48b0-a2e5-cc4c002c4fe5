<div class="list-staff-container">
  <div class="component-header">
    <h1 class="component-title">Staff Management</h1>
  </div>

  <div class="content-wrapper">
    <div class="list-section">
      <div class="actions-row">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search Staff</mat-label>
          <input matInput (keyup)="applyFilter($event)" placeholder="Ex. John Doe">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="addStaff()" class="add-staff-button">
            <mat-icon>person_add</mat-icon> Add Staff
          </button>
          <button mat-raised-button color="warn" (click)="openDeleteConfirmationDialog()" *ngIf="selection.hasValue()">
            <mat-icon>delete</mat-icon> Delete Selected
          </button>
        </div>
      </div>

      <div class="table-container">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- Sl. No. Column -->
          <ng-container matColumnDef="sl_no">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sl. No. </th>
            <td mat-cell *matCellDef="let element; let i = index"> {{ getSerialNumber(i) }} </td>
          </ng-container>

          <!-- Employee ID Column -->
          <ng-container matColumnDef="employeeId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee ID </th>
            <td mat-cell *matCellDef="let element"> {{element.employeeId}} </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
            <td mat-cell *matCellDef="let element"> {{element.firstName}} {{element.lastName}} </td>
          </ng-container>

          <!-- Department Column -->
          <ng-container matColumnDef="department_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Department </th>
            <td mat-cell *matCellDef="let element"> {{element.department_name}} </td>
          </ng-container>

          <!-- Designation Column -->
          <ng-container matColumnDef="designation_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation </th>
            <td mat-cell *matCellDef="let element"> {{element.designation_name}} </td>
          </ng-container>

          <!-- Employment Type Column -->
          <ng-container matColumnDef="employment_type_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Type </th>
            <td mat-cell *matCellDef="let element"> {{element.employment_type_name}} </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="employmentStatus">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let element">
              <span [ngClass]="getStatusClass(element.employmentStatus)">
                {{element.employmentStatus}}
              </span>
            </td>
          </ng-container>

          <!-- Active Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
            <td mat-cell *matCellDef="let element">
              <mat-icon [ngClass]="{'active-icon': element.isActive, 'inactive-icon': !element.isActive}">
                {{element.isActive ? 'check_circle' : 'cancel'}}
              </mat-icon>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" (click)="viewStaffDetails(element)">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent" 
                      (click)="editStaff(element); $event.stopPropagation()" 
                      matTooltip="Edit Staff">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button [color]="element.isActive ? 'warn' : 'primary'"
                      (click)="toggleStatus(element); $event.stopPropagation()"
                      [matTooltip]="element.isActive ? 'Deactivate Staff' : 'Activate Staff'">
                <mat-icon>power_settings_new</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              (click)="selection.toggle(row)"
              [class.selected-row]="selection.isSelected(row)">
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>
</div>