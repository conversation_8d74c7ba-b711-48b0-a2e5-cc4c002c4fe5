.list-staff-container {
  padding: 20px;

  .component-header {
    margin-bottom: 20px;

    .component-title {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #666;
      }
    }
  }

  .content-wrapper {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .list-section {
      .actions-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .search-field {
          width: 300px;
        }

        .action-buttons {
          display: flex;
          gap: 10px;

          button {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .table-container {
        overflow-x: auto;

        table {
          width: 100%;

          .mat-column-select {
            width: 48px;
            padding-right: 8px;
          }

          .mat-column-sl_no {
            width: 70px;
          }

          .mat-column-employeeId {
            min-width: 100px;
          }

          .mat-column-fullName {
            min-width: 150px;
          }

          .mat-column-department_name {
            min-width: 120px;
          }

          .mat-column-designation_name {
            min-width: 120px;
          }

          .mat-column-employment_type_name {
            min-width: 100px;
          }

          .mat-column-employmentStatus {
            min-width: 100px;
          }

          .mat-column-isActive {
            width: 80px;
            text-align: center;
          }

          .mat-column-actions {
            width: 225px;
            text-align: center;
          }

          // Header styles
          th.mat-header-cell {
            color: #666;
            font-weight: 500;
            font-size: 14px;
            padding: 0 8px;
            white-space: nowrap;

            &:first-child {
              padding-left: 16px;
            }

            &:last-child {
              padding-right: 16px;
            }
          }

          // Cell styles
          td.mat-cell {
            padding: 12px 8px;
            font-size: 14px;
            color: #333;

            &:first-child {
              padding-left: 16px;
            }

            &:last-child {
              padding-right: 16px;
            }
          }

          // Row hover effect
          tr.mat-row {
            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }

            &.selected-row {
              background-color: rgba(0, 0, 0, 0.04);
            }
          }

          // Status badges
          .status-badge {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;

            &.active {
              background-color: rgba(76, 175, 80, 0.1);
              color: #4CAF50;
            }

            &.on-leave {
              background-color: rgba(255, 152, 0, 0.1);
              color: #FF9800;
            }

            &.terminated {
              background-color: rgba(244, 67, 54, 0.1);
              color: #F44336;
            }
          }

          // Active/Inactive icons
          .active-icon {
            color: #4CAF50;
          }

          .inactive-icon {
            color: #F44336;
          }

          // Action buttons
          button[mat-icon-button] {
            margin: 0 4px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }
          }
        }
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 960px) {
  .list-staff-container {
    .content-wrapper {
      .list-section {
        .actions-row {
          flex-direction: column;
          gap: 16px;

          .search-field {
            width: 100%;
          }

          .action-buttons {
            width: 100%;
            justify-content: space-between;
          }
        }

        .table-container {
          .mat-table {
            .mat-header-row {
              padding: 0 12px;
            }

            .mat-row {
              padding: 8px 12px;
            }

            .mat-column-select,
            .mat-column-sl_no,
            .mat-column-actions {
              min-width: unset;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 600px) {
  .list-staff-container {
    padding: 12px;

    .content-wrapper {
      padding: 12px;

      .list-section {
        .action-buttons {
          flex-direction: column;
          gap: 8px;

          button {
            width: 100%;
          }
        }
      }
    }
  }

  .mat-table {
    border: 0;
    vertical-align: middle;

    caption {
      font-size: 1em;
    }
  }
}

// Loading overlay
.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Empty state
.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 16px;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}

// Checkbox styles
:host ::ng-deep {
  .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #1976d2;
  }
}

// Tooltip styles
::ng-deep .mat-tooltip {
  font-size: 14px !important;
  background-color: rgba(97, 97, 97, 0.9);
}