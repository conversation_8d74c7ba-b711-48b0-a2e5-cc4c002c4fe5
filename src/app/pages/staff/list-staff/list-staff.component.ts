import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClientModule } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { StaffService } from '../../../services/staff.service';
import { Staff } from '../../../services/staff.service';
import { DeleteConfirmationDialogComponent } from '../../../components/deleteconfirmationdialog/deleteconfirmationdialog.component';
import { ViewStaffDialogComponent } from '../../../components/view-staff-dialog/view-staff-dialog.component';

@Component({
  selector: 'app-list-staff',
  templateUrl: './list-staff.component.html',
  styleUrls: ['./list-staff.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule,
    RouterModule,
    MatDialogModule,
  ],
})
export class ListStaffComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'sl_no',
    'employeeId',
    'fullName',
    'department_name',
    'designation_name',
    'employment_type_name',
    'employmentStatus',
    'isActive',
    'actions',
  ];
  dataSource = new MatTableDataSource<Staff>([]);
  selection = new SelectionModel<Staff>(true, []);

  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    this.dataSource.paginator = paginator;
  }

  @ViewChild(MatSort) set sort(sort: MatSort) {
    this.dataSource.sort = sort;
  }

  constructor(
    private staffService: StaffService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  // list-staff.component.ts
  viewStaffDetails(staff: any): void {
    // Make sure we're passing the staff ID correctly
    const staffId = staff.id; // Get the actual ID from the staff object

    this.staffService.getStaffById(staffId).subscribe({
      next: (response) => {
        if (response.success) {
          this.dialog.open(ViewStaffDialogComponent, {
            data: response.staff,
            width: '80%',
            maxWidth: '1200px',
            height: '80vh',
            panelClass: 'custom-dialog-container',
          });
        }
      },
      error: (error) => {
        console.error('Error fetching staff details:', error);
        this.snackBar.open('Error fetching staff details', 'Close', {
          duration: 3000,
        });
      },
    });
  }
  ngOnInit() {
    this.fetchStaffData();
    this.setupFilter();
  }

  private setupFilter() {
    // Custom filter predicate for the data source
    this.dataSource.filterPredicate = (data: Staff, filter: string) => {
      const searchStr = filter.toLowerCase();

      // Create a single string of all searchable fields
      const searchableString = [
        data.employeeId,
        data.firstName,
        data.lastName,
        `${data.firstName} ${data.lastName}`, // Full name
        data.department_name,
        data.designation_name,
        data.employment_type_name,
        data.employmentStatus,
      ]
        .join(' ')
        .toLowerCase();

      // Check if any part of the searchable string includes the filter
      return searchableString.includes(searchStr);
    };
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addStaff() {
    this.router.navigate(['/site/staffs/add']);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  viewStaff(staff: Staff) {
    console.log('View staff:', staff);
    // Implement view logic
    // this.router.navigate(['/site/staffs/view', staff.id]);
  }

  editStaff(staff: Staff) {
    this.router.navigate(['/site/staffs/edit', staff.id]);
  }

  toggleStatus(staff: Staff) {
    this.staffService.toggleStaffStatus(staff.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.fetchStaffData();
          this.snackBar.open('Staff status updated successfully', 'Close', {
            duration: 2000,
          });
        }
      },
      error: (error) => {
        console.error('Error toggling staff status:', error);
        this.snackBar.open('Error updating staff status', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  openDeleteConfirmationDialog(): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '300px',
      data: { count: this.selection.selected.length },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteSelected();
      }
    });
  }

  deleteSelected() {
    const selectedStaff = this.selection.selected;
    if (selectedStaff.length > 0) {
      const staffIds = selectedStaff.map((staff) => staff.id);
      this.staffService.deleteStaffs(staffIds).subscribe({
        next: (response) => {
          if (response.success) {
            this.selection.clear();
            this.fetchStaffData();
            this.snackBar.open('Staff deleted successfully', 'Close', {
              duration: 2000,
            });
          } else {
            console.error('Failed to delete staff:', response.message);
            this.snackBar.open('Error deleting staff', 'Close', {
              duration: 2000,
            });
          }
        },
        error: (error) => {
          console.error('Error deleting staff:', error);
          this.snackBar.open('Error deleting staff', 'Close', {
            duration: 2000,
          });
        },
      });
    }
  }

  private fetchStaffData() {
    this.staffService.getAllStaff().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.staff.map(
            (staff: Staff, index: number) => ({
              ...staff,
              sl_no: index + 1,
            })
          );
        } else {
          console.error('Failed to fetch staff:', response.message);
        }
      },
      error: (error) => {
        console.error('Error fetching staff:', error);
        this.snackBar.open('Error fetching staff data', 'Close', {
          duration: 2000,
        });
      },
    });
  }

  getSerialNumber(index: number): number {
    if (this.dataSource.paginator) {
      return (
        this.dataSource.paginator.pageIndex *
          this.dataSource.paginator.pageSize +
        index +
        1
      );
    }
    return index + 1;
  }

  getStatusClass(status: string): string {
    const statusMap: { [key: string]: string } = {
      Active: 'status-badge active',
      'On Leave': 'status-badge on-leave',
      Terminated: 'status-badge terminated',
    };
    return statusMap[status] || '';
  }
}
