.users-list-container {
  padding: 20px;

  .component-header {
    margin-bottom: 20px;

    .component-title {
      font-size: 24px;
      margin: 0;
      color: #333;
    }
  }

  .content-wrapper {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .actions-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      gap: 20px;

      .search-field {
        flex: 1;
        max-width: 400px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .table-container {
      overflow-x: auto;

      table {
        width: 100%;

        .mat-column-select {
          width: 48px;
          padding-left: 8px;
        }

        .mat-column-actions {
          width: 100px;
          text-align: center;
        }

        .mat-column-is_active {
          width: 80px;
          text-align: center;

          .status-icon {
            cursor: pointer;
            
            &.active {
              color: #4CAF50;
            }

            &:not(.active) {
              color: #f44336;
            }
          }
        }

        th.mat-header-cell {
          background: #fafafa;
          padding: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.87);
        }

        td.mat-cell {
          padding: 16px;
        }
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 768px) {
  .users-list-container {
    .content-wrapper {
      .actions-row {
        flex-direction: column;
        align-items: stretch;

        .search-field {
          max-width: none;
        }

        .action-buttons {
          flex-direction: column;
        }
      }
    }
  }
}