<div class="users-list-container">
  <div class="component-header">
    <h2>Users List</h2>
  </div>

  <div class="content-wrapper">
    <div class="actions-row">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Type to search">
        <mat-icon matPrefix>search</mat-icon>
      </mat-form-field>

      <div class="action-buttons">
        <button mat-button color="warn" (click)="deleteSelectedUsers()" [disabled]="!selection.hasValue()">
          <mat-icon>delete</mat-icon>
          Delete Selected
        </button>
        <button mat-button color="primary" (click)="addUser()">
          <mat-icon>person_add</mat-icon>
          Add User
        </button>
      </div>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="dataSource" matSort>
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? toggleAllRows() : null"
                        [checked]="selection.hasValue() && isAllSelected()"
                        [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Username Column -->
        <ng-container matColumnDef="username">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Username </th>
          <td mat-cell *matCellDef="let user"> {{user.username}} </td>
        </ng-container>

        <!-- Email Column -->
        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Email </th>
          <td mat-cell *matCellDef="let user"> {{user.email}} </td>
        </ng-container>

        <!-- Mobile Column -->
        <ng-container matColumnDef="mobile_number">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Mobile </th>
          <td mat-cell *matCellDef="let user"> {{user.mobile_number}} </td>
        </ng-container>

        <!-- Role Column -->
        <ng-container matColumnDef="role_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Role </th>
          <td mat-cell *matCellDef="let user"> {{user.role_name}} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="is_active">
          <th mat-header-cell *matHeaderCellDef> Status </th>
          <td mat-cell *matCellDef="let user">
            <mat-icon [ngClass]="{'active': user.is_active}" (click)="toggleStatus(user.id)">
              {{user.is_active ? 'check_circle' : 'cancel'}}
            </mat-icon>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let user">
            <button mat-icon-button color="primary" (click)="editUser(user.id)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteUser(user.id)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of users"></mat-paginator>
    </div>
  </div>
</div>