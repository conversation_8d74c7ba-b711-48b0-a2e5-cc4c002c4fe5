import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { UserService } from '../../../services/user.service';
import { ConfirmDialogComponent } from '../../../components/confirm-dialog/confirm-dialog.component';
import { SelectionModel } from '@angular/cdk/collections';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-list-users',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
  ],
  templateUrl: './list-users.component.html',
  styleUrls: ['./list-users.component.scss'],
})
export class ListUsersComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'username',
    'email',
    'mobile_number',
    'role_name',
    'is_active',
    'actions',
  ];
  dataSource = new MatTableDataSource<any>([]);
  selection = new SelectionModel<any>(true, []);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private userService: UserService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadUsers();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadUsers() {
    this.userService.getAllUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.dataSource.data = response.users.map(
            (user: any, index: number) => ({
              ...user,
              sl_no: index + 1,
            })
          );
        }
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.snackBar.open('Error loading users', 'Close', { duration: 3000 });
      },
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deleteUser(id: number) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this user?',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.userService.deleteUser(id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('User deleted successfully', 'Close', {
                duration: 3000,
              });
              this.loadUsers();
            }
          },
          error: (error) => {
            console.error('Error deleting user:', error);
            this.snackBar.open('Error deleting user', 'Close', {
              duration: 3000,
            });
          },
        });
      }
    });
  }

  deleteSelectedUsers() {
    const selectedUsers = this.selection.selected;
    if (selectedUsers.length === 0) {
      this.snackBar.open('Please select users to delete', 'Close', {
        duration: 3000,
      });
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete ${selectedUsers.length} selected users?`,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const userIds = selectedUsers.map((user) => user.id);

        // Single API call to delete multiple users
        this.userService.deleteUsers(userIds).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Users deleted successfully', 'Close', {
                duration: 3000,
              });
              this.selection.clear();
              this.loadUsers();
            } else {
              this.snackBar.open('Error deleting users', 'Close', {
                duration: 3000,
              });
            }
          },
          error: (error) => {
            console.error('Error deleting users:', error);
            this.snackBar.open('Error deleting users', 'Close', {
              duration: 3000,
            });
          },
        });
      }
    });
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }
    this.selection.select(...this.dataSource.data);
  }

  addUser() {
    this.router.navigate(['/site/users/add']);
  }

  editUser(id: number) {
    this.router.navigate(['/site/users/edit', id]);
  }

  toggleStatus(id: number) {
    this.userService.toggleUserStatus(id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('User status updated successfully', 'Close', {
            duration: 3000,
          });
          this.loadUsers();
        }
      },
      error: (error) => {
        console.error('Error toggling user status:', error);
        this.snackBar.open('Error updating user status', 'Close', {
          duration: 3000,
        });
      },
    });
  }
}
