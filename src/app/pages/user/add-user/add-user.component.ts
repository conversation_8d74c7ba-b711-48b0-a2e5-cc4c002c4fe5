import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-add-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSlideToggleModule,
  ],
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.scss'],
})
export class AddUserComponent implements OnInit {
  userForm: FormGroup;
  roles: any[] = [];
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.userForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      role_id: ['', Validators.required],
      mobile_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      email: ['', [Validators.required, Validators.email]],
      is_active: [true],
    });
  }

  ngOnInit() {
    this.loadRoles();
  }

  loadRoles() {
    this.userService.getRoles().subscribe({
      next: (response) => {
        if (response.success) {
          this.roles = response.roles;
        }
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.snackBar.open('Error loading roles', 'Close', { duration: 3000 });
      },
    });
  }

  onSubmit() {
    if (this.userForm.valid) {
      this.isLoading = true;
      this.userService.createUser(this.userForm.value).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('User created successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['/site/users/list']);
          }
        },
        error: (error) => {
          console.error('Error creating user:', error);
          this.snackBar.open(
            error.error?.message || 'Error creating user',
            'Close',
            { duration: 3000 }
          );
        },
        complete: () => {
          this.isLoading = false;
        },
      });
    } else {
      this.snackBar.open('Please fill all required fields correctly', 'Close', {
        duration: 3000,
      });
    }
  }

  onCancel() {
    this.router.navigate(['/site/users/list']);
  }
}
