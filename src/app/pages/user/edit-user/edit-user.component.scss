.edit-user-container {
    padding: 20px;
  
    .component-header {
      margin-bottom: 20px;
  
      .component-title {
        font-size: 24px;
        margin: 0;
        color: #333;
      }
    }
  
    .content-wrapper {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
      form {
        display: flex;
        flex-direction: column;
        gap: 20px;
  
        .form-row {
          display: flex;
          gap: 20px;
          align-items: flex-start;
  
          mat-form-field {
            flex: 1;
          }
  
          .toggle-field {
            flex: 1;
            display: flex;
            align-items: center;
            min-height: 56px;
            padding-top: 4px;
          }
        }
  
        .form-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
          margin-top: 20px;
  
          button {
            min-width: 100px;
          }
        }
      }
    }
  }
  
  // Responsive styles
  @media screen and (max-width: 768px) {
    .edit-user-container {
      .content-wrapper {
        form {
          .form-row {
            flex-direction: column;
            gap: 0;
  
            mat-form-field {
              width: 100%;
            }
  
            .toggle-field {
              margin-top: 16px;
            }
          }
        }
      }
    }
  }