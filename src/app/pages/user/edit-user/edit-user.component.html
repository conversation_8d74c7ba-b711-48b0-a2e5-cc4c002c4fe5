<div class="edit-user-container">
    <div class="component-header">
      <h2 class="component-title">Edit User</h2>
    </div>
  
    <div class="content-wrapper">
      <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Username</mat-label>
            <input matInput formControlName="username" placeholder="Enter username">
            @if (userForm.get('username')?.hasError('required') && userForm.get('username')?.touched) {
              <mat-error>Username is required</mat-error>
            }
            @if (userForm.get('username')?.hasError('minlength')) {
              <mat-error>Username must be at least 3 characters</mat-error>
            }
          </mat-form-field>
  
          <mat-form-field appearance="outline">
            <mat-label>Role</mat-label>
            <mat-select formControlName="role_id">
              @for (role of roles; track role.id) {
                <mat-option [value]="role.id">{{role.name}}</mat-option>
              }
            </mat-select>
            @if (userForm.get('role_id')?.hasError('required') && userForm.get('role_id')?.touched) {
              <mat-error>Role is required</mat-error>
            }
          </mat-form-field>
        </div>
  
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Mobile Number</mat-label>
            <input matInput formControlName="mobile_number" placeholder="Enter mobile number">
            @if (userForm.get('mobile_number')?.hasError('required') && userForm.get('mobile_number')?.touched) {
              <mat-error>Mobile number is required</mat-error>
            }
            @if (userForm.get('mobile_number')?.hasError('pattern')) {
              <mat-error>Enter a valid 10-digit mobile number</mat-error>
            }
          </mat-form-field>
  
          <mat-form-field appearance="outline">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" placeholder="Enter email">
            @if (userForm.get('email')?.hasError('required') && userForm.get('email')?.touched) {
              <mat-error>Email is required</mat-error>
            }
            @if (userForm.get('email')?.hasError('email')) {
              <mat-error>Enter a valid email address</mat-error>
            }
          </mat-form-field>
        </div>
  
        <div class="form-row">
          <div class="toggle-field">
            <mat-slide-toggle formControlName="is_active">Active</mat-slide-toggle>
          </div>
        </div>
  
        <div class="form-actions">
          <button mat-button type="button" (click)="onCancel()">
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="isLoading || !userForm.valid">
            Save Changes
          </button>
        </div>
      </form>
    </div>
  </div>