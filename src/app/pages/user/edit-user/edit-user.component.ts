import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatSlideToggleModule,
  ],
  templateUrl: './edit-user.component.html',
  styleUrls: ['./edit-user.component.scss'],
})
export class EditUserComponent implements OnInit {
  userForm: FormGroup;
  roles: any[] = [];
  userId: number;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.userForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      mobile_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      role_id: ['', Validators.required],
      is_active: [true],
    });

    this.userId = Number(this.route.snapshot.params['id']);
  }

  ngOnInit() {
    this.loadRoles();
    if (this.userId) {
      this.loadUserData();
    }
  }

  loadRoles() {
    this.userService.getRoles().subscribe({
      next: (response) => {
        if (response.success) {
          this.roles = response.roles;
        }
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.snackBar.open('Error loading roles', 'Close', { duration: 3000 });
      },
    });
  }

  loadUserData() {
    this.isLoading = true;
    this.userService.getUserById(this.userId).subscribe({
      next: (response) => {
        if (response.success) {
          const user = response.user;
          this.userForm.patchValue({
            username: user.username,
            email: user.email,
            mobile_number: user.mobile_number,
            role_id: user.role_id,
            is_active: user.is_active,
          });
        }
      },
      error: (error) => {
        console.error('Error loading user:', error);
        this.snackBar.open('Error loading user data', 'Close', {
          duration: 3000,
        });
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  onSubmit() {
    if (this.userForm.valid) {
      this.isLoading = true;
      this.userService.updateUser(this.userId, this.userForm.value).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('User updated successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['/site/users/list']);
          }
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.snackBar.open(
            error.error?.message || 'Error updating user',
            'Close',
            { duration: 3000 }
          );
        },
        complete: () => {
          this.isLoading = false;
        },
      });
    } else {
      this.snackBar.open('Please fill all required fields correctly', 'Close', {
        duration: 3000,
      });
    }
  }

  onCancel() {
    this.router.navigate(['/site/users/list']);
  }
}
