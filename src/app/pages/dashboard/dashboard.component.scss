.dashboard-container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
    position: relative;
  }
  
  .dashboard-header {
    margin-bottom: 24px;
    h1 {
      margin: 0;
      font-size: 28px;
      color: #333;
      font-weight: 500;
    }
  }
  
  .stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    background: white;
    border-radius: 8px;
    
    mat-card-content {
      display: flex;
      align-items: center;
      padding: 24px;
      gap: 20px;
    }
  
    .stat-icon {
      padding: 16px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
  
      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }
  
    .stat-info {
      h3 {
        margin: 0;
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
  
      .stat-number {
        margin: 8px 0;
        font-size: 28px;
        font-weight: 500;
        color: #333;
      }
  
      .stat-detail {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 24px;
  
    h2 {
      margin: 0 0 16px 0;
      font-size: 20px;
      color: #333;
      font-weight: 500;
    }
  
    .action-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
  
      button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        
        mat-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  .stats-details {
    mat-card {
      background: white;
      border-radius: 8px;
    }
  
    mat-card-header {
      padding: 16px;
      border-bottom: 1px solid #eee;
  
      mat-card-title {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin: 0;
      }
    }
  
    .distribution-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 24px;
      padding: 24px;
  
      .distribution-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
  
        .label {
          font-size: 14px;
          color: #666;
        }
  
        .value {
          font-size: 24px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
  
  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  @media (max-width: 768px) {
    .dashboard-container {
      padding: 16px;
    }
  
    .stats-row {
      grid-template-columns: 1fr;
    }
  
    .quick-actions .action-buttons {
      flex-direction: column;
      
      button {
        width: 100%;
      }
    }
  
    .stats-details .distribution-grid {
      grid-template-columns: 1fr 1fr;
      padding: 16px;
      gap: 16px;
    }
  }
  
  @media (max-width: 480px) {
    .stats-details .distribution-grid {
      grid-template-columns: 1fr;
    }
  }