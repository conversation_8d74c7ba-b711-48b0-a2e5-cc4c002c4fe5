// src/app/pages/dashboard/dashboard.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { MasterserviceService } from '../../services/masterservice.service';
import { StaffService } from '../../services/staff.service';
import { AttendanceService } from '../../services/attendance.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit, OnDestroy {
  metrics = {
    totalStaff: 0,
    activeStaff: 0,
    onLeave: 0,
    totalStores: 0,
    activeStores: 0,
    todayPresent: 0,
    todayTotal: 0,
    totalDepartments: 0,
    totalDesignations: 0,
  };

  isLoading = true;
  error = '';

  private destroy$ = new Subject<void>();

  constructor(
    private masterService: MasterserviceService,
    private staffService: StaffService,
    private attendanceService: AttendanceService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadDashboardData(): void {
    this.isLoading = true;

    // Create an observable for each service call
    const departmentsCall = this.masterService.getAllDepartments();
    const designationsCall = this.masterService.getAllDesignations();
    const storesCall = this.masterService.getAllStores();
    const staffCall = this.staffService.getAllStaff();
    // const attendanceCall = this.attendanceService.getTodayAttendance();

    // Subscribe to all calls at once
    forkJoin({
      departments: departmentsCall,
      designations: designationsCall,
      stores: storesCall,
      staff: staffCall,
      // attendance: attendanceCall
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (results) => {
          // Process departments
          if (results.departments.success) {
            this.metrics.totalDepartments =
              results.departments.departments?.length || 0;
          }

          // Process designations
          if (results.designations.success) {
            this.metrics.totalDesignations =
              results.designations.designations?.length || 0;
          }

          // Process stores
          if (results.stores.success) {
            this.metrics.totalStores = results.stores.stores?.length || 0;
            this.metrics.activeStores =
              results.stores.stores?.filter((store: any) => store.is_active)
                .length || 0;
          }

          // Process staff
          if (results.staff.success) {
            this.metrics.totalStaff = results.staff.staff?.length || 0;
            this.metrics.activeStaff =
              results.staff.staff?.filter(
                (staff: any) =>
                  staff.isActive && staff.employmentStatus === 'Active'
              ).length || 0;
            this.metrics.onLeave =
              results.staff.staff?.filter(
                (staff: any) =>
                  staff.isActive && staff.employmentStatus === 'On Leave'
              ).length || 0;
          }

          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
          this.error = 'Failed to load dashboard data. Please try again.';
          this.isLoading = false;
        },
      });
  }

  getAttendancePercentage(): number {
    if (this.metrics.todayTotal === 0) return 0;
    return Math.round(
      (this.metrics.todayPresent / this.metrics.todayTotal) * 100
    );
  }

  goToMarkAttendance(): void {
    this.router.navigate(['/site/attendance/add']);
  }

  goToAddStaff(): void {
    this.router.navigate(['/site/staffs/add']);
  }

  goToAttendanceList(): void {
    this.router.navigate(['/site/attendance/list']);
  }
}
