<div class="dashboard-container">
    <div class="dashboard-header">
      <h1>Dashboard</h1>
    </div>
  
    <div class="stats-row">
      <!-- Staff Stats -->
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon" style="background-color: rgba(25, 118, 210, 0.1)">
            <mat-icon style="color: #1976d2">people</mat-icon>
          </div>
          <div class="stat-info">
            <h3>Total Staff</h3>
            <p class="stat-number">{{metrics.totalStaff}}</p>
            <span class="stat-detail">{{metrics.activeStaff}} Active</span>
          </div>
        </mat-card-content>
      </mat-card>
  
      <!-- Store Stats -->
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon" style="background-color: rgba(76, 175, 80, 0.1)">
            <mat-icon style="color: #4CAF50">store</mat-icon>
          </div>
          <div class="stat-info">
            <h3>Total Stores</h3>
            <p class="stat-number">{{metrics.totalStores}}</p>
            <span class="stat-detail">{{metrics.activeStores}} Active</span>
          </div>
        </mat-card-content>
      </mat-card>
  
      <!-- Today's Attendance -->
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon" style="background-color: rgba(244, 67, 54, 0.1)">
            <mat-icon style="color: #f44336">event_available</mat-icon>
          </div>
          <div class="stat-info">
            <h3>Today's Attendance</h3>
            <p class="stat-number">{{metrics.todayPresent}} / {{metrics.todayTotal}}</p>
            <span class="stat-detail">{{getAttendancePercentage()}}% Present</span>
          </div>
        </mat-card-content>
      </mat-card>
  
      <!-- Department Stats -->
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon" style="background-color: rgba(156, 39, 176, 0.1)">
            <mat-icon style="color: #9c27b0">business</mat-icon>
          </div>
          <div class="stat-info">
            <h3>Departments</h3>
            <p class="stat-number">{{metrics.totalDepartments}}</p>
            <span class="stat-detail">Active Departments</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  
    <div class="quick-actions">
      <h2>Quick Actions</h2>
      <div class="action-buttons">
        <button mat-raised-button color="primary" (click)="goToMarkAttendance()">
          <mat-icon>assignment</mat-icon>
          Mark Attendance
        </button>
        <button mat-raised-button color="accent" (click)="goToAddStaff()">
          <mat-icon>person_add</mat-icon>
          Add Staff
        </button>
        <button mat-raised-button color="warn" (click)="goToAttendanceList()">
          <mat-icon>list</mat-icon>
          View Attendance
        </button>
      </div>
    </div>
  
    <div class="stats-details">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Staff Distribution</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="distribution-grid">
            <div class="distribution-item">
              <span class="label">Total Staff</span>
              <span class="value">{{metrics.totalStaff}}</span>
            </div>
            <div class="distribution-item">
              <span class="label">Active Staff</span>
              <span class="value">{{metrics.activeStaff}}</span>
            </div>
            <div class="distribution-item">
              <span class="label">On Leave</span>
              <span class="value">{{metrics.onLeave}}</span>
            </div>
            <div class="distribution-item">
              <span class="label">Departments</span>
              <span class="value">{{metrics.totalDepartments}}</span>
            </div>
            <div class="distribution-item">
              <span class="label">Designations</span>
              <span class="value">{{metrics.totalDesignations}}</span>
            </div>
            <div class="distribution-item">
              <span class="label">Stores</span>
              <span class="value">{{metrics.totalStores}}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  
    <mat-progress-spinner 
      *ngIf="isLoading" 
      mode="indeterminate" 
      diameter="40"
      class="loading-spinner">
    </mat-progress-spinner>
  </div>