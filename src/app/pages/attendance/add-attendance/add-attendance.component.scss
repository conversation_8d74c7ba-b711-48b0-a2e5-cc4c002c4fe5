.add-attendance-container {
    padding: 20px;
  
    .component-header {
      margin-bottom: 20px;
      h2 {
        margin: 0;
        font-size: 24px;
        color: #333;
      }
    }
  
    .content-wrapper {
      padding: 24px;
  
      .filters-section {
        display: flex;
        gap: 20px;
        margin-bottom: 24px;
  
        .store-select, .date-select {
          flex: 1;
        }
      }
  
      .staff-list-container {
        margin-bottom: 24px;
        overflow-x: auto;
  
        .staff-table {
          width: 100%;
  
          .mat-column-name {
            width: 25%;
            padding: 0 12px;
          }
  
          .mat-column-status {
            width: 35%;
            
            .status-group {
              display: flex;
              gap: 16px;
            }
          }
  
          .mat-column-reason {
            width: 40%;
  
            mat-form-field {
              width: 100%;
            }
          }
  
          th.mat-header-cell {
            background-color: #f5f5f5;
            padding: 12px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
          }
  
          td.mat-cell {
            padding: 12px;
          }
        }
      }
  
      .no-staff-message {
        text-align: center;
        padding: 40px;
        color: #666;
  
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
  
        p {
          margin: 0;
          font-size: 16px;
        }
      }
  
      .actions-section {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #eee;
      }
    }
  }
  
  // Responsive styles
  @media (max-width: 768px) {
    .add-attendance-container {
      .content-wrapper {
        .filters-section {
          flex-direction: column;
        }
  
        .staff-table {
          .status-group {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  }