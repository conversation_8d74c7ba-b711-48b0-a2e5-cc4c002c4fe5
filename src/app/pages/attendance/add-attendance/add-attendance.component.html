<div class="add-attendance-container">
    <div class="component-header">
      <h2>Mark Attendance</h2>
    </div>
  
    <mat-card class="content-wrapper">
      <!-- Filter Section -->
      <div class="filters-section">
        <mat-form-field appearance="outline" class="store-select">
          <mat-label>Select Store</mat-label>
          <mat-select [(ngModel)]="selectedStore" (selectionChange)="onStoreChange()" name="store">
            <mat-option *ngFor="let store of stores" [value]="store.id">
              {{store.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
  
        <mat-form-field appearance="outline" class="date-select">
          <mat-label>Select Date</mat-label>
          <input matInput [matDatepicker]="picker" [(ngModel)]="selectedDate" 
                 [max]="dateService.getCurrentISTDate()" (dateChange)="loadStoreStaff()" name="date">
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </div>
  
      <!-- Staff List Table -->
      <div class="staff-list-container mat-elevation-z2" *ngIf="storeStaff.length > 0">
        <table mat-table [dataSource]="storeStaff">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Staff Name</th>
            <td mat-cell *matCellDef="let staff">{{staff.firstName}} {{staff.lastName}}</td>
          </ng-container>
  
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let staff">
              <mat-radio-group [(ngModel)]="staff.status" class="status-group" [name]="'status_' + staff.staff_id">
                <mat-radio-button value="present">Present</mat-radio-button>
                <mat-radio-button value="absent">Absent</mat-radio-button>
              </mat-radio-group>
            </td>
          </ng-container>
  
          <!-- Reason Column -->
          <ng-container matColumnDef="reason">
            <th mat-header-cell *matHeaderCellDef>Reason</th>
            <td mat-cell *matCellDef="let staff">
              <mat-form-field appearance="outline" *ngIf="staff.status === 'absent'">
                <input matInput [(ngModel)]="staff.absence_reason" 
                       [name]="'reason_' + staff.staff_id"
                       placeholder="Enter reason">
              </mat-form-field>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
  
      <!-- Loading Spinner -->
      <div class="loading-spinner-container" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
  
      <!-- No Staff Message -->
      <div class="no-staff-message" *ngIf="selectedStore && storeStaff.length === 0 && !isLoading">
        <mat-icon>people_outline</mat-icon>
        <p>No staff members found for this store</p>
      </div>
  
      <!-- Actions -->
      <div class="actions-section">
        <button mat-button (click)="onCancel()">
          Cancel
        </button>
        <button mat-raised-button color="primary" 
                [disabled]="isLoading || !selectedStore || storeStaff.length === 0"
                (click)="onSubmit()">
          {{isLoading ? 'Marking...' : 'Mark Attendance'}}
        </button>
      </div>
    </mat-card>
  </div>