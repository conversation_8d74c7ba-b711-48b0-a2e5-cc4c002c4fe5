import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { AttendanceService } from '../../../services/attendance.service';
import { MasterserviceService } from '../../../services/masterservice.service';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DateService } from '../../../services/date.service';

@Component({
  selector: 'app-add-attendance',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatTableModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './add-attendance.component.html',
  styleUrls: ['./add-attendance.component.scss'],
})
export class AddAttendanceComponent implements OnInit {
  stores: any[] = [];
  selectedStore!: number; // Use definite assignment assertion

  selectedDate: Date = new Date();
  storeStaff: any[] = [];
  isLoading = false;
  displayedColumns: string[] = ['name', 'status', 'reason'];

  constructor(
    private attendanceService: AttendanceService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router,
    public dateService: DateService
  ) {}

  ngOnInit() {
    this.loadStores();
  }

  loadStores() {
    this.masterService.getAllStores().subscribe({
      next: (response) => {
        if (response.success) {
          this.stores = response.stores;
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading stores', 'Close', { duration: 3000 });
      },
    });
  }

  onStoreChange() {
    if (this.selectedStore) {
      this.loadStoreStaff();
    }
  }

  loadStoreStaff() {
    if (!this.selectedStore) return;

    this.isLoading = true;
    const formattedDate = this.formatDate(this.selectedDate);

    this.attendanceService
      .getStoreAttendance(this.selectedStore, formattedDate)
      .subscribe({
        next: (response) => {
          if (
            response.success &&
            response.attendance &&
            response.attendance.length > 0
          ) {
            this.storeStaff = response.attendance;
            this.snackBar.open(
              'Attendance already marked for this date',
              'Close',
              { duration: 3000 }
            );
          } else {
            this.masterService.getStoreStaff(this.selectedStore).subscribe({
              next: (staffResponse) => {
                if (staffResponse.success) {
                  this.storeStaff = staffResponse.staff.map((staff: any) => ({
                    staff_id: staff.id,
                    firstName: staff.firstName,
                    lastName: staff.lastName,
                    status: 'present',
                    absence_reason: '',
                  }));
                }
              },
              error: (error) => {
                this.snackBar.open('Error loading staff', 'Close', {
                  duration: 3000,
                });
              },
            });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open('Error loading attendance', 'Close', {
            duration: 3000,
          });
          this.isLoading = false;
        },
      });
  }

  onSubmit() {
    if (
      !this.selectedStore ||
      !this.selectedDate ||
      this.storeStaff.length === 0
    ) {
      this.snackBar.open('Please select store and date', 'Close', {
        duration: 3000,
      });
      return;
    }

    // Validate absence reasons
    const missingReasons = this.storeStaff.some(
      (staff) => staff.status === 'absent' && !staff.absence_reason.trim()
    );

    if (missingReasons) {
      this.snackBar.open(
        'Please provide reasons for all absent staff',
        'Close',
        { duration: 3000 }
      );
      return;
    }

    const attendanceData = {
      store_id: this.selectedStore,
      date: this.formatDate(this.selectedDate),
      attendance_records: this.storeStaff.map((staff) => ({
        staff_id: staff.staff_id,
        status: staff.status,
        absence_reason:
          staff.status === 'absent' ? staff.absence_reason : undefined,
      })),
    };

    this.isLoading = true;
    this.attendanceService.markAttendance(attendanceData).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Attendance marked successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['/site/attendance/list']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error marking attendance', 'Close', {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  onCancel() {
    this.router.navigate(['/site/attendance/list']);
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
