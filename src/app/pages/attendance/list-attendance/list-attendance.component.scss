.list-attendance-container {
    padding: 20px;
  
    .component-header {
      margin-bottom: 20px;
      
      .component-title {
        margin: 0;
        font-size: 24px;
        color: #333;
      }
    }
  
    .content-wrapper {
      .filters-section {
        margin-bottom: 20px;
  
        .filter-form {
          display: flex;
          gap: 16px;
          align-items: center;
  
          mat-form-field {
            flex: 1;
          }
        }
      }
  
      .table-container {
        position: relative;
        min-height: 200px;
        
        table {
          width: 100%;
  
          .status-present {
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
          }
  
          .status-absent {
            color: #f44336;
            background-color: rgba(244, 67, 54, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
          }
  
          th.mat-header-cell {
            background-color: #f5f5f5;
            padding: 12px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
          }
  
          td.mat-cell {
            padding: 12px;
          }
        }
      }
  
      .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: #666;
  
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
  
        p {
          font-size: 16px;
          margin: 0;
        }
      }
  
      .loading-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  
  @media (max-width: 768px) {
    .list-attendance-container {
      .content-wrapper {
        .filters-section {
          .filter-form {
            flex-direction: column;
  
            button {
              align-self: stretch;
            }
          }
        }
      }
    }
  }