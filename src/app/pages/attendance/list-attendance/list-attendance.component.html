<div class="list-attendance-container">
    <div class="component-header">
      <h2 class="component-title">Daily Attendance</h2>
    </div>
  
    <div class="content-wrapper">
      <mat-card>
        <mat-card-content>
          <div class="filters-section">
            <form [formGroup]="filterForm" class="filter-form">
              <mat-form-field appearance="outline">
                <mat-label>Select Store</mat-label>
                <mat-select formControlName="store">
                  <mat-option *ngFor="let store of stores" [value]="store.id">
                    {{store.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Select Date</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="date">
                <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
  
              <button mat-raised-button color="primary" (click)="goToAddAttendance()">
                <mat-icon>add</mat-icon>
                Mark Attendance
              </button>
            </form>
          </div>
  
          <div class="table-container">
            <table mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="slNo">
                <th mat-header-cell *matHeaderCellDef> No. </th>
                <td mat-cell *matCellDef="let element"> {{element.slNo}} </td>
              </ng-container>
  
              <ng-container matColumnDef="staffName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Staff Name </th>
                <td mat-cell *matCellDef="let element"> {{element.firstName}} {{element.lastName}} </td>
              </ng-container>
  
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
                <td mat-cell *matCellDef="let element">
                  <span [class]="getStatusClass(element.status)">
                    {{element.status | titlecase}}
                  </span>
                </td>
              </ng-container>
  
              <ng-container matColumnDef="reason">
                <th mat-header-cell *matHeaderCellDef> Reason </th>
                <td mat-cell *matCellDef="let element"> {{element.absence_reason || '-'}} </td>
              </ng-container>
  
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
  
            <div *ngIf="dataSource.data.length === 0 && !isLoading" class="no-data-message">
              <mat-icon>event_busy</mat-icon>
              <p>No attendance records found</p>
            </div>
  
            <mat-progress-spinner 
              *ngIf="isLoading" 
              mode="indeterminate" 
              diameter="40"
              class="loading-spinner">
            </mat-progress-spinner>
  
            <mat-paginator 
              [pageSizeOptions]="[5, 10, 25, 100]" 
              aria-label="Select page"
              *ngIf="dataSource.data.length > 0">
            </mat-paginator>
          </div>
  
        </mat-card-content>
      </mat-card>
    </div>
  </div>