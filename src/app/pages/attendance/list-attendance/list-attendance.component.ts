import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { AttendanceService } from '../../../services/attendance.service';
import { MasterserviceService } from '../../../services/masterservice.service';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-list-attendance',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatCardModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './list-attendance.component.html',
  styleUrls: ['./list-attendance.component.scss'],
})
export class ListAttendanceComponent implements OnInit {
  displayedColumns: string[] = ['slNo', 'staffName', 'status', 'reason'];
  dataSource = new MatTableDataSource<any>([]);
  stores: any[] = [];
  filterForm: FormGroup;
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private attendanceService: AttendanceService,
    private masterService: MasterserviceService,
    private snackBar: MatSnackBar,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      store: [''],
      date: [new Date()],
    });
  }

  ngOnInit() {
    this.loadStores();
    this.setupFilters();
  }

  private setupFilters() {
    // Watch for store selection changes
    this.filterForm.get('store')?.valueChanges.subscribe((storeId) => {
      if (storeId) {
        const date = this.formatDate(this.filterForm.get('date')?.value);
        this.loadAttendance(storeId, date);
      }
    });

    // Watch for date changes
    this.filterForm.get('date')?.valueChanges.subscribe((date) => {
      const storeId = this.filterForm.get('store')?.value;
      if (storeId && date) {
        this.loadAttendance(storeId, this.formatDate(date));
      }
    });
  }

  private loadStores() {
    this.masterService.getAllStores().subscribe({
      next: (response) => {
        if (response.success) {
          this.stores = response.stores;
        }
      },
      error: (error) => {
        console.error('Error loading stores:', error);
        this.snackBar.open('Error loading stores', 'Close', { duration: 3000 });
      },
    });
  }

  loadAttendance(storeId: number, date: string) {
    this.isLoading = true;
    console.log('Loading attendance for:', { storeId, date });

    this.attendanceService.getStoreAttendance(storeId, date).subscribe({
      next: (response) => {
        console.log('Attendance response:', response);

        if (response.success && response.attendance) {
          this.dataSource.data = response.attendance.map(
            (record: any, index: number) => ({
              ...record,
              slNo: index + 1,
            })
          );

          if (this.paginator) {
            this.dataSource.paginator = this.paginator;
          }
          if (this.sort) {
            this.dataSource.sort = this.sort;
          }
        } else {
          this.dataSource.data = [];
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading attendance:', error);
        this.snackBar.open('Error loading attendance records', 'Close', {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  getStatusClass(status: string): string {
    return status === 'present' ? 'status-present' : 'status-absent';
  }

  goToAddAttendance() {
    this.router.navigate(['/site/attendance/add']);
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }
}
