import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  FormArray,
  Validators,
  AbstractControl,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { PublicApiService } from '../../../services/public-api.service';
import { MasterserviceService } from '../../../services/masterservice.service';

import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'app-candidate-registration',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatStepperModule,
    MatSnackBarModule,
    MatCheckboxModule,
  ],
  templateUrl: './candidate-registration.component.html',
  styleUrls: ['./candidate-registration.component.scss'],
})
export class CandidateRegistrationComponent implements OnInit {
  token: string = '';
  isLoading: boolean = true;
  isSubmitting: boolean = false;
  errorMessage: string = '';
  tokenData: any = null;
  positionData: any = null;
  currentYear = new Date().getFullYear();

  // Form groups for the stepper
  personalInfoForm!: FormGroup;
  contactInfoForm!: FormGroup;
  educationForm!: FormGroup;
  experienceForm!: FormGroup;
  familyInfoForm!: FormGroup;
  documentsForm!: FormGroup;

  // File upload variables
  resumeFile: File | null = null;
  profilePicture: File | null = null;
  resumeFileName: string = '';
  profilePictureFileName: string = '';

  // Reference to the stepper
  @ViewChild('stepper') stepper!: MatStepper;

  // Dropdown data from master tables
  bloodGroups: any[] = [];
  genders: any[] = [];
  religions: any[] = [];
  communities: any[] = [];
  states: any[] = [];
  cities: any[] = [];
  educationLevels = [
    'High School',
    'Diploma',
    "Bachelor's Degree",
    "Master's Degree",
    'Ph.D.',
    'Other',
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private publicApiService: PublicApiService,
    private masterService: MasterserviceService
  ) {}

  ngOnInit(): void {
    this.initForms();

    this.route.params.subscribe((params) => {
      this.token = params['token'];
      this.validateToken();
    });

    // Load master data for dropdowns
    this.loadMasterData();
  }

  loadMasterData(): void {
    this.isLoading = true;

    // Create an array of observables for all the API calls
    const masterDataObservables = [
      // Load genders - with error handling
      this.publicApiService.getAllGenders().pipe(
        catchError((error) => {
          console.error('Error loading genders:', error);
          // Return some default genders as fallback
          return of({
            success: true,
            genders: [
              { id: 1, name: 'Male' },
              { id: 2, name: 'Female' },
              { id: 3, name: 'Other' },
            ],
          });
        })
      ),

      // Load blood groups - with error handling
      this.publicApiService.getAllBloodGroups().pipe(
        catchError((error) => {
          console.error('Error loading blood groups:', error);
          // Return default blood groups as fallback
          return of({
            success: true,
            bloodGroups: [
              { id: 1, name: 'A+' },
              { id: 2, name: 'A-' },
              { id: 3, name: 'B+' },
              { id: 4, name: 'B-' },
              { id: 5, name: 'AB+' },
              { id: 6, name: 'AB-' },
              { id: 7, name: 'O+' },
              { id: 8, name: 'O-' },
            ],
          });
        })
      ),

      // Load religions - with error handling
      this.publicApiService.getAllReligions().pipe(
        catchError((error) => {
          console.error('Error loading religions:', error);
          return of({ success: true, religions: [] });
        })
      ),

      // Load communities - with error handling
      this.publicApiService.getAllCommunities().pipe(
        catchError((error) => {
          console.error('Error loading communities:', error);
          return of({ success: true, communities: [] });
        })
      ),

      // Load states - with error handling
      this.publicApiService.getAllStates().pipe(
        catchError((error) => {
          console.error('Error loading states:', error);
          return of({ success: true, states: [] });
        })
      ),
    ];

    // Execute all API calls in parallel
    forkJoin(masterDataObservables).subscribe({
      next: ([
        gendersResponse,
        bloodGroupsResponse,
        religionsResponse,
        communitiesResponse,
        statesResponse,
      ]) => {
        // Process genders
        if (gendersResponse && gendersResponse.success) {
          this.genders = gendersResponse.genders || [];
        }

        // Process blood groups
        if (bloodGroupsResponse && bloodGroupsResponse.success) {
          this.bloodGroups = bloodGroupsResponse.bloodGroups || [];
        }

        // Process religions
        if (religionsResponse && religionsResponse.success) {
          this.religions = religionsResponse.religions || [];
        }

        // Process communities
        if (communitiesResponse && communitiesResponse.success) {
          this.communities = communitiesResponse.communities || [];
        }

        // Process states
        if (statesResponse && statesResponse.success) {
          this.states = statesResponse.states || [];
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading master data:', error);
        this.isLoading = false;

        // Set default values for critical dropdowns
        if (this.genders.length === 0) {
          this.genders = [
            { id: 1, name: 'Male' },
            { id: 2, name: 'Female' },
            { id: 3, name: 'Other' },
          ];
        }

        if (this.bloodGroups.length === 0) {
          this.bloodGroups = [
            { id: 1, name: 'A+' },
            { id: 2, name: 'A-' },
            { id: 3, name: 'B+' },
            { id: 4, name: 'B-' },
            { id: 5, name: 'AB+' },
            { id: 6, name: 'AB-' },
            { id: 7, name: 'O+' },
            { id: 8, name: 'O-' },
          ];
        }
      },
    });
  }

  onStateChange(event: any): void {
    const stateId = event.value;
    if (stateId) {
      this.loadCitiesByState(stateId);
      this.contactInfoForm.get('city')?.enable();
    } else {
      this.cities = [];
      this.contactInfoForm.get('city')?.disable();
      this.contactInfoForm.get('city')?.setValue(null);
    }
  }

  loadCitiesByState(stateId: number): void {
    this.publicApiService.getCitiesByStateId(stateId).subscribe({
      next: (response) => {
        if (response && response.success) {
          this.cities = response.cities || [];
        } else {
          this.cities = [];
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.cities = [];
      },
    });
  }

  initForms(): void {
    // Personal Information Form
    this.personalInfoForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      dateOfBirth: ['', [Validators.required]],
      genderId: ['', [Validators.required]],
      bloodGroupId: [''],
    });

    // Contact Information Form
    this.contactInfoForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      alternatePhone: ['', [Validators.pattern('^[0-9]{10}$')]],
      emergencyContactName: [''],
      emergencyContactNumber: ['', [Validators.pattern('^[0-9]{10}$')]],
      address: ['', [Validators.required]],
      permanentAddress: [''],
      city: [{ value: '', disabled: true }],
      state: [''],
      postalCode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
      idProofType: [''],
      idProofNumber: [''],
    });

    // Education Form
    this.educationForm = this.fb.group({
      educationLevel: ['', [Validators.required]],
      degrees: ['', [Validators.required]],
      university: ['', [Validators.required]],
      yearOfPassing: [
        '',
        [
          Validators.required,
          Validators.min(1950),
          Validators.max(this.currentYear),
        ],
      ],
      specialization: [''],
      additionalCertifications: [''],
    });

    // Experience Form
    this.experienceForm = this.fb.group({
      totalExperience: ['', [Validators.required, Validators.min(0)]],
      currentOrganization: [''],
      currentDesignation: [''],
      currentSalary: ['', [Validators.min(0)]],
      expectedSalary: ['', [Validators.required, Validators.min(0)]],
      noticePeriod: ['', [Validators.min(0)]],
      reasonForChange: [''],
      skills: ['', [Validators.required]],
      referenceContacts: [''],
      portfolioLinks: [''],
    });

    // Family Information Form
    this.familyInfoForm = this.fb.group({
      religion: [''],
      community: [''],
      father_name: [''],
      father_occupation: [''],
      father_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      father_status: ['Living'],
      mother_name: [''],
      mother_occupation: [''],
      mother_contact: ['', [Validators.pattern('^[0-9]{10}$')]],
      mother_status: ['Living'],
      family_address: [''],
      siblings: this.fb.array([]),
    });

    // Documents Form
    this.documentsForm = this.fb.group({
      resumeUploaded: [false, [Validators.requiredTrue]],
    });
  }

  // Getter for siblings form array
  get siblings(): FormArray {
    return this.familyInfoForm.get('siblings') as FormArray;
  }

  // Get a control from a sibling form group
  getSiblingControl(
    index: number,
    controlName: string
  ): AbstractControl | null {
    return this.siblings.at(index).get(controlName);
  }

  // Add a new sibling to the form array
  addSibling(): void {
    this.siblings.push(
      this.fb.group({
        name: ['', [Validators.required]],
        date_of_birth: [null, [Validators.required]],
        gender: [''],
        occupation: [''],
        marital_status: ['Single'],
        contact: ['', [Validators.pattern('^[0-9]{10}$')]],
        status: ['Living'],
        is_emergency_contact: [false],
        additional_info: [''],
      })
    );
  }

  // Remove a sibling from the form array
  removeSibling(index: number): void {
    this.siblings.removeAt(index);
  }

  validateToken(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.publicApiService.validateToken(this.token).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.tokenData = response.token;
          this.positionData = response.position;

          // Pre-fill email if provided in token
          if (this.tokenData.email) {
            this.contactInfoForm.patchValue({
              email: this.tokenData.email,
            });
          }
        } else {
          this.errorMessage = response.message || 'Invalid registration token';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage =
          error.error?.message || 'Error validating token. Please try again.';
        console.error('Token validation error:', error);
      },
    });
  }

  // Handle resume file selection
  onResumeFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open('Please upload a PDF or Word document', 'Close', {
          duration: 3000,
        });
        return;
      }

      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        this.snackBar.open('File size should not exceed 5MB', 'Close', {
          duration: 3000,
        });
        return;
      }

      this.resumeFile = file;
      this.resumeFileName = file.name;

      // Mark resume as uploaded in the form
      this.documentsForm.patchValue({
        resumeUploaded: true,
      });
      this.documentsForm.get('resumeUploaded')?.markAsDirty();
      this.documentsForm.get('resumeUploaded')?.updateValueAndValidity();
    }
  }

  // Handle profile picture selection
  onProfilePictureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open('Please upload a JPG, PNG, or GIF image', 'Close', {
          duration: 3000,
        });
        return;
      }

      // Check file size (3MB max)
      if (file.size > 3 * 1024 * 1024) {
        this.snackBar.open('File size should not exceed 3MB', 'Close', {
          duration: 3000,
        });
        return;
      }

      this.profilePicture = file;
      this.profilePictureFileName = file.name;
    }
  }

  // Format date to YYYY-MM-DD
  formatDate(date: Date | null | string): string {
    if (!date) return '';

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  // Submit the registration form
  submitRegistration(): void {
    if (
      this.personalInfoForm.invalid ||
      this.contactInfoForm.invalid ||
      this.educationForm.invalid ||
      this.experienceForm.invalid ||
      this.documentsForm.invalid ||
      !this.resumeFile
    ) {
      // Find which form is invalid for better error messaging
      let invalidForms = [];
      if (this.personalInfoForm.invalid)
        invalidForms.push('Personal Information');
      if (this.contactInfoForm.invalid)
        invalidForms.push('Contact Information');
      if (this.educationForm.invalid) invalidForms.push('Education');
      if (this.experienceForm.invalid) invalidForms.push('Experience');
      if (!this.resumeFile) invalidForms.push('Resume Upload');

      this.snackBar.open(
        `Please complete all required fields in: ${invalidForms.join(', ')}`,
        'Close',
        {
          duration: 3000,
        }
      );

      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.personalInfoForm);
      this.markFormGroupTouched(this.contactInfoForm);
      this.markFormGroupTouched(this.educationForm);
      this.markFormGroupTouched(this.experienceForm);
      this.markFormGroupTouched(this.familyInfoForm);
      this.markFormGroupTouched(this.documentsForm);
      return;
    }

    this.isSubmitting = true;

    // Create FormData object for file upload
    const formData = new FormData();

    // Personal Information
    formData.append('firstName', this.personalInfoForm.value.firstName || '');
    formData.append('lastName', this.personalInfoForm.value.lastName || '');
    formData.append(
      'dateOfBirth',
      this.formatDate(this.personalInfoForm.value.dateOfBirth)
    );
    formData.append('genderId', this.personalInfoForm.value.genderId || '');
    formData.append(
      'bloodGroupId',
      this.personalInfoForm.value.bloodGroupId || ''
    );

    // Contact Information
    formData.append('email', this.contactInfoForm.value.email || '');
    formData.append(
      'phoneNumber',
      this.contactInfoForm.value.phoneNumber || ''
    );
    formData.append(
      'alternatePhone',
      this.contactInfoForm.value.alternatePhone || ''
    );
    formData.append(
      'emergencyContactName',
      this.contactInfoForm.value.emergencyContactName || ''
    );
    formData.append(
      'emergencyContactNumber',
      this.contactInfoForm.value.emergencyContactNumber || ''
    );
    formData.append('address', this.contactInfoForm.value.address || '');
    formData.append(
      'permanentAddress',
      this.contactInfoForm.value.permanentAddress || ''
    );
    formData.append('city', this.contactInfoForm.value.city || '');
    formData.append('state', this.contactInfoForm.value.state || '');
    formData.append('postalCode', this.contactInfoForm.value.postalCode || '');
    formData.append(
      'idProofType',
      this.contactInfoForm.value.idProofType || ''
    );
    formData.append(
      'idProofNumber',
      this.contactInfoForm.value.idProofNumber || ''
    );

    // Education Information
    formData.append(
      'educationLevel',
      this.educationForm.value.educationLevel || ''
    );
    formData.append('degrees', this.educationForm.value.degrees || '');
    formData.append('university', this.educationForm.value.university || '');
    formData.append(
      'yearOfPassing',
      this.educationForm.value.yearOfPassing || ''
    );
    formData.append(
      'specialization',
      this.educationForm.value.specialization || ''
    );
    formData.append(
      'additionalCertifications',
      this.educationForm.value.additionalCertifications || ''
    );

    // Professional Information
    formData.append(
      'totalExperience',
      this.experienceForm.value.totalExperience || ''
    );
    formData.append(
      'currentOrganization',
      this.experienceForm.value.currentOrganization || ''
    );
    formData.append(
      'currentDesignation',
      this.experienceForm.value.currentDesignation || ''
    );
    formData.append(
      'currentSalary',
      this.experienceForm.value.currentSalary || ''
    );
    formData.append(
      'expectedSalary',
      this.experienceForm.value.expectedSalary || ''
    );
    formData.append(
      'noticePeriod',
      this.experienceForm.value.noticePeriod || ''
    );
    formData.append(
      'reasonForChange',
      this.experienceForm.value.reasonForChange || ''
    );
    formData.append('skills', this.experienceForm.value.skills || '');
    formData.append(
      'referenceContacts',
      this.experienceForm.value.referenceContacts || ''
    );
    formData.append(
      'portfolioLinks',
      this.experienceForm.value.portfolioLinks || ''
    );

    // Family Information
    formData.append('religion', this.familyInfoForm.value.religion || '');
    formData.append('community', this.familyInfoForm.value.community || '');
    formData.append('father_name', this.familyInfoForm.value.father_name || '');
    formData.append(
      'father_occupation',
      this.familyInfoForm.value.father_occupation || ''
    );
    formData.append(
      'father_contact',
      this.familyInfoForm.value.father_contact || ''
    );
    formData.append(
      'father_status',
      this.familyInfoForm.value.father_status || 'Living'
    );
    formData.append('mother_name', this.familyInfoForm.value.mother_name || '');
    formData.append(
      'mother_occupation',
      this.familyInfoForm.value.mother_occupation || ''
    );
    formData.append(
      'mother_contact',
      this.familyInfoForm.value.mother_contact || ''
    );
    formData.append(
      'mother_status',
      this.familyInfoForm.value.mother_status || 'Living'
    );
    formData.append(
      'family_address',
      this.familyInfoForm.value.family_address || ''
    );

    // Process siblings
    if (this.siblings.length > 0) {
      const formattedSiblings = this.siblings.controls.map((control) => {
        const sibling = control.value;
        return {
          name: sibling.name,
          date_of_birth: this.formatDate(sibling.date_of_birth),
          gender: sibling.gender || '',
          occupation: sibling.occupation || '',
          marital_status: sibling.marital_status || 'Single',
          contact: sibling.contact || '',
          status: sibling.status || 'Living',
          is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
          additional_info: sibling.additional_info || '',
        };
      });

      formData.append('siblings', JSON.stringify(formattedSiblings));
    }

    // Add files
    if (this.resumeFile) {
      formData.append('resumeFile', this.resumeFile);
    }

    if (this.profilePicture) {
      formData.append('profilePicture', this.profilePicture);
    }

    // Submit the form
    this.publicApiService.registerCandidate(this.token, formData).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        if (response.success) {
          this.router.navigate(['/register/success']);
        } else {
          this.snackBar.open(
            response.message || 'Registration failed',
            'Close',
            { duration: 5000 }
          );
        }
      },
      error: (error) => {
        this.isSubmitting = false;

        // Get detailed error message
        let errorMessage = 'Error submitting registration. Please try again.';

        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        } else if (typeof error.error === 'string') {
          errorMessage = error.error;
        }

        this.snackBar.open(errorMessage, 'Close', { duration: 10000 });
        console.error('Registration error:', error);
      },
    });
  }

  // Mark all controls in a form group as touched
  markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Fill the form with dummy data for testing
  fillWithDummyData(): void {
    // Personal Info
    this.personalInfoForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-15'),
      genderId: this.genders.length > 0 ? this.genders[0].id : 1,
      bloodGroupId: this.bloodGroups.length > 0 ? this.bloodGroups[0].id : 1,
    });

    // Contact Info
    this.contactInfoForm.patchValue({
      email: '<EMAIL>',
      phoneNumber: '9876543210',
      alternatePhone: '8765432109',
      emergencyContactName: 'Jane Doe',
      emergencyContactNumber: '7654321098',
      address: '123 Main Street, Apartment 4B',
      permanentAddress: '123 Main Street, Apartment 4B',
      state: this.states.length > 0 ? this.states[0].id : '',
      postalCode: '400001',
      idProofType: 'Aadhar',
      idProofNumber: '123456789012',
    });

    // If state is selected, load cities
    if (this.states.length > 0) {
      this.loadCitiesByState(this.states[0].id);
      setTimeout(() => {
        if (this.cities.length > 0) {
          this.contactInfoForm.get('city')?.enable();
          this.contactInfoForm.patchValue({
            city: this.cities[0].id,
          });
        }
      }, 1000);
    }

    // Education
    this.educationForm.patchValue({
      educationLevel: "Bachelor's Degree",
      degrees: 'B.Tech Computer Science',
      university: 'Mumbai University',
      yearOfPassing: '2015',
      specialization: 'Software Development',
      additionalCertifications:
        'AWS Certified Developer, Microsoft Certified Professional',
    });

    // Experience
    this.experienceForm.patchValue({
      totalExperience: '5',
      currentOrganization: 'Tech Solutions Ltd',
      currentDesignation: 'Senior Developer',
      currentSalary: '1200000',
      expectedSalary: '1500000',
      noticePeriod: '60',
      reasonForChange: 'Career growth and new challenges',
      skills: 'JavaScript, Angular, Node.js, MongoDB, Express, REST API, Git',
      referenceContacts: 'Mark Johnson, CTO, Tech Solutions, 9876543210',
      portfolioLinks:
        'https://github.com/johndoe, https://linkedin.com/in/johndoe',
    });

    // Family Info
    this.familyInfoForm.patchValue({
      religion: this.religions.length > 0 ? this.religions[0].id : '',
      community: this.communities.length > 0 ? this.communities[0].id : '',
      father_name: 'Robert Doe',
      father_occupation: 'Retired Teacher',
      father_contact: '9876543211',
      father_status: 'Living',
      mother_name: 'Mary Doe',
      mother_occupation: 'Homemaker',
      mother_contact: '9876543212',
      mother_status: 'Living',
      family_address: '123 Family Street, Mumbai, Maharashtra, 400001',
    });

    // Add a sibling
    if (this.siblings.length === 0) {
      this.addSibling();
      this.siblings.at(0).patchValue({
        name: 'Jane Doe',
        date_of_birth: new Date('1992-05-20'),
        gender: 'Female',
        occupation: 'Doctor',
        marital_status: 'Single',
        contact: '9876543213',
        status: 'Living',
        is_emergency_contact: true,
        additional_info: 'Sister',
      });
    }

    // Show notification
    this.snackBar.open('Form filled with dummy data', 'Close', {
      duration: 3000,
    });

    // Move through stepper
    if (this.stepper) {
      setTimeout(() => {
        this.stepper.next();
        setTimeout(() => {
          this.stepper.next();
          setTimeout(() => {
            this.stepper.next();
            setTimeout(() => {
              this.stepper.next();
              setTimeout(() => {
                this.stepper.next();
              }, 300);
            }, 300);
          }, 300);
        }, 300);
      }, 300);
    }
  }
}
