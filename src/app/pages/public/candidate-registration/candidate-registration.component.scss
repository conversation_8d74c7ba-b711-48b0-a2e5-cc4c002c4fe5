.registration-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.registration-card {
  width: 100%;
  max-width: 1000px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.company-logo {
  height: 40px;
  width: auto;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 0;
}

.form-container {
  margin-top: 20px;
}

// Form styling to match existing forms
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;

  &.full-width {
    flex-direction: column;
  }

  mat-form-field {
    flex: 1;
    min-width: 200px;

    &.short-field {
      flex: 0 0 150px;
    }
  }
}

.form-section {
  margin-bottom: 32px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 500;
    color: #1976d2;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;

    mat-icon {
      color: #1976d2;
      font-size: 24px;
      height: 24px;
      width: 24px;
    }
  }
}

.full-width {
  width: 100%;
}

.step-content {
  padding: 24px;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.sibling-row {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
}

.sibling-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    color: #1976d2;
  }
}

.file-upload-container {
  width: 100%;
  margin-bottom: 24px;
}

.file-input-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.file-name {
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.file-requirements {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-error {
  margin-top: 8px;
  color: #f44336;
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-note {
  margin-top: 24px;
  padding: 16px;
  background-color: #fff8e1;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  gap: 12px;

  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.7);
  }
}

.review-section {
  padding: 24px 0;
}

.review-summary {
  margin: 24px 0;
}

.review-category {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #1976d2;

  h4 {
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1976d2;
  }

  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.7);
  }
}

.review-note {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #e8f5e9;
  padding: 16px;
  border-radius: 4px;
  margin-top: 16px;
}

.consent-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #e3f2fd;
  border-radius: 4px;

  h3 {
    margin-top: 0;
    color: #1976d2;
  }

  p {
    margin-bottom: 0;
  }
}

.empty-siblings {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
}

.debug-panel {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #ff4081;
  display: flex;
  justify-content: flex-end;
}

h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

h3 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
  color: #1976d2;
}

// Custom styling for mat-stepper to match your application
::ng-deep {
  .mat-horizontal-stepper-header-container {
    background-color: #f5f5f5;
    padding: 8px 16px;
  }

  .mat-step-header .mat-step-icon-selected {
    background-color: #1976d2;
  }

  .mat-step-header .mat-step-label.mat-step-label-active {
    color: #1976d2;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #fafafa;
  }

  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
    background-color: white;
  }

  .mat-form-field-subscript-wrapper {
    margin-top: 0;
  }

  .mat-form-field-infix {
    width: auto;
    min-width: 0;
  }

  mat-datepicker-toggle {
    color: rgba(0, 0, 0, 0.54);
  }

  .mat-select-panel {
    max-height: 300px;
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .registration-card {
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .registration-container {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  mat-form-field {
    width: 100%;
  }

  .file-input-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .file-name {
      max-width: 100%;
    }
  }

  .step-actions {
    flex-direction: column-reverse;
    gap: 12px;

    button {
      width: 100%;
    }
  }
}
