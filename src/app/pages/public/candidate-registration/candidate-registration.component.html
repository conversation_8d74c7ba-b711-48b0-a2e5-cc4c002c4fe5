<div class="registration-container">
  <mat-card class="registration-card">
    <mat-card-header>
      <mat-card-title>
        <div class="title-container">
          <img src="assets/images/logo.png" alt="Company Logo" class="company-logo">
          <h1>Candidate Registration</h1>
        </div>
      </mat-card-title>
      <mat-card-subtitle *ngIf="positionData">
        Applying for: <strong>{{ positionData.title }}</strong> in <strong>{{ positionData.department_name }}</strong>
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Validating registration link...</p>
      </div>

      <div *ngIf="!isLoading && errorMessage" class="error-container">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ errorMessage }}</p>
        <button mat-button color="primary" routerLink="/register/positions">View Open Positions</button>
      </div>

      <div *ngIf="!isLoading && !errorMessage" class="form-container">
        <!-- Debug button for filling form with dummy data -->
        <div class="debug-panel">
          <button mat-raised-button color="accent" (click)="fillWithDummyData()">
            <mat-icon>data_object</mat-icon>
            Fill with Dummy Data
          </button>
        </div>

        <mat-horizontal-stepper #stepper>
          <!-- Personal Information Step -->
          <mat-step [stepControl]="personalInfoForm" label="Personal Information">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>person</mat-icon>
                  Personal Information
                </h2>
                <form [formGroup]="personalInfoForm">
                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>First Name</mat-label>
                      <input matInput formControlName="firstName" required placeholder="Enter first name">
                      <mat-icon matSuffix>person</mat-icon>
                      <mat-error *ngIf="personalInfoForm.get('firstName')?.hasError('required')">
                        First name is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Last Name</mat-label>
                      <input matInput formControlName="lastName" required placeholder="Enter last name">
                      <mat-icon matSuffix>person</mat-icon>
                      <mat-error *ngIf="personalInfoForm.get('lastName')?.hasError('required')">
                        Last name is required
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Date of Birth</mat-label>
                      <input matInput [matDatepicker]="picker" formControlName="dateOfBirth" required>
                      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                      <mat-error *ngIf="personalInfoForm.get('dateOfBirth')?.hasError('required')">
                        Date of birth is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Gender</mat-label>
                      <mat-select formControlName="genderId" required>
                        <mat-option *ngFor="let gender of genders" [value]="gender.id">
                          {{ gender.name }}
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix>wc</mat-icon>
                      <mat-error *ngIf="personalInfoForm.get('genderId')?.hasError('required')">
                        Gender is required
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Blood Group</mat-label>
                      <mat-select formControlName="bloodGroupId">
                        <mat-option *ngFor="let bloodGroup of bloodGroups" [value]="bloodGroup.id">
                          {{ bloodGroup.name }}
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix>bloodtype</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="step-actions">
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Next</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Contact Information Step -->
          <mat-step [stepControl]="contactInfoForm" label="Contact Information">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>contact_phone</mat-icon>
                  Contact Information
                </h2>
                <form [formGroup]="contactInfoForm">
                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Email</mat-label>
                      <input matInput formControlName="email" required type="email" placeholder="<EMAIL>">
                      <mat-icon matSuffix>email</mat-icon>
                      <mat-error *ngIf="contactInfoForm.get('email')?.hasError('required')">
                        Email is required
                      </mat-error>
                      <mat-error *ngIf="contactInfoForm.get('email')?.hasError('email')">
                        Please enter a valid email address
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Phone Number</mat-label>
                      <input matInput formControlName="phoneNumber" required placeholder="10-digit number">
                      <mat-icon matSuffix>phone</mat-icon>
                      <mat-error *ngIf="contactInfoForm.get('phoneNumber')?.hasError('required')">
                        Phone number is required
                      </mat-error>
                      <mat-error *ngIf="contactInfoForm.get('phoneNumber')?.hasError('pattern')">
                        Please enter a valid 10-digit phone number
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Alternate Phone</mat-label>
                      <input matInput formControlName="alternatePhone" placeholder="Optional alternate number">
                      <mat-icon matSuffix>phone_alt</mat-icon>
                      <mat-error *ngIf="contactInfoForm.get('alternatePhone')?.hasError('pattern')">
                        Please enter a valid 10-digit phone number
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Emergency Contact Name</mat-label>
                      <input matInput formControlName="emergencyContactName" placeholder="Name of emergency contact">
                      <mat-icon matSuffix>person</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Emergency Contact Number</mat-label>
                      <input matInput formControlName="emergencyContactNumber" placeholder="Emergency contact number">
                      <mat-icon matSuffix>phone</mat-icon>
                      <mat-error *ngIf="contactInfoForm.get('emergencyContactNumber')?.hasError('pattern')">
                        Please enter a valid 10-digit phone number
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-section">
                    <h3>Address Details</h3>
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Current Address</mat-label>
                        <textarea matInput formControlName="address" required rows="3" placeholder="Enter your current address"></textarea>
                        <mat-icon matSuffix>home</mat-icon>
                        <mat-error *ngIf="contactInfoForm.get('address')?.hasError('required')">
                          Address is required
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Permanent Address (if different)</mat-label>
                        <textarea matInput formControlName="permanentAddress" rows="3" placeholder="Enter your permanent address if different from current"></textarea>
                        <mat-icon matSuffix>location_city</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>State</mat-label>
                        <mat-select formControlName="state" (selectionChange)="onStateChange($event)">
                          <mat-option *ngFor="let state of states" [value]="state.id">
                            {{ state.name }}
                          </mat-option>
                        </mat-select>
                        <mat-icon matSuffix>map</mat-icon>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>City</mat-label>
                        <mat-select formControlName="city">
                          <mat-option *ngFor="let city of cities" [value]="city.id">
                            {{ city.name }}
                          </mat-option>
                        </mat-select>
                        <mat-icon matSuffix>location_on</mat-icon>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Postal Code</mat-label>
                        <input matInput formControlName="postalCode" required placeholder="Enter postal code">
                        <mat-icon matSuffix>markunread_mailbox</mat-icon>
                        <mat-error *ngIf="contactInfoForm.get('postalCode')?.hasError('required')">
                          Postal code is required
                        </mat-error>
                        <mat-error *ngIf="contactInfoForm.get('postalCode')?.hasError('pattern')">
                          Please enter a valid 6-digit postal code
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>ID Proof Type</mat-label>
                      <mat-select formControlName="idProofType">
                        <mat-option value="Aadhar">Aadhar Card</mat-option>
                        <mat-option value="PAN">PAN Card</mat-option>
                        <mat-option value="Passport">Passport</mat-option>
                        <mat-option value="Driving License">Driving License</mat-option>
                        <mat-option value="Voter ID">Voter ID</mat-option>
                      </mat-select>
                      <mat-icon matSuffix>badge</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>ID Proof Number</mat-label>
                      <input matInput formControlName="idProofNumber" placeholder="Enter ID number">
                      <mat-icon matSuffix>pin</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="step-actions">
                    <button mat-button matStepperPrevious>
                      <mat-icon>arrow_back</mat-icon>
                      <span>Back</span>
                    </button>
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Next</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Education Step -->
          <mat-step [stepControl]="educationForm" label="Education">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>school</mat-icon>
                  Education Details
                </h2>
                <form [formGroup]="educationForm">
                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Highest Education Level</mat-label>
                      <mat-select formControlName="educationLevel" required>
                        <mat-option *ngFor="let level of educationLevels" [value]="level">
                          {{ level }}
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix>grade</mat-icon>
                      <mat-error *ngIf="educationForm.get('educationLevel')?.hasError('required')">
                        Education level is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Degrees</mat-label>
                      <input matInput formControlName="degrees" required placeholder="e.g., B.Tech, MBA">
                      <mat-icon matSuffix>school</mat-icon>
                      <mat-error *ngIf="educationForm.get('degrees')?.hasError('required')">
                        Degrees are required
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>University/Institution</mat-label>
                      <input matInput formControlName="university" required placeholder="Enter university name">
                      <mat-icon matSuffix>account_balance</mat-icon>
                      <mat-error *ngIf="educationForm.get('university')?.hasError('required')">
                        University is required
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Year of Passing</mat-label>
                      <input matInput formControlName="yearOfPassing" required type="number" placeholder="e.g., 2020">
                      <mat-icon matSuffix>event</mat-icon>
                      <mat-error *ngIf="educationForm.get('yearOfPassing')?.hasError('required')">
                        Year of passing is required
                      </mat-error>
                      <mat-error *ngIf="educationForm.get('yearOfPassing')?.hasError('min') || educationForm.get('yearOfPassing')?.hasError('max')">
                        Please enter a valid year (1950-{{currentYear}})
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Specialization</mat-label>
                      <input matInput formControlName="specialization" placeholder="e.g., Computer Science">
                      <mat-icon matSuffix>psychology</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Additional Certifications</mat-label>
                      <input matInput formControlName="additionalCertifications" placeholder="e.g., AWS, PMP">
                      <mat-icon matSuffix>verified</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="step-actions">
                    <button mat-button matStepperPrevious>
                      <mat-icon>arrow_back</mat-icon>
                      <span>Back</span>
                    </button>
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Next</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Experience Step -->
          <mat-step [stepControl]="experienceForm" label="Experience">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>work</mat-icon>
                  Professional Experience
                </h2>
                <form [formGroup]="experienceForm">
                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Total Experience (years)</mat-label>
                      <input matInput formControlName="totalExperience" required type="number" step="0.1" placeholder="e.g., 5.5">
                      <mat-icon matSuffix>timeline</mat-icon>
                      <mat-error *ngIf="experienceForm.get('totalExperience')?.hasError('required')">
                        Total experience is required
                      </mat-error>
                      <mat-error *ngIf="experienceForm.get('totalExperience')?.hasError('min')">
                        Value must be 0 or greater
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Current Organization</mat-label>
                      <input matInput formControlName="currentOrganization" placeholder="Enter current company name">
                      <mat-icon matSuffix>business</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Current Designation</mat-label>
                      <input matInput formControlName="currentDesignation" placeholder="e.g., Senior Developer">
                      <mat-icon matSuffix>badge</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Current Salary (Annual)</mat-label>
                      <input matInput formControlName="currentSalary" type="number" placeholder="Annual salary in INR">
                      <mat-icon matSuffix>payments</mat-icon>
                      <mat-error *ngIf="experienceForm.get('currentSalary')?.hasError('min')">
                        Value must be 0 or greater
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Expected Salary (Annual)</mat-label>
                      <input matInput formControlName="expectedSalary" required type="number" placeholder="Expected annual salary in INR">
                      <mat-icon matSuffix>trending_up</mat-icon>
                      <mat-error *ngIf="experienceForm.get('expectedSalary')?.hasError('required')">
                        Expected salary is required
                      </mat-error>
                      <mat-error *ngIf="experienceForm.get('expectedSalary')?.hasError('min')">
                        Value must be 0 or greater
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Notice Period (days)</mat-label>
                      <input matInput formControlName="noticePeriod" type="number" placeholder="e.g., 30, 60, 90">
                      <mat-icon matSuffix>date_range</mat-icon>
                      <mat-error *ngIf="experienceForm.get('noticePeriod')?.hasError('min')">
                        Value must be 0 or greater
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-section">
                    <h3>Additional Information</h3>
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Reason for Change</mat-label>
                        <textarea matInput formControlName="reasonForChange" rows="2" placeholder="Why are you looking for a change?"></textarea>
                        <mat-icon matSuffix>question_answer</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Skills</mat-label>
                        <textarea matInput formControlName="skills" required rows="3" placeholder="e.g., JavaScript, Node.js, React, Express"></textarea>
                        <mat-icon matSuffix>code</mat-icon>
                        <mat-error *ngIf="experienceForm.get('skills')?.hasError('required')">
                          Skills are required
                        </mat-error>
                      </mat-form-field>
                    </div>
                    
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Reference Contacts</mat-label>
                        <textarea matInput formControlName="referenceContacts" rows="2" placeholder="Name, Designation, Company, Contact Number"></textarea>
                        <mat-icon matSuffix>contacts</mat-icon>
                        <mat-hint>Add multiple references separated by new lines</mat-hint>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Portfolio Links</mat-label>
                        <textarea matInput formControlName="portfolioLinks" rows="2" placeholder="LinkedIn, GitHub, personal website, etc."></textarea>
                        <mat-icon matSuffix>link</mat-icon>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="step-actions">
                    <button mat-button matStepperPrevious>
                      <mat-icon>arrow_back</mat-icon>
                      <span>Back</span>
                    </button>
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Next</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Family Information Step -->
          <mat-step [stepControl]="familyInfoForm" label="Family Information">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>family_restroom</mat-icon>
                  Family Information
                </h2>
                <form [formGroup]="familyInfoForm">
                  <div class="form-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Religion</mat-label>
                      <mat-select formControlName="religion">
                        <mat-option *ngFor="let religion of religions" [value]="religion.id">
                          {{ religion.name }}
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix>church</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Community</mat-label>
                      <mat-select formControlName="community">
                        <mat-option *ngFor="let community of communities" [value]="community.id">
                          {{ community.name }}
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix>groups</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="form-section">
                    <h3>Father's Information</h3>
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Father's Name</mat-label>
                        <input matInput formControlName="father_name" placeholder="Enter father's name">
                        <mat-icon matSuffix>person</mat-icon>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Father's Occupation</mat-label>
                        <input matInput formControlName="father_occupation" placeholder="Enter father's occupation">
                        <mat-icon matSuffix>work</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Father's Contact</mat-label>
                        <input matInput formControlName="father_contact" placeholder="Enter father's contact number">
                        <mat-icon matSuffix>phone</mat-icon>
                        <mat-error *ngIf="familyInfoForm.get('father_contact')?.hasError('pattern')">
                          Please enter a valid 10-digit number
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Father's Status</mat-label>
                        <mat-select formControlName="father_status">
                          <mat-option value="Living">Living</mat-option>
                          <mat-option value="Deceased">Deceased</mat-option>
                        </mat-select>
                        <mat-icon matSuffix>info</mat-icon>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="form-section">
                    <h3>Mother's Information</h3>
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Mother's Name</mat-label>
                        <input matInput formControlName="mother_name" placeholder="Enter mother's name">
                        <mat-icon matSuffix>person</mat-icon>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Mother's Occupation</mat-label>
                        <input matInput formControlName="mother_occupation" placeholder="Enter mother's occupation">
                        <mat-icon matSuffix>work</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Mother's Contact</mat-label>
                        <input matInput formControlName="mother_contact" placeholder="Enter mother's contact number">
                        <mat-icon matSuffix>phone</mat-icon>
                        <mat-error *ngIf="familyInfoForm.get('mother_contact')?.hasError('pattern')">
                          Please enter a valid 10-digit number
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Mother's Status</mat-label>
                        <mat-select formControlName="mother_status">
                          <mat-option value="Living">Living</mat-option>
                          <mat-option value="Deceased">Deceased</mat-option>
                        </mat-select>
                        <mat-icon matSuffix>info</mat-icon>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Family Address</mat-label>
                      <textarea matInput formControlName="family_address" rows="3" placeholder="Enter family address if different from your address"></textarea>
                      <mat-icon matSuffix>home</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="form-section">
                    <div class="section-title">
                      <h3>Siblings</h3>
                      <button mat-mini-fab color="primary" type="button" (click)="addSibling()">
                        <mat-icon>add</mat-icon>
                      </button>
                    </div>

                    <div formArrayName="siblings">
                      <div *ngFor="let sibling of siblings.controls; let i = index" [formGroupName]="i" class="sibling-row">
                        <div class="sibling-header">
                          <h3>Sibling #{{ i + 1 }}</h3>
                          <button mat-icon-button color="warn" type="button" (click)="removeSibling(i)">
                            <mat-icon>delete</mat-icon>
                          </button>
                        </div>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Name</mat-label>
                            <input matInput formControlName="name" required placeholder="Enter sibling's name">
                            <mat-icon matSuffix>person</mat-icon>
                            <mat-error *ngIf="getSiblingControl(i, 'name')?.hasError('required')">
                              Name is required
                            </mat-error>
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Date of Birth</mat-label>
                            <input matInput [matDatepicker]="siblingDob" formControlName="date_of_birth" required>
                            <mat-datepicker-toggle matSuffix [for]="siblingDob"></mat-datepicker-toggle>
                            <mat-datepicker #siblingDob></mat-datepicker>
                            <mat-error *ngIf="getSiblingControl(i, 'date_of_birth')?.hasError('required')">
                              Date of birth is required
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Gender</mat-label>
                            <mat-select formControlName="gender">
                              <mat-option value="Male">Male</mat-option>
                              <mat-option value="Female">Female</mat-option>
                              <mat-option value="Other">Other</mat-option>
                            </mat-select>
                            <mat-icon matSuffix>wc</mat-icon>
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Occupation</mat-label>
                            <input matInput formControlName="occupation" placeholder="Enter sibling's occupation">
                            <mat-icon matSuffix>work</mat-icon>
                          </mat-form-field>
                        </div>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Marital Status</mat-label>
                            <mat-select formControlName="marital_status">
                              <mat-option value="Single">Single</mat-option>
                              <mat-option value="Married">Married</mat-option>
                              <mat-option value="Divorced">Divorced</mat-option>
                              <mat-option value="Widowed">Widowed</mat-option>
                            </mat-select>
                            <mat-icon matSuffix>family_restroom</mat-icon>
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Contact</mat-label>
                            <input matInput formControlName="contact" placeholder="Enter sibling's contact">
                            <mat-icon matSuffix>phone</mat-icon>
                            <mat-error *ngIf="getSiblingControl(i, 'contact')?.hasError('pattern')">
                              Please enter a valid 10-digit number
                            </mat-error>
                          </mat-form-field>
                        </div>
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Status</mat-label>
                            <mat-select formControlName="status">
                              <mat-option value="Living">Living</mat-option>
                              <mat-option value="Deceased">Deceased</mat-option>
                            </mat-select>
                            <mat-icon matSuffix>info</mat-icon>
                          </mat-form-field>
                          
                          <div class="checkbox-field">
                            <mat-checkbox formControlName="is_emergency_contact">Emergency Contact</mat-checkbox>
                          </div>
                        </div>
                        <div class="form-row">
                          <mat-form-field appearance="outline" class="full-width">
                            <mat-label>Additional Information</mat-label>
                            <textarea matInput formControlName="additional_info" rows="2" placeholder="Any additional information about the sibling"></textarea>
                            <mat-icon matSuffix>notes</mat-icon>
                          </mat-form-field>
                        </div>
                      </div>
                    </div>

                    <div *ngIf="siblings.controls.length === 0" class="empty-siblings">
                      <p>No siblings added. Click the + button to add siblings.</p>
                    </div>
                  </div>

                  <div class="step-actions">
                    <button mat-button matStepperPrevious>
                      <mat-icon>arrow_back</mat-icon>
                      <span>Back</span>
                    </button>
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Next</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Documents Step -->
          <mat-step [stepControl]="documentsForm" label="Documents">
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>description</mat-icon>
                  Upload Documents
                </h2>
                <form [formGroup]="documentsForm">
                  <!-- Hidden control for resume validation -->
                  <input type="hidden" formControlName="resumeUploaded">
                  <div class="form-section">
                    <h3>Resume</h3>
                    <div class="form-row">
                      <div class="file-upload-container">
                        <div class="file-input-wrapper">
                          <button mat-raised-button color="primary" type="button" (click)="resumeInput.click()">
                            <mat-icon>upload_file</mat-icon> Upload Resume
                          </button>
                          <input #resumeInput type="file" (change)="onResumeFileSelected($event)" accept=".pdf,.doc,.docx" hidden>
                          <span class="file-name">{{ resumeFileName || 'No file chosen' }}</span>
                        </div>
                        <div class="file-requirements">
                          <mat-icon color="primary">info</mat-icon>
                          <span>Accepted formats: PDF, DOC, DOCX | Maximum size: 5MB</span>
                        </div>
                        <div *ngIf="!resumeFile && documentsForm.get('resumeUploaded')?.touched" class="file-error">
                          <mat-icon color="warn">error</mat-icon>
                          <span>Resume is required</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-section">
                    <h3>Profile Picture</h3>
                    <div class="form-row">
                      <div class="file-upload-container">
                        <div class="file-input-wrapper">
                          <button mat-raised-button color="primary" type="button" (click)="profilePictureInput.click()">
                            <mat-icon>add_photo_alternate</mat-icon> Upload Photo
                          </button>
                          <input #profilePictureInput type="file" (change)="onProfilePictureSelected($event)" accept=".jpg,.jpeg,.png,.gif" hidden>
                          <span class="file-name">{{ profilePictureFileName || 'No file chosen' }}</span>
                        </div>
                        <div class="file-requirements">
                          <mat-icon color="primary">info</mat-icon>
                          <span>Accepted formats: JPG, PNG, GIF | Maximum size: 3MB</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="document-note">
                    <mat-icon color="warn">warning</mat-icon>
                    <p>Please ensure all uploaded documents are clear and legible. Your resume should include your complete work history and educational background.</p>
                  </div>

                  <div class="step-actions">
                    <button mat-button matStepperPrevious>
                      <mat-icon>arrow_back</mat-icon>
                      <span>Back</span>
                    </button>
                    <button mat-raised-button matStepperNext color="primary">
                      <span>Review</span>
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </mat-step>

          <!-- Review Step -->
          <mat-step label="Review & Submit" optional>
            <div class="step-content">
              <div class="form-section">
                <h2 class="section-title">
                  <mat-icon>fact_check</mat-icon>
                  Review & Submit
                </h2>

                <div class="review-section">
                  <h3>Review Your Information</h3>
                  <p>Please review all the information you've provided before submitting your application.</p>

                  <div class="review-summary">
                    <div class="review-category">
                      <h4><mat-icon>person</mat-icon> Personal Information</h4>
                      <p>Make sure your personal details are correct, including your name, date of birth, and gender.</p>
                    </div>

                    <div class="review-category">
                      <h4><mat-icon>contact_phone</mat-icon> Contact Information</h4>
                      <p>Verify your email address and phone number are accurate, as we will use these to contact you.</p>
                    </div>

                    <div class="review-category">
                      <h4><mat-icon>school</mat-icon> Education</h4>
                      <p>Confirm your educational qualifications, degrees, and institutions are correctly listed.</p>
                    </div>

                    <div class="review-category">
                      <h4><mat-icon>work</mat-icon> Experience</h4>
                      <p>Review your work experience, current position, and salary expectations.</p>
                    </div>

                    <div class="review-category">
                      <h4><mat-icon>description</mat-icon> Documents</h4>
                      <p>Ensure you have uploaded all required documents, especially your resume.</p>
                    </div>
                  </div>

                  <div class="review-note">
                    <mat-icon color="primary">info</mat-icon>
                    <span>By submitting this form, you confirm that all the information provided is accurate and complete.</span>
                  </div>

                  <div class="consent-section">
                    <h3>Consent</h3>
                    <p>I hereby consent to the processing of my personal data for recruitment purposes. I understand that my information will be stored in the company's database and used solely for the purpose of evaluating my candidacy for current and future positions.</p>
                  </div>
                </div>

                <div class="step-actions">
                  <button mat-button matStepperPrevious>
                    <mat-icon>arrow_back</mat-icon>
                    <span>Back</span>
                  </button>
                  <button mat-raised-button color="primary" (click)="submitRegistration()" [disabled]="isSubmitting">
                    <mat-icon>send</mat-icon>
                    <span *ngIf="!isSubmitting">Submit Application</span>
                    <span *ngIf="isSubmitting">
                      <mat-spinner diameter="20" style="display: inline-block; margin-right: 8px;"></mat-spinner>
                      Submitting...
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </mat-step>
        </mat-horizontal-stepper>
      </div>
    </mat-card-content>
  </mat-card>
</div>