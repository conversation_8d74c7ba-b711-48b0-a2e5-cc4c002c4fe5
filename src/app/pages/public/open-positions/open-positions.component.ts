import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { PublicApiService } from '../../../services/public-api.service';

@Component({
  selector: 'app-open-positions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule
  ],
  templateUrl: './open-positions.component.html',
  styleUrls: ['./open-positions.component.scss']
})
export class OpenPositionsComponent implements OnInit {
  positions: any[] = [];
  isLoading: boolean = true;
  errorMessage: string = '';
  displayedColumns: string[] = ['title', 'department', 'skills', 'vacancies', 'status'];

  constructor(private publicApiService: PublicApiService) { }

  ngOnInit(): void {
    this.loadOpenPositions();
  }

  loadOpenPositions(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.publicApiService.getOpenPositions().subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.positions = response.positions || [];
        } else {
          this.errorMessage = response.message || 'Failed to load open positions';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.error?.message || 'Error loading positions. Please try again.';
        console.error('Error loading open positions:', error);
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Open':
        return 'status-open';
      case 'Filled':
        return 'status-filled';
      case 'On Hold':
        return 'status-hold';
      default:
        return '';
    }
  }
}
