<div class="open-positions-container">
  <mat-card class="positions-card">
    <mat-card-header>
      <mat-card-title>
        <div class="title-container">
          <img src="assets/images/logo.png" alt="Company Logo" class="company-logo">
          <h1>Open Positions</h1>
        </div>
      </mat-card-title>
      <mat-card-subtitle>
        Explore current job opportunities at our company
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading open positions...</p>
      </div>

      <div *ngIf="!isLoading && errorMessage" class="error-container">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ errorMessage }}</p>
        <button mat-button color="primary" (click)="loadOpenPositions()">Retry</button>
      </div>

      <div *ngIf="!isLoading && !errorMessage && positions.length === 0" class="empty-container">
        <mat-icon color="primary">info</mat-icon>
        <p>No open positions available at the moment. Please check back later.</p>
      </div>

      <div *ngIf="!isLoading && !errorMessage && positions.length > 0" class="positions-table-container">
        <table mat-table [dataSource]="positions" class="positions-table">
          <!-- Title Column -->
          <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef>Position</th>
            <td mat-cell *matCellDef="let position">{{ position.title }}</td>
          </ng-container>

          <!-- Department Column -->
          <ng-container matColumnDef="department">
            <th mat-header-cell *matHeaderCellDef>Department</th>
            <td mat-cell *matCellDef="let position">{{ position.department_name }}</td>
          </ng-container>

          <!-- Skills Column -->
          <ng-container matColumnDef="skills">
            <th mat-header-cell *matHeaderCellDef>Required Skills</th>
            <td mat-cell *matCellDef="let position">
              <div class="skills-container">
                <ng-container *ngFor="let skill of position.required_skills.split(',')">
                  <mat-chip>{{ skill.trim() }}</mat-chip>
                </ng-container>
              </div>
            </td>
          </ng-container>

          <!-- Vacancies Column -->
          <ng-container matColumnDef="vacancies">
            <th mat-header-cell *matHeaderCellDef>Vacancies</th>
            <td mat-cell *matCellDef="let position">{{ position.vacancies }}</td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let position">
              <span class="status-chip" [ngClass]="getStatusClass(position.status)">
                {{ position.status }}
              </span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>

    <mat-card-footer>
      <div class="footer-content">
        <p>To apply for a position, you need a registration token. Please contact our HR department for more information.</p>
        <p>If you already have a registration token, please use the link provided in your email.</p>
      </div>
    </mat-card-footer>
  </mat-card>
</div>
