.open-positions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.positions-card {
  width: 100%;
  max-width: 900px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.company-logo {
  height: 40px;
  width: auto;
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 0;
}

.positions-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

.positions-table {
  width: 100%;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.status-chip {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-open {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-filled {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-hold {
  background-color: #fff8e1;
  color: #f57f17;
}

.footer-content {
  padding: 16px;
  background-color: #f9f9f9;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

mat-card-subtitle {
  margin-top: 0;
}
