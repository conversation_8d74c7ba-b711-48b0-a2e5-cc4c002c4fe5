.success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.success-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.company-logo {
  height: 40px;
  width: auto;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 64px;
  height: 64px;
  width: 64px;
  margin-bottom: 24px;
}

.success-icon mat-icon {
  font-size: 64px;
  height: 64px;
  width: 64px;
}

.info-box {
  width: 100%;
  text-align: left;
  margin: 20px 0;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #3f51b5;
}

.info-box ol {
  margin-top: 8px;
  padding-left: 20px;
}

.info-box li {
  margin-bottom: 8px;
}

mat-card-actions {
  padding: 16px;
}

h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

h2 {
  margin-top: 0;
  font-size: 20px;
  font-weight: 500;
  color: #3f51b5;
}

h3 {
  margin-top: 0;
  font-size: 18px;
  font-weight: 500;
}

p {
  margin: 8px 0;
  color: rgba(0, 0, 0, 0.7);
}

.center-button {
  display: flex;
  justify-content: center;
  width: 100%;
}
