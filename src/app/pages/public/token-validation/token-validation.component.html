<div class="token-validation-container">
  <mat-card class="validation-card">
    <mat-card-header>
      <mat-card-title>
        <div class="title-container">
          <img src="assets/images/logo.png" alt="Company Logo" class="company-logo">
          <h1>Candidate Registration</h1>
        </div>
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Validating registration link...</p>
      </div>

      <div *ngIf="!isLoading && isValid" class="success-container">
        <div class="success-icon">
          <mat-icon color="primary">check_circle</mat-icon>
        </div>
        <h2>Valid Registration Link</h2>
        <p>Your registration link is valid. You can now proceed with your application.</p>
        
        <div *ngIf="positionData" class="position-info">
          <h3>Position Details</h3>
          <p><strong>Title:</strong> {{ positionData.title }}</p>
          <p><strong>Department:</strong> {{ positionData.department_name }}</p>
          <p><strong>Required Skills:</strong> {{ positionData.required_skills }}</p>
          <p><strong>Required Qualifications:</strong> {{ positionData.required_qualifications }}</p>
        </div>
      </div>

      <div *ngIf="!isLoading && !isValid" class="error-container">
        <div class="error-icon">
          <mat-icon color="warn">error</mat-icon>
        </div>
        <h2>Invalid Registration Link</h2>
        <p>{{ errorMessage }}</p>
        <p>This link may have expired or already been used. Please contact the HR department for assistance.</p>
      </div>
    </mat-card-content>

    <mat-card-actions align="end">
      <button *ngIf="!isLoading && !isValid" mat-raised-button color="primary" (click)="viewOpenPositions()">
        View Open Positions
      </button>
      <button *ngIf="!isLoading && isValid" mat-raised-button color="primary" (click)="proceedToRegistration()">
        Proceed to Registration
      </button>
    </mat-card-actions>
  </mat-card>
</div>
