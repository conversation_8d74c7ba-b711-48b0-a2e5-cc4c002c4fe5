import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { PublicApiService } from '../../../services/public-api.service';

@Component({
  selector: 'app-token-validation',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule
  ],
  templateUrl: './token-validation.component.html',
  styleUrls: ['./token-validation.component.scss']
})
export class TokenValidationComponent implements OnInit {
  token: string = '';
  isLoading: boolean = true;
  isValid: boolean = false;
  errorMessage: string = '';
  tokenData: any = null;
  positionData: any = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private publicApiService: PublicApiService
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.token = params['token'];
      this.validateToken();
    });
  }

  validateToken(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.publicApiService.validateToken(this.token).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.isValid = true;
          this.tokenData = response.token;
          this.positionData = response.position;
        } else {
          this.isValid = false;
          this.errorMessage = response.message || 'Invalid registration token';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.isValid = false;
        this.errorMessage = error.error?.message || 'Error validating token. Please try again.';
        console.error('Token validation error:', error);
      }
    });
  }

  proceedToRegistration(): void {
    this.router.navigate(['/register/candidate', this.token]);
  }

  viewOpenPositions(): void {
    this.router.navigate(['/register/positions']);
  }
}
