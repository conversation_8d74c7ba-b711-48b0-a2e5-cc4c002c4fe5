.token-validation-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.validation-card {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.company-logo {
  height: 40px;
  width: auto;
}

.loading-container, .success-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

.success-icon, .error-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

.success-icon mat-icon, .error-icon mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
}

.position-info {
  width: 100%;
  text-align: left;
  margin-top: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #3f51b5;
}

mat-card-actions {
  padding: 16px;
}

h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

h2 {
  margin-top: 0;
  font-size: 20px;
  font-weight: 500;
}

h3 {
  margin-top: 0;
  font-size: 18px;
  font-weight: 500;
}

p {
  margin: 8px 0;
  color: rgba(0, 0, 0, 0.7);
}
