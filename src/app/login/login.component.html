<div class="login-container">
  <div class="login-card">
    <h1>Login</h1>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <mat-form-field appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput formControlName="identifier" type="email">
        <mat-error *ngIf="loginForm.get('identifier')?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="loginForm.get('identifier')?.hasError('email')">
          Invalid email
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Password</mat-label>
        <input matInput formControlName="password" type="password">
        <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
          Password is required
        </mat-error>
      </mat-form-field>

      <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid">Login</button>
    </form>
    
    <div *ngIf="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</div>