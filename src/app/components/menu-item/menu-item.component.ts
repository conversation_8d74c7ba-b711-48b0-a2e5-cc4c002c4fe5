import { animate, style, transition, trigger } from '@angular/animations';
import { Component, computed, input, signal } from '@angular/core';
import { MenuItem } from '../custom-sidenav/custom-sidenav.component';
import { MatIconModule } from '@angular/material/icon';
import { MatListItem, MatListModule } from '@angular/material/list';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-menu-item',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatListModule,
    MatListItem,
    RouterModule,
  ],
  animations: [
    trigger('expandContractMenu', [
      transition(':enter', [
        style({ opacity: 0, height: 0 }),
        animate('150ms ease-in-out', style({ opacity: 1, height: '*' })),
      ]),
      transition(':leave', [
        style({ opacity: 1, height: '*' }),
        animate('100ms ease-in-out', style({ opacity: 0, height: 0 })),
      ]),
    ]),
  ],
  templateUrl: './menu-item.component.html',
  styleUrl: './menu-item.component.scss',
})
export class MenuItemComponent {
  item = input.required<MenuItem>();
  collapsed = input(false);
  nestedMenuOpen = signal(false);

  constructor(private router: Router) {}

  toggleNested() {
    const currentItem = this.item();
    if (currentItem.action) {
      currentItem.action();
      return;
    }
    if (currentItem.subItems) {
      this.nestedMenuOpen.set(!this.nestedMenuOpen());
    }
  }

  handleClick() {
    const currentItem = this.item();
    if (currentItem.action) {
      currentItem.action();
      return;
    }
    if (currentItem.subItems) {
      this.toggleNested();
    }
  }
}
