<a mat-list-item 
   [routerLink]="item().action ? null : (item().subItems ? null : item().route)" 
   routerLinkActive 
   #rla="routerLinkActive" 
   [activated]="rla.isActive"
   (click)="handleClick()">
    <mat-icon 
      [fontSet]="rla.isActive ? 'material-icons' : 'material-icons-outlined'"
      matListItemIcon>
      {{item().icon}}
    </mat-icon>

    @if (!collapsed()) {
      <span matListItemTitle>{{item().label}}</span>
    }

    @if(item().subItems){
      <span matListItemMeta>
        @if (nestedMenuOpen()) {
          <mat-icon>expand_less</mat-icon>
        } @else {
          <mat-icon>expand_more</mat-icon>
        }
      </span>
    }
</a>

@if (item().subItems && nestedMenuOpen()) {
  <div @expandContractMenu>
    @for (subItem of item().subItems; track subItem.label) {
      <a mat-list-item 
         [routerLink]="subItem.route" 
         routerLinkActive 
         #rla="routerLinkActive">
        <mat-icon 
          [fontSet]="rla.isActive ? 'material-icons' : 'material-icons-outlined'"
          matListItemIcon>
          {{subItem.icon}}
        </mat-icon>

        @if (!collapsed()) {
          <span matListItemTitle>{{subItem.label}}</span>
        }
      </a>
    }
  </div>
}