import { Component, Input, computed, signal } from '@angular/core';
import { MatNavList } from '@angular/material/list';
import { MenuItemComponent } from '../menu-item/menu-item.component';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../services/auth-service.service';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';

export type MenuItem = {
  icon: string;
  label: string;
  route?: string;
  subItems?: MenuItem[];
  action?: () => void;
};

@Component({
  selector: 'app-custom-sidenav',
  standalone: true,
  imports: [MatNavList, MenuItemComponent, MatIconModule, RouterModule],
  templateUrl: './custom-sidenav.component.html',
  styleUrl: './custom-sidenav.component.scss',
})
export class CustomSidenavComponent {
  constructor(
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}
  sidenavcollapsed = signal<boolean>(false);

  @Input() set collapsed(val: boolean) {
    this.sidenavcollapsed.set(val);
  }

  profilepicsize = computed(() => (this.sidenavcollapsed() ? '32' : '100'));

  handleLogout() {
    this.authService.logout().subscribe({
      next: () => {
        this.snackBar.open('Logged out successfully', 'Close', {
          duration: 3000,
        });
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Still navigate to login even if server request fails
        this.snackBar.open('Logged out', 'Close', { duration: 3000 });
        this.router.navigate(['/login']);
      },
    });
  }

  menuItems = signal<MenuItem[]>([
    {
      icon: 'dashboard',
      label: 'Dashboard',
      route: 'dashboard',
    },
    {
      icon: 'settings',
      label: 'Masters',
      subItems: [
        {
          icon: 'location_on',
          label: 'States',
          route: 'master/state',
        },
        {
          icon: 'location_city',
          label: 'Cities',
          route: 'master/city',
        },
        {
          icon: 'store',
          label: 'Stores',
          route: 'master/stores',
        },
        {
          icon: 'church',
          label: 'Religion',
          route: 'master/religion',
        },
        {
          icon: 'bloodtype',
          label: 'Blood Groups',
          route: 'master/bloodgroup',
        },
        {
          icon: 'groups',
          label: 'Communities',
          route: 'master/community',
        },
        {
          icon: 'business',
          label: 'Departments',
          route: 'master/department',
        },
        {
          icon: 'work',
          label: 'Designations',
          route: 'master/designations',
        },
        {
          icon: 'badge',
          label: 'Employment Types',
          route: 'master/employmenttype',
        },
        {
          icon: 'wc',
          label: 'Genders',
          route: 'master/gender',
        },
      ],
    },
    {
      icon: 'people',
      label: 'Staff Management',
      subItems: [
        {
          icon: 'list',
          label: 'Staff List',
          route: 'staffs/list',
        },
        {
          icon: 'person_add',
          label: 'Add Staff',
          route: 'staffs/add',
        },
      ],
    },
    {
      icon: 'person_search',
      label: 'Interview',
      subItems: [
        {
          icon: 'dashboard',
          label: 'Dashboard',
          route: 'interview/dashboard',
        },
        {
          icon: 'work',
          label: 'Positions',
          route: 'interview/positions',
        },
        {
          icon: 'people',
          label: 'Candidates',
          route: 'interview/candidates',
        },
        {
          icon: 'event_note',
          label: 'Sessions',
          route: 'interview/sessions',
        },
        {
          icon: 'vpn_key',
          label: 'Registration Tokens',
          route: 'interview/registration-tokens',
        },
      ],
    },
    {
      icon: 'admin_panel_settings',
      label: 'Administration',
      subItems: [
        {
          icon: 'people',
          label: 'Users',
          route: 'users/list',
        },
        {
          icon: 'security',
          label: 'Roles',
          route: 'roles/list',
        },
        {
          icon: 'extension',
          label: 'Modules',
          route: 'modules/list',
        },
      ],
    },
    {
      icon: 'logout',
      label: 'Logout',
      action: () => {
        console.log('Logout clicked');
        this.handleLogout();
      },
    },
  ]);
}
