.sidenav-header {
    background: #3f51b5;
    color: white;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 20px 0px;

    * {
        margin: 0px;
        padding: 0px;
    }



    p {
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        flex-direction: column;
        align-items: center;

        span {
            font-size: 0.8rem;
            font-weight: 400;
        }
    }

    img {
        border-radius: 100px;
        border: 3px solid #fff;
    }


}

:host * {
    transition: all 500ms ease-in-out;
}

.sidenav-header-title {
    height: 3rem;
}

.hide-header-text {
    opacity: 0;
    height: 0px !important;
}