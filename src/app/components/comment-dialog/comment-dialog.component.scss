/* src/app/components/comment-dialog/comment-dialog.component.scss */

.comment-dialog {
    .close-button {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  
    .error-message {
      background-color: #ffebee;
      color: #b71c1c;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 16px;
    }
  
    .full-width {
      width: 100%;
    }
  
    .existing-comments {
      margin-top: 20px;
  
      h3 {
        margin-bottom: 8px;
      }
  
      .comment-list {
        margin-top: 16px;
        max-height: 300px;
        overflow-y: auto;
  
        .comment-item {
          padding: 12px;
          border-radius: 4px;
          margin-bottom: 12px;
          background-color: #f5f5f5;
          border-left: 3px solid #e0e0e0;
          cursor: pointer;
          transition: background-color 0.2s ease;
  
          &:hover {
            background-color: #eeeeee;
          }
  
          &.unread {
            border-left-color: #1976d2;
            background-color: #e3f2fd;
          }
  
          .comment-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
  
            .comment-author {
              font-weight: 500;
              margin-right: 8px;
            }
  
            .comment-date {
              font-size: 12px;
              color: #666;
            }
  
            .unread-badge {
              margin-left: auto;
              background-color: #1976d2;
              color: white;
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 11px;
              font-weight: 500;
            }
          }
  
          .comment-content {
            white-space: pre-line;
            line-height: 1.5;
          }
        }
      }
    }
  
    .loading-indicator {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }
  }