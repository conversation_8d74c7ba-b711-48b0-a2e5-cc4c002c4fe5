<!-- src/app/components/comment-dialog/comment-dialog.component.html -->

<div class="comment-dialog">
    <h2 mat-dialog-title>
      Comment on {{data.fieldLabel}}
      <button mat-icon-button aria-label="Close dialog" mat-dialog-close class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </h2>
  
    <mat-dialog-content class="dialog-content">
      <!-- Error message -->
      <div *ngIf="errorMessage" class="error-message">
        <mat-icon>error</mat-icon>
        {{errorMessage}}
      </div>
  
      <!-- New comment section -->
      <div class="new-comment-section">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Add Comment</mat-label>
          <textarea matInput [(ngModel)]="commentText" rows="4" placeholder="Enter your comment here..."></textarea>
          <mat-hint align="end">{{commentText.length}} characters</mat-hint>
        </mat-form-field>
      </div>
  
      <!-- Existing comments section -->
      <div *ngIf="existingComments.length > 0" class="existing-comments">
        <h3>Previous Comments <span class="comment-count">({{existingComments.length}})</span></h3>
        <mat-divider></mat-divider>
        
        <div class="comment-list">
          <div *ngFor="let comment of existingComments" 
               class="comment-item" 
               [ngClass]="{'unread': !comment.is_read}">
            <div class="comment-header">
              <span class="comment-author">{{comment.username}}</span>
              <span class="comment-date">{{formatDate(comment.created_at)}}</span>
              <button *ngIf="!comment.is_read" 
                     mat-icon-button 
                     color="primary"
                     class="mark-read-button"
                     matTooltip="Mark as read"
                     (click)="markAsRead(comment.id)">
                <mat-icon>visibility</mat-icon>
              </button>
            </div>
            <div class="comment-content">{{comment.comment_text}}</div>
          </div>
        </div>
      </div>
  
      <!-- No comments message -->
      <div *ngIf="!isLoading && existingComments.length === 0" class="no-comments">
        No previous comments found.
      </div>
  
      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="loading-indicator">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
    </mat-dialog-content>
  
    <mat-dialog-actions align="end">
      <button mat-button [disabled]="isLoading" (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || !commentText.trim()" (click)="saveComment()">
        Save Comment
      </button>
    </mat-dialog-actions>
  </div>