// src/app/components/comment-dialog/comment-dialog.component.ts

import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CandidateCommentService } from '../../services/candidate-comment.service';

interface Comment {
  id: number;
  candidate_id: number;
  field_identifier: string;
  comment_text: string;
  user_id: number;
  username: string;
  created_at: string;
  updated_at: string;
  is_read: boolean;
  status: string;
}

@Component({
  selector: 'app-comment-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
  ],
  templateUrl: './comment-dialog.component.html',
  styleUrls: ['./comment-dialog.component.scss'],
})
export class CommentDialogComponent implements OnInit {
  commentText: string = '';
  existingComments: Comment[] = [];
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(
    public dialogRef: MatDialogRef<CommentDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      candidateId: number;
      fieldIdentifier: string;
      fieldLabel: string;
    },
    private commentService: CandidateCommentService
  ) {}

  ngOnInit(): void {
    this.loadExistingComments();
  }

  loadExistingComments(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.commentService
      .getFieldComments(this.data.candidateId, this.data.fieldIdentifier)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.existingComments = response.comments;
            // Sort comments by created date (newest first)
            this.existingComments.sort(
              (a, b) =>
                new Date(b.created_at).getTime() -
                new Date(a.created_at).getTime()
            );
          } else {
            this.errorMessage = response.message || 'Unable to load comments';
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading comments:', error);
          this.errorMessage =
            'Error loading comments: ' + (error.message || 'Unknown error');
          this.isLoading = false;
        },
      });
  }

saveComment(): void {
  if (!this.commentText.trim()) {
    return;
  }

  this.isLoading = true;
  this.errorMessage = '';

  const commentData = {
    candidate_id: this.data.candidateId,
    field_identifier: this.data.fieldIdentifier,
    comment_text: this.commentText.trim(),
  };

  this.commentService.addOrUpdateComment(commentData).subscribe({
    next: (response) => {
      if (response.success) {
        // Instead of closing, clear the input and reload comments
        this.commentText = '';
        this.loadExistingComments();
      } else {
        this.errorMessage = response.message || 'Failed to save comment';
        this.isLoading = false;
      }
    },
    error: (error) => {
      console.error('Error saving comment:', error);
      this.errorMessage =
        'Error saving comment: ' + (error.message || 'Unknown error');
      this.isLoading = false;
    },
  });
}

  markAsRead(commentId: number): void {
    // Prevent the click from bubbling up to the parent element
    event?.stopPropagation();

    this.commentService.markCommentAsRead(commentId).subscribe({
      next: (response) => {
        if (response.success) {
          // Update local comment data
          this.existingComments = this.existingComments.map((comment) =>
            comment.id === commentId ? { ...comment, is_read: true } : comment
          );
        } else {
          console.error('Failed to mark comment as read:', response.message);
        }
      },
      error: (error) => {
        console.error('Error marking comment as read:', error);
      },
    });
  }

  cancel(): void {
    this.dialogRef.close(false);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // For comments less than 24 hours old, show relative time
    if (diffDays < 1) {
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      if (diffHours < 1) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return diffMinutes < 1 ? 'Just now' : `${diffMinutes} min ago`;
      }
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    }

    // For older comments, show the date
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }
}
