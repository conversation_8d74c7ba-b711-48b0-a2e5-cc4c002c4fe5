<div class="view-staff-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon>person</mat-icon>
      Staff Details
    </h2>
    <button mat-icon-button (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="profile-section">
    <div class="profile-image-container">
      <img
        *ngIf="data.profilePicture"
        [src]="'http://localhost:3000/uploads/' + data.profilePicture"
        alt="Profile Picture"
        class="profile-image"
      />
      <!-- Default image if no profile picture -->
      <div *ngIf="!data.profilePicture" class="default-profile">
        <mat-icon>person</mat-icon>
      </div>
    </div>
    <div class="profile-info">
      <h2>{{ data.firstName }} {{ data.lastName }}</h2>
      <p class="designation">{{ data.designation_name }}</p>
      <p class="employee-id">Employee ID: {{ data.employeeId }}</p>
    </div>
  </div>

  <mat-dialog-content>
    <mat-tab-group>
      <!-- Personal Information Tab -->
      <mat-tab label="Personal Info">
        <div class="info-section">
          <div class="info-grid">
            <div class="info-item">
              <label>Name</label>
              <p>{{ data.firstName }} {{ data.lastName }}</p>
            </div>
            <div class="info-item">
              <label>Employee ID</label>
              <p>{{ data.employeeId }}</p>
            </div>
            <div class="info-item">
              <label>Date of Birth</label>
              <p>
                {{ data.dateOfBirth | date }} (Age:
                {{ calculateAge(data.dateOfBirth) }} years)
              </p>
            </div>
            <div class="info-item">
              <label>Gender</label>
              <p>{{ data.gender_name }}</p>
            </div>
            <div class="info-item">
              <label>Email</label>
              <p>{{ data.email }}</p>
            </div>
            <div class="info-item">
              <label>Phone</label>
              <p>{{ data.phoneNumber }}</p>
            </div>
            <div class="info-item">
              <label>Emergency Contact</label>
              <p>
                {{ data.emergencyContactName }} ({{
                  data.emergencyContactNumber
                }})
              </p>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Employment Tab -->
      <mat-tab label="Employment">
        <div class="info-section">
          <div class="info-grid">
            <div class="info-item">
              <label>Department</label>
              <p>{{ data.department_name }}</p>
            </div>
            <div class="info-item">
              <label>Designation</label>
              <p>{{ data.designation_name }}</p>
            </div>
            <div class="info-item">
              <label>Employment Type</label>
              <p>{{ data.employment_type_name }}</p>
            </div>
            <div class="info-item">
              <label>Hire Date</label>
              <p>{{ data.hireDate | date }}</p>
            </div>
            <div class="info-item">
              <label>Status</label>
              <p [class]="data.isActive ? 'status-active' : 'status-inactive'">
                {{ data.employmentStatus }}
              </p>
            </div>
            <div class="info-item">
              <label>Education</label>
              <p>{{ data.educationLevel }}</p>
            </div>
            <div class="info-item">
              <label>Salary</label>
              <p>{{ data.salary | currency : "INR" }}</p>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Family Information Tab -->
      <mat-tab label="Family">
        <div class="info-section">
          <h3>Parents</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>Father's Name</label>
              <p>{{ data.father_name || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Father's Occupation</label>
              <p>{{ data.father_occupation || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Father's Contact</label>
              <p>{{ data.father_contact || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Father's Status</label>
              <p>{{ data.father_status }}</p>
            </div>
            <div class="info-item">
              <label>Mother's Name</label>
              <p>{{ data.mother_name || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Mother's Occupation</label>
              <p>{{ data.mother_occupation || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Mother's Contact</label>
              <p>{{ data.mother_contact || "N/A" }}</p>
            </div>
            <div class="info-item">
              <label>Mother's Status</label>
              <p>{{ data.mother_status }}</p>
            </div>
          </div>

          <h3>Siblings</h3>
          <div
            class="siblings-table-container"
            *ngIf="data.siblings?.length > 0"
          >
            <table
              mat-table
              [dataSource]="data.siblings"
              class="siblings-table"
            >
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let sibling">{{ sibling.name }}</td>
              </ng-container>

              <!-- Age/DOB Column -->
              <ng-container matColumnDef="date_of_birth">
                <th mat-header-cell *matHeaderCellDef>Date of Birth</th>
                <td mat-cell *matCellDef="let sibling">
                  {{ sibling.date_of_birth | date }}
                </td>
              </ng-container>

              <!-- Gender Column -->
              <ng-container matColumnDef="gender">
                <th mat-header-cell *matHeaderCellDef>Gender</th>
                <td mat-cell *matCellDef="let sibling">{{ sibling.gender }}</td>
              </ng-container>

              <!-- Occupation Column -->
              <ng-container matColumnDef="occupation">
                <th mat-header-cell *matHeaderCellDef>Occupation</th>
                <td mat-cell *matCellDef="let sibling">
                  {{ sibling.occupation }}
                </td>
              </ng-container>

              <!-- Marital Status Column -->
              <ng-container matColumnDef="marital_status">
                <th mat-header-cell *matHeaderCellDef>Marital Status</th>
                <td mat-cell *matCellDef="let sibling">
                  {{ sibling.marital_status }}
                </td>
              </ng-container>

              <!-- Contact Column -->
              <ng-container matColumnDef="contact">
                <th mat-header-cell *matHeaderCellDef>Contact</th>
                <td mat-cell *matCellDef="let sibling">
                  {{ sibling.contact }}
                </td>
              </ng-container>

              <!-- Emergency Contact Column -->
              <ng-container matColumnDef="is_emergency_contact">
                <th mat-header-cell *matHeaderCellDef>Emergency Contact</th>
                <td mat-cell *matCellDef="let sibling">
                  <mat-icon
                    *ngIf="sibling.is_emergency_contact"
                    class="emergency-icon"
                    [matTooltip]="'Emergency Contact'"
                  >
                    emergency
                  </mat-icon>
                </td>
              </ng-container>

              <tr
                mat-header-row
                *matHeaderRowDef="displayedSiblingColumns"
              ></tr>
              <tr
                mat-row
                *matRowDef="let row; columns: displayedSiblingColumns"
              ></tr>
            </table>
          </div>
          <p *ngIf="!data.siblings?.length" class="no-siblings">
            No siblings recorded
          </p>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-dialog-content>
</div>
