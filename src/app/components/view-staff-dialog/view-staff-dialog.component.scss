.view-staff-dialog {
    min-width: 800px;
    max-width: 1000px;
    
    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: #f5f5f5;
      margin: -24px -24px 0;
      
      h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        
        mat-icon {
          color: #1976d2;
        }
      }
    }
  
    mat-dialog-content {
      padding-top: 24px;
    }
  
    .info-section {
      padding: 16px;
  
      h3 {
        color: #1976d2;
        margin: 24px 0 16px;
      }
    }
  
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
    }
  
    .info-item {
      label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 4px;
        display: block;
      }
  
      p {
        margin: 0;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.87);

        .age {
          color: #666;
          font-size: 0.9em;
          margin-left: 4px;
        }

        
      }
    }
  
    .status-active {
      color: #4caf50;
      font-weight: 500;
    }
  
    .status-inactive {
      color: #f44336;
      font-weight: 500;
    }
  
    .siblings-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }
  
    .sibling-card {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
  
      h4 {
        margin: 0 0 8px;
        color: #1976d2;
      }
  
      .sibling-info {
        p {
          margin: 4px 0;
          font-size: 14px;
        }
  
        .emergency-contact {
          color: #f44336;
          font-weight: 500;
        }
      }
    }
  
    .no-siblings {
      color: rgba(0, 0, 0, 0.6);
      font-style: italic;
    }

    // view-staff-dialog.component.scss
.siblings-table-container {
    margin: 16px 0;
    overflow-x: auto;
  
    .siblings-table {
      width: 100%;
      
      th.mat-header-cell {
        background-color: #f5f5f5;
        color: rgba(0, 0, 0, 0.87);
        font-weight: 500;
        padding: 12px 16px;
      }
  
      td.mat-cell {
        padding: 12px 16px;
      }
  
      tr.mat-row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
  
        &:hover {
          background-color: #f5f5f5;
        }
      }
  
      .emergency-icon {
        color: #f44336;
        font-size: 20px;
        height: 20px;
        width: 20px;
      }
    }
  }
  
  .no-siblings {
    text-align: center;
    padding: 20px;
    color: rgba(0, 0, 0, 0.54);
    font-style: italic;
  }

  // view-staff-dialog.component.scss
.profile-section {
    display: flex;
    align-items: center;
    padding: 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  
    .profile-image-container {
      width: 120px;
      height: 120px;
      margin-right: 24px;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
      .profile-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
  
      .default-profile {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e9ecef;
  
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          color: #adb5bd;
        }
      }
    }
  
    .profile-info {
      h2 {
        margin: 0;
        font-size: 24px;
        color: #2196F3;
      }
  
      .designation {
        margin: 4px 0;
        font-size: 16px;
        color: #6c757d;
      }
  
      .employee-id {
        margin: 4px 0;
        font-size: 14px;
        color: #495057;
      }
    }
  }
  
  // Update dialog header to accommodate profile section
  .dialog-header {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
  }




  


  }